"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

export default function ProtectedRoute({ children }) {
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const token = typeof window !== "undefined" ? localStorage.getItem("EloopeToken") : null;
    if (!token) {
      router.replace("/"); // Redirect to login route
      setIsAuthenticated(false);
    } else {
      setIsAuthenticated(true);
    }
    setIsChecking(false);
  }, [router]);

  if (isChecking) {
    // Optionally, return a spinner here
    return null;
  }

  if (!isAuthenticated) {
    return null;
  }

  return children;
}