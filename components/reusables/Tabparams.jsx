
"use client"

import { useSearchPara<PERSON>, useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'

const Tabparams = ({defaultTab}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const initialTab = searchParams.get('tab') || defaultTab;
  const [switchInfo, setSwitchInfo] = useState(initialTab);

  const handleSwitch = (type) => {
    setSwitchInfo(type)
    router.push(`?tab=${type}`)
  }

  useEffect(() => {
    const currentTab = searchParams.get('tab')
    if (currentTab) {
      setSwitchInfo(currentTab)
    }
  }, [searchParams])

  return { handleSwitch, switchInfo}
}

export default Tabparams;