"use client"
import React, { useEffect, useState } from 'react'
import { Sidebar } from "@/constants/Sidebar"
import SidebarIcons from "@/constants/SidebarIcons"
import { ChevronDown, ChevronRight, HelpCircle, LayoutDashboardIcon, Menu, MoreHorizontal, Plus, Settings, UserPlus } from 'lucide-react'
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Role } from "@/utils/roles/Role"
import { useQuery } from "@tanstack/react-query"
import { getProfileApi } from "@/apis/profile-management"
import Image from 'next/image'

export const MSidebar = () => {
  const pathname = usePathname();
  const [extendedToolsOpen, setExtendedToolsOpen] = useState(true);
  const { isAdministrator, isApproverManager, isSystemAdmin } = Role();

  const { data: myProfile } = useQuery({
    queryKey: ["profile-settings"],
    queryFn: getProfileApi,
  });

  // Helper function to check if a path is active
  const isPathActive = (itemPath) => {
    // Handle root paths
    if (itemPath === "/") {
      return pathname === "/";
    }
    // Check if current pathname starts with the item path
    return pathname.startsWith(itemPath);
  };

  // Filter sidebar items based on user role
  const mainMenuItems = Sidebar.filter((item) => {
    // If it's the Approvals menu and user is not manager/admin, hide it
    if (item.path === "/approvals" && !isApproverManager && !isAdministrator) {
      return false;
    }
    // Only include main menu items (not extended tools or bottom items)
    return !item.isExtendedTool && !item.isBottomItem;
  });

  // Extended tools items
  const extendedToolsItems = Sidebar.filter((item) => {
    // If it's the Companies menu and user is not system admin, hide it
    if (item.path === "/companies" && !isSystemAdmin) {
      return false;
    }
    return item.isExtendedTool;
  });

  // Bottom menu items
  const bottomMenuItems = Sidebar.filter(item => item.isBottomItem);

  return (
    <>
      <Sheet>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="shrink-0 md:hidden"
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 flex flex-col">
          <div className="flex flex-col h-full bg-white dark:bg-background">
            {/* Header with logo */}
            <div className="flex h-14 items-center border-b px-4">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 font-semibold"
              >
                <LayoutDashboardIcon className="h-6 w-6" />
                <span className="">Eloope</span>
              </Link>
            </div>

            {/* Quick Create Button */}
            <div className="px-4 py-3">
              <button className="flex items-center gap-2 bg-black text-white rounded-md py-2 px-3 w-full justify-center hover:bg-gray-800 transition-colors">
                <Plus size={16} />
                <span className="text-sm font-medium">Quick Create</span>
              </button>
            </div>

            {/* Main Menu Items */}
            <div className="flex-1 overflow-y-auto">
              <nav className="px-2 space-y-1">
                {mainMenuItems.map((item, index) => (
                  <Link
                    key={index}
                    href={item.path}
                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm ${isPathActive(item.path)
                        ? "bg-gray-100 dark:bg-gray-800 text-black dark:text-white font-medium"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                      }`}
                  >
                    <SidebarIcons
                      as={item.name}
                      size={18}
                      strokeWidth={1.5}
                      color={isPathActive(item.path) ? "currentColor" : "#71717A"}
                    />
                    <span>{item.name}</span>
                  </Link>
                ))}
              </nav>

              {/* Extended Tools Section */}
              {extendedToolsItems.length > 0 && (
                <div className="mt-6 px-3">
                  <div
                    className="flex items-center justify-between mb-1 cursor-pointer"
                    onClick={() => setExtendedToolsOpen(!extendedToolsOpen)}
                  >
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Extended Tools</span>
                    {extendedToolsOpen ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                  </div>

                  {extendedToolsOpen && (
                    <nav className="space-y-1">
                      {extendedToolsItems.map((item, index) => (
                        <Link
                          key={index}
                          href={item.path}
                          className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm ${isPathActive(item.path)
                              ? "bg-gray-100 dark:bg-gray-800 text-black dark:text-white font-medium"
                              : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                            }`}
                        >
                          <SidebarIcons
                            as={item.name}
                            size={18}
                            strokeWidth={1.5}
                            color={isPathActive(item.path) ? "currentColor" : "#71717A"}
                          />
                          <span>{item.name}</span>
                        </Link>
                      ))}
                    </nav>
                  )}
                </div>
              )}
            </div>

            {/* Bottom Menu Items */}
            <div className="mt-auto">
              <nav className="px-2 space-y-1">
                {bottomMenuItems.map((item, index) => (
                  <Link
                    key={index}
                    href={item.path}
                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm ${isPathActive(item.path)
                        ? "bg-gray-100 dark:bg-gray-800 text-black dark:text-white font-medium"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                      }`}
                  >
                    {item.icon || (
                      <SidebarIcons
                        as={item.name}
                        size={18}
                        strokeWidth={1.5}
                        color={isPathActive(item.path) ? "currentColor" : "#71717A"}
                      />
                    )}
                    <span>{item.name}</span>
                  </Link>
                ))}
              </nav>

              {/* User Profile */}
              <div className="mt-4 px-3 py-3 border-t">
                <div className="flex items-center gap-2">
                  <div className="flex-shrink-0">
                    {myProfile?.data?.profile?.profile_picture ? (
                      <Image
                        src={myProfile?.data?.profile?.profile_picture}
                        alt="Profile"
                        className="h-8 w-8 rounded-md object-cover"
                        width={32}
                        height={32}
                        unoptimized={true}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-md bg-gray-300 flex items-center justify-center text-xs font-normal">
                        {myProfile?.data?.initials}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{myProfile?.data?.name}</p>
                    <p className="text-xs text-gray-500 truncate">Free</p>
                  </div>
                  <MoreHorizontal size={16} className="text-gray-500 cursor-pointer" />
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}
