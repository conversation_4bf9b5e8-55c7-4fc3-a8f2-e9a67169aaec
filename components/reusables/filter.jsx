import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { listOfReports } from "@/apis/expense-report";
import { choicesApi } from "@/apis/utilapi";

import { addDays, format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export const FilterComponent = ({ isOpen, onClose, className, onFilter }) => {
  const [open, setOpen] = useState(false)

  const [date, setDate] = useState({
    from: new Date(2025, 0, 3),
    to: addDays(new Date(2025, 1, 20), 20),
  })

  const [status, setStatus] = useState("");
  const [paymentType, setPaymentType] = useState("");
  const [reportType, setReportType] = useState("");

  const { data: choicesQuery } = useQuery({ queryKey: ["listOfChoices"], queryFn: choicesApi });

  useEffect(() => {
    onFilter({ date, status, paymentType, reportType });
  }, [date, status, paymentType, reportType, onFilter]);


  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="fixed top-4 right-0 w-[10rem] sm:w-[20rem] h-[30rem] overflow-y-auto shadow-lg z-50 border"
        style={{
          transform: "translateX(0)",
          right: "1rem",
          top: "1rem",
          left: "auto",
        }}
      >

        <div className="flex flex-col gap-4 mt-4">

          <div className={cn("grid gap-2", className)}>
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  id="date"
                  variant={"outline"}
                  className={cn(
                    "justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon />
                  {date?.from ? (
                    date.to ? (
                      <>
                        {format(date.from, "LLL dd, y")} -{" "}
                        {format(date.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(date.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={date?.from}
                  selected={date}
                  onSelect={() => {
                    setDate();
                    setOpen(false);
                  }}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </div>

          <Select onValueChange={(value) => setStatus(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              {choicesQuery?.data?.approval_status?.map((item) => (
                <SelectItem key={item?.value} value={item?.value}>
                  {item?.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => setPaymentType(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Payment Type" />
            </SelectTrigger>
            <SelectContent>
              {choicesQuery?.data?.payment_types?.map((item) => (
                <SelectItem key={item?.value} value={item?.value}>
                  {item?.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select onValueChange={(value) => setReportType(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              {choicesQuery?.data?.report_types?.map((item) => (
                <SelectItem key={item?.value} value={item?.value}>
                  {item?.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

        </div>

      </DialogContent>
    </Dialog>
  );
};
