"use client"

import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import React, { useState } from "react";
import { Input } from "../ui/input";
import { Loader2 } from "lucide-react"; // Add this import

export const SearchableSelect = ({
  value,
  onValueChange,
  searchPlaceholder = "Search...",
  placeholder,
  items,
  error,
  className,
  isLoading = false // Add loading prop
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  // Handle different data formats
  const getItemLabel = (item) => {
    if (typeof item === 'object') {
      // Check if item.name is a React element
      if (React.isValidElement(item.name)) {
        return item.label || ''; // Skip React elements for search filtering
      }
      return item.label || item.name || '';
    }
    return item;
  };

  const getItemValue = (item) => {
    if (typeof item === 'object') {
      return (item.value || item.id || item.code ||   '').toString();
    }
    return item;
  };

  const filteredItems = items?.filter((item) => {
    const label = getItemLabel(item);
    // Only apply toLowerCase if label is a string
    return typeof label === 'string' ?
      label.toLowerCase().includes(searchQuery?.toLowerCase()) :
      true; // Include items with non-string labels by default
  });

  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className={cn("text-xs", error && "validate_input", className)}> 
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="h-52 m-0">
        <div className="fixed p-1 z-10 top-0 right-0 left-0">
          <Input
            type="text"
            placeholder={searchPlaceholder}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="text-xs"
            disabled={isLoading}
          />
        </div>
        <div className="h-[40px]"></div>

        {isLoading ? (
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="ml-2 text-xs text-muted-foreground">Loading...</span>
          </div>
        ) : filteredItems?.length === 0 ? (
          <div className="text-xs text-center py-4 text-muted-foreground">
            No results found
          </div>
        ) : (
          filteredItems?.map((item, index) => (
            <SelectItem
              className="text-xs"
              key={getItemValue(`${item}-${index}`)}
              value={getItemValue(item)}
            >
              {getItemLabel(item)}
            </SelectItem>
          ))
        )}
      </SelectContent>
    </Select>
  );
};
