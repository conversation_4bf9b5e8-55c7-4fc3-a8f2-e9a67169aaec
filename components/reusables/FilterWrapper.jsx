"use client";

import React, { Suspense } from 'react';
import TableFilters from './TableFilters';
import { useSearchParams } from 'next/navigation';

const FilterContent = (props) => {
  useSearchParams();
  return <TableFilters {...props} />;
};

const FilterWrapper = (props) => {
  return (
    <Suspense fallback={<div className="w-full p-4 animate-pulse">Loading filters...</div>}>
      <FilterContent {...props} />
    </Suspense>
  );
};

export default FilterWrapper;
