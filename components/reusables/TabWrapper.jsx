"use client";

import React, { Suspense } from 'react';
import Tabaccross from './MyTabs';
import { useSearchParams } from 'next/navigation';

const TabContent = ({ tabs, defaultTab }) => {
  useSearchParams();
  return <Tabaccross tabs={tabs} defaultTab={defaultTab} />;
};

const TabWrapper = (props) => {
  return (
    <Suspense fallback={<div className="w-full p-2 animate-pulse">Loading tabs...</div>}>
      <TabContent {...props} />
    </Suspense>
  );
};

export default TabWrapper;