"use client";
import React, { useState } from "react";
import { Sidebar } from "@/constants/Sidebar";
import SidebarIcons from "@/constants/SidebarIcons";
import {
  FileText,
  ChevronDown,
  ChevronRight,
  CircleChevronRight,
  FileSliders,
  Plane,
  Plus,
  ChartNoAxesColumn,
  Wallet,
  UserRound,
  Bell,
  LogOut,
  ChevronLeft
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Role } from "@/utils/roles/Role";
import { useQuery } from "@tanstack/react-query";
import { getProfileApi } from "@/apis/profile-management";
import { MoreHorizontal } from "lucide-react";
import { Button } from "../ui/button";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { ExpenseModal } from "@/app/(dashboard)/expenses/components/Allexpense/ExpenseModals/ExpenseModal";
import { RecordAdvanceModal } from "@/app/(dashboard)/advances/components/RecordAdvanceModal";
import NewtripModal from "@/app/(dashboard)/trips/components/Newtrip/NewtripModal";
import Createreport from "@/app/(dashboard)/reports/components/createreport";

export const DSidebar = ({ collapsed, setCollapsed }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { isAdministrator, isApproverManager, isSystemAdmin } = Role();
  const [extendedToolsOpen, setExtendedToolsOpen] = useState(true);

  // Modal states
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
  const [isAdvanceModalOpen, setIsAdvanceModalOpen] = useState(false);
  const [isTripModalOpen, setIsTripModalOpen] = useState(false);

  const { data: myProfile } = useQuery({
    queryKey: ["profile-settings"],
    queryFn: getProfileApi,
  });


  const handleLogout = (e) => {
    e.preventDefault();
    if (typeof window !== "undefined") {
      localStorage.removeItem('Eloope_UserData');
      localStorage.removeItem('EloopeToken');
      localStorage.removeItem('lastPath')
      localStorage.removeItem('eloope_signup_data')
    }
    toast.success("You've successfully logged out");
    router.push("/");
  };


  // Helper function to check if a path is active
  const isPathActive = (itemPath) => {
    // Handle root paths
    if (itemPath === "/") {
      return pathname === "/";
    }
    // Check if current pathname starts with the item path
    return pathname.startsWith(itemPath);
  };

  // Quick create navigation handlers
  const handleCreateReport = () => {
    setIsReportModalOpen(true);
  };

  const handleCreateExpense = () => {
    setIsExpenseModalOpen(true);
  };

  const handleCreateAdvance = () => {
    setIsAdvanceModalOpen(true);
  };

  const handleCreateTrip = () => {
    setIsTripModalOpen(true);
  };

  // Filter sidebar items based on user role
  const mainMenuItems = Sidebar.filter((item) => {
    // Hide Approvals menu if user is not manager/admin
    if (item.path === "/approvals" && !isApproverManager && !isAdministrator) {
      return false;
    }
    // Only include main menu items
    return !item.isExtendedTool && !item.isBottomItem;
  });

  // Extended tools items
  const extendedToolsItems = Sidebar.filter((item) => {
    // If it's the Companies menu and user is not system admin, hide it
    if (item.path === "/companies" && !isSystemAdmin) {
      return false;
    }
    // If it's the Analytics menu and user is not administrator, hide it
    if (item.path === "/analytics" && !isAdministrator) {
      return false;
    }
    return item.isExtendedTool;
  });

  // Bottom menu items with Settings visibility control
  const bottomMenuItems = Sidebar.filter(item => {
    // Hide Settings menu if user is not administrator
    if (item.path === "/admin-settings" && !isAdministrator) {
      return false;
    }
    return item.isBottomItem;
  });

  return (
    <>
      <div
        className={`flex flex-col h-full ${collapsed ? "w-16 border-r" : "w-full px-4"
          } transition-all duration-300 dark:text-white bg-white dark:bg-background`}
      >
        {/* Quick Create Button with Dropdown */}
        <div className={`px-2 py-3 ${collapsed ? "px-1" : ""}`}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="flex items-center justify-between w-full py-5 bg-black hover:bg-gray-800 dark:bg-white">
                <span className="flex items-center gap-2">
                  <Plus size={12} />
                  {!collapsed && <span className="text-xs font-medium">Quick Create</span>}
                </span>
                {!collapsed && <CircleChevronRight size={12} />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="right"
              align="start"
              alignOffset={-5}
              sideOffset={10}
              className="w-[150px] p-2 bg-white dark:bg-gray-800 rounded-xl shadow-lg"
            >
              {[
                { name: "Report", icon: ChartNoAxesColumn, handler: handleCreateReport },
                { name: "Expense", icon: Wallet, handler: handleCreateExpense },
                { name: "Advance", icon: FileSliders, handler: handleCreateAdvance },
                { name: "Trip", icon: Plane, handler: handleCreateTrip }
              ].map((item, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={item.handler}
                  className="flex items-center gap-2 py-2 cursor-pointer"
                >
                  <item.icon size={16} />
                  <span>{item.name}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Main Menu Items */}
        <div className="flex-1 overflow-y-auto">
          <nav className={`px-2 space-y-1 ${collapsed ? "px-1" : ""}`}>
            {mainMenuItems.map((item, index) => (
              <Link
                key={index}
                href={item.path}
                className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-normal ${isPathActive(item.path)
                  ? "bg-gray-100 dark:bg-gray-800 text-[#3f3f46] dark:text-[#FFFFFF] font-normal"
                  : "text-gray-700 dark:text-[#FFFFFF] hover:bg-gray-50 dark:hover:bg-gray-800"
                  }`}
              >
                <SidebarIcons
                  as={item.name}
                  size={18}
                  strokeWidth={1.8}
                />
                {!collapsed && <span>{item.name}</span>}
              </Link>
            ))}
          </nav>

          {/* Extended Tools Section */}
          {extendedToolsItems.length > 0 && (
            <div className={`mt-6 ${collapsed ? "px-2" : ""}`}>
              {!collapsed && (
                <div
                  className="px-2 flex items-center justify-between mb-1 cursor-pointer"
                  onClick={() => setExtendedToolsOpen(!extendedToolsOpen)}
                >
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Extended Tools</span>
                  {extendedToolsOpen ? <ChevronDown size={18} /> : <ChevronRight size={18} />}
                </div>
              )}

              {extendedToolsOpen && (
                <nav className={`space-y-1 ${collapsed ? "px-1" : ""}`}>
                  {extendedToolsItems.map((item, index) => (
                    <Link
                      key={index}
                      href={item.path}
                      className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-normal ${isPathActive(item.path)
                        ? "bg-gray-100 dark:bg-gray-800 text-[#3f3f46] dark:text-white font-normal"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                        }`}
                    >
                      <SidebarIcons
                        as={item.name}
                        size={18}
                        strokeWidth={1.8}
                      // color={isPathActive(item.path) ? "currentColor" : "#71717A"}
                      // className={`${isPathActive(item.path) ? "text-gray-300 dark:text-white" : ".text-[#71717A]"}`}
                      />
                      {!collapsed && <span>{item.name}</span>}
                    </Link>
                  ))}
                </nav>
              )}
            </div>
          )}
        </div>

        {/* Bottom Menu Items */}
        <div className="mt-auto">
          <nav className={`px-2 space-y-1 ${collapsed ? "px-1" : ""}`}>
            {bottomMenuItems.map((item, index) => (
              <Link
                key={index}
                href={item.path}
                className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-normal ${isPathActive(item.path)
                  ? "bg-gray-100 dark:bg-gray-800 text-[#3f3f46] dark:text-white font-normal"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                  }`}
              >
                {item.icon || (
                  <SidebarIcons
                    as={item.name}
                    size={18}
                    strokeWidth={1.5}
                  // color={isPathActive(item.path) ? "currentColor" : "#71717A"}
                  // className={`${isPathActive(item.path) ? ".dark:text-white" : ""}`}

                  />
                )}
                {!collapsed && <span>{item.name}</span>}
              </Link>
            ))}
          </nav>

          {/* User Profile */}
          <div className={`px-3 py-3 border-t ${collapsed ? "px-2" : ""}`}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center gap-2 cursor-pointer">
                  <div className="flex-shrink-0">
                    {myProfile?.data?.profile?.profile_picture ? (
                      <Image
                        src={myProfile?.data?.profile?.profile_picture}
                        alt="Profile"
                        className="h-8 w-8 rounded-md object-cover"
                        width={32}
                        height={32}
                        unoptimized={true}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-md bg-gray-300 flex items-center justify-center text-xs font-normal">
                        {myProfile?.data?.initials}
                      </div>
                    )}
                  </div>
                  {!collapsed && (
                    <>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-normal truncate">{myProfile?.data?.name}</p>
                        <p className="text-xs truncate">Free</p>
                      </div>
                      <MoreHorizontal size={16} />
                    </>
                  )}
                </div>
              </DropdownMenuTrigger>

              <DropdownMenuContent
                side="right"
                align="start"
                alignOffset={-5}
                sideOffset={10}
                className="w-[250px] bg-white dark:bg-gray-800 rounded-xl shadow-lg"
              >
                <div className="flex items-center gap-2 border-b p-2">
                  <div className="flex-shrink-0">
                    {myProfile?.data?.profile?.profile_picture ? (
                      <Image
                        src={myProfile?.data?.profile?.profile_picture}
                        alt="Profile"
                        className="h-8 w-8 rounded-md object-cover"
                        width={32}
                        height={32}
                        unoptimized={true}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-md bg-gray-300 flex items-center justify-center text-xs font-normal">
                        {myProfile?.data?.initials}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-xs font-medium truncate">{myProfile?.data?.email}</p>
                    <p className="text-xs truncate">Free</p>
                  </div>
                </div>
                {[
                  { name: "Profile", icon: UserRound, handler: () => router.push("/profile-settings") },
                  // Only show Billing for administrators
                  ...(isAdministrator ? [
                    { name: "Billing", icon: Wallet, handler: () => router.push("/billings") }
                  ] : []),
                  { name: "Notifications", icon: Bell, handler: () => router.push("/notifications") },
                  { name: "Logout", icon: LogOut, handler: handleLogout }
                ].map((item, index) => (
                  <DropdownMenuItem
                    key={index}
                    onClick={item.handler}
                    className={`flex items-center gap-4 py-2 cursor-pointer last:border-t`}
                  >
                    <item.icon size={16} />
                    <span>{item.name}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

          </div>

          {/* Collapse Button */}
          <div className="px-2 py-2">
            <button
              onClick={() => setCollapsed(!collapsed)}
              className={`
                flex items-center w-full gap-2 p-2 
                text-sm text-gray-600 rounded-md
                transition-all duration-300
                hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800
                ${collapsed ? 'px-3 text-center' : 'px-2'}
              `}
              title={collapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {collapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <>
                  <ChevronLeft className="w-4 h-4" />
                  <span className="text-xs font-medium">Collapse</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Modals */}
      {isReportModalOpen && (
        <Createreport
          isOpen={isReportModalOpen}
          onClose={() => setIsReportModalOpen(false)}
        />
      )}

      {isExpenseModalOpen && (
        <ExpenseModal
          isOpen={isExpenseModalOpen}
          onClose={() => setIsExpenseModalOpen(false)}
          mode="create"
        />
      )}

      {isAdvanceModalOpen && (
        <RecordAdvanceModal
          isOpen={isAdvanceModalOpen}
          onClose={() => setIsAdvanceModalOpen(false)}
          mode="create"
        />
      )}

      {isTripModalOpen && (
        <NewtripModal
          isOpen={isTripModalOpen}
          onClose={() => setIsTripModalOpen(false)}
        />
      )}
    </>
  );
};
