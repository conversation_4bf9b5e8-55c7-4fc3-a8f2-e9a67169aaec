"use client"

import React, { useState, useId, useMemo } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from '../ui/button'
import {
  ArrowDownWideNarrow,
  ArrowUpDown,
  ArrowUpWideNarrow,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronsLeftIcon,
  ChevronsRightIcon,
  ColumnsIcon,
  GripVerticalIcon,
  ListFilter,
  Trash2
} from "lucide-react"
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  closestCenter,
  useSensor,
  useSensors,
} from "@dnd-kit/core"
import { restrictToVerticalAxis } from "@dnd-kit/modifiers"
import {
  SortableContext,
  arrayMove,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"

// Create a separate component for the drag handle
function DragHandle({ id }) {
  const { attributes, listeners } = useSortable({
    id,
  })
  return (
    <Button
      {...attributes}
      {...listeners}
      variant="ghost"
      size="icon"
      className="icon-grip size-7 text-muted-foreground hover:bg-transparent"
    >
      <GripVerticalIcon className="icon-grip .size-3 text-muted-foreground" />
      <span className="sr-only">Drag to reorder</span>
    </Button>
  )
}

// Create a draggable row component
function DraggableRow({ row, columns, columnVisibility, onRowClick, rowId }) {
  const { transform, transition, setNodeRef, isDragging } = useSortable({
    id: rowId,
  })

  return (
    <TableRow
      ref={setNodeRef}
      data-dragging={isDragging}
      className={`text-gray-950/60 dark:text-white/70 whitespace-nowrap text-start cursor-pointer h-12 relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80`}
      onClick={() => onRowClick && onRowClick(row)}
      style={{
        transform: CSS.Transform.toString(transform),
        transition: transition,
      }}
    >
      {columns?.map((column, colIndex) => {
        // Skip hidden columns, but always show unhidable ones
        if (columnVisibility[column.id || column.accessorKey] && !column.unhidable) {
          return null;
        }
        return (
          <TableCell
            key={colIndex}
            className={`dark:text-white/60 border first:border-l-0 last:border-r-0 py-0 my-0 font-[500] ${column.sticky ? 'sticky top-0 z-20 right-0 backdrop-blur-lg shadow-lg' : ''} ${colIndex > 3 ? 'text-gray-600' : 'text-gray-950'}`}
          >
            {column?.cell ? column.cell({ row: { original: row, getIsSelected: () => false, toggleSelected: () => { } } }) :
              column?.accessor ? column.accessor(row) : null}
          </TableCell>
        );
      })}
    </TableRow>
  )
}

// Reusable TableComponent
export const TableComponent = ({
  columns,
  rows,
  tableTitle,
  onRowClick,
  showImportExport = true,
  onSort,
  onFilter,
  showFilter = true,
  showColumnFilter = true,
  sortOrder,
  sortColumn,
  exportToCSV,
  NoavailableTitle,
  createTitle,
  onRowsReorder,
  filterComponents,
  isLoading,
  tableDescription = "",
  children
}) => {
  const [selectAll, setSelectAll] = useState(false);
  const [open, setOpen] = useState(false);
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [columnVisibility, setColumnVisibility] = useState({});
  const [localRows, setLocalRows] = useState(rows || []);

  // Update localRows when rows prop changes
  React.useEffect(() => {
    setLocalRows(rows || []);
  }, [rows]);

  // Calculate pagination values
  const pageCount = Math.ceil((localRows?.length || 0) / pagination.pageSize);
  const paginatedRows = localRows?.slice(
    pagination.pageIndex * pagination.pageSize,
    (pagination.pageIndex + 1) * pagination.pageSize
  );

  const canPreviousPage = pagination.pageIndex > 0;
  const canNextPage = pagination.pageIndex < pageCount - 1;

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
  };

  const getSortIcon = (column) => {
    if (column.title !== sortColumn) {
      return <ArrowUpDown className="h-2.5 w-2.5" />;
    }
    return sortOrder === "asc" ? <ArrowUpWideNarrow className="h-2.5 w-2.5" /> : <ArrowDownWideNarrow className="h-2.5 w-2.5" />;
  };

  // Setup for drag and drop
  const sortableId = useId();
  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {})
  );

  // Get IDs for sortable context
  const rowIds = useMemo(() => {
    return paginatedRows?.map((row, index) => row.id || `row-${index}`) || [];
  }, [paginatedRows]);

  // Add drag column if not already present
  const enhancedColumns = useMemo(() => {
    const hasDragColumn = columns?.some(col => col.id === 'drag');
    if (!hasDragColumn) {
      return [
        {
          id: "drag",
          header: () => null,
          cell: ({ row }) => <DragHandle id={row.original.id || `row-${row.index}`} />,
          unhidable: true, // Make drag handle always visible
        },
        ...columns || []
      ];
    }
    return columns;
  }, [columns]);

  // Handle drag end
  function handleDragEnd(event) {
    const { active, over } = event;
    if (active && over && active.id !== over.id) {
      setLocalRows((items) => {
        const oldIndex = rowIds.indexOf(active.id);
        const newIndex = rowIds.indexOf(over.id);

        const newItems = arrayMove(paginatedRows, oldIndex, newIndex);

        // Calculate the actual indices in the full dataset
        const startIdx = pagination.pageIndex * pagination.pageSize;
        const fullDatasetNewItems = [...localRows];

        // Replace the items in the full dataset
        for (let i = 0; i < newItems.length; i++) {
          fullDatasetNewItems[startIdx + i] = newItems[i];
        }

        // Call the callback if provided
        if (onRowsReorder) {
          onRowsReorder(fullDatasetNewItems);
        }

        return fullDatasetNewItems;
      });
    }
  }

  return (
    <>
      <div className="relative flex flex-col gap-2 .overflow-auto">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between p-2 px-4 pt-4">
          <div>
            <div className="text-sm font-semibold">{tableTitle}</div>
          </div>
          <div className="flex items-center gap-2">

            {showFilter && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <ListFilter className="mr-2 h-4 w-4" />
                    <span>Filter</span>
                    <ChevronDownIcon className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-60 p-4">
                  {filterComponents ? (
                    filterComponents
                  ) : (
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Options</h4>
                      <div className="flex justify-end">
                        <Button size="sm" onClick={() => onFilter && onFilter({})}>
                          Apply Filters
                        </Button>
                      </div>
                    </div>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {showColumnFilter && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <ColumnsIcon className="mr-2 h-4 w-4" />
                    <span>Columns</span>
                    <ChevronDownIcon className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  {enhancedColumns
                    .filter(column => !column.unhidable) // Don't show unhidable columns in the dropdown
                    .map((column) => {
                      // Count visible columns that are not marked as unhidable
                      const visibleColumnsCount = enhancedColumns.filter(
                        col => !columnVisibility[col.id || col.accessorKey] && !col.unhidable
                      ).length;

                      const isChecked = !columnVisibility[column.id || column.accessorKey];
                      // Disable checkbox if this is the last visible column
                      const disabled = isChecked && visibleColumnsCount <= 1;

                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id || column.accessorKey}
                          className="capitalize text-xs"
                          checked={isChecked}
                          disabled={disabled}
                          onCheckedChange={(value) => {
                            // Prevent hiding all columns
                            if (!value && visibleColumnsCount <= 1) {
                              return;
                            }

                            setColumnVisibility({
                              ...columnVisibility,
                              [column.id || column.accessorKey]: !value,
                            });
                          }}
                        >
                          {column.header || column.title}
                        </DropdownMenuCheckboxItem>
                      );
                    })}
                </DropdownMenuContent>
              </DropdownMenu>
            )}


            {showImportExport && (
              <Button onClick={exportToCSV} variant="outline" size="sm">
                <span>Export CSV</span>
              </Button>
            )}
          </div>
        </div>

        <div className="overflow-hidden .rounded-lg .border">
          <DndContext
            collisionDetection={closestCenter}
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={handleDragEnd}
            sensors={sensors}
            id={sortableId}
          >
            <Table className="min-w-full table-auto">
              <TableHeader className="sticky top-0 z-10 bg-muted">
                <TableRow>
                  {enhancedColumns?.map((column, index) => {
                    // Skip hidden columns, but always show unhidable ones
                    if (columnVisibility[column.id || column.accessorKey] && !column.unhidable) {
                      return null;
                    }
                    return (
                      <TableHead
                        key={index}
                        className={`h-10 whitespace-nowrap text-sm font-[500] text-slate-950/50 dark:text-white border ${column.sticky ? 'sticky top-0 z-20 right-0 backdrop-blur-lg shadow-lg .bg-background' : ''}`}
                        onClick={() => column.sortable && onSort(column)}
                      >
                        {typeof column.header === 'function' ? column.header() : (
                          <span className='flex items-center justify-between cursor-pointer'>
                            {column?.title || column.header}
                            {column.sortable && getSortIcon(column)}
                          </span>
                        )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody className="text-slate-950 font-[500] text-[13px]">
                {isLoading ? (
                  <TableRow>
                    <TableCell
                      colSpan={enhancedColumns?.length}
                      className="text-center py-8 text-gray-500 font-normal"
                    >
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : paginatedRows?.length > 0 ? (
                  <SortableContext
                    items={rowIds}
                    strategy={verticalListSortingStrategy}
                  >
                    {paginatedRows.map((row, rowIndex) => (
                      <DraggableRow
                        key={row.id || `row-${rowIndex}`}
                        rowId={row.id || `row-${rowIndex}`}
                        row={row}
                        columns={enhancedColumns}
                        columnVisibility={columnVisibility}
                        onRowClick={onRowClick}
                      />
                    ))}
                  </SortableContext>
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={enhancedColumns?.length}
                      className="text-center space-y-4 font-normal py-8 overflow-hidden w-fit"
                    >
                      <h4 className='text-sm font-semibold mb-4 text-primary'>You don&apos;t have any {NoavailableTitle} yet</h4>
                      <div className="flex items-center justify-center">
                        <p className='text-xs .text-primary w-[60%] font-medium text-gray-500'>{tableDescription}</p>
                      </div>
                      {createTitle || ""}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </DndContext>
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center justify-between px-4">
          <div className="hidden flex-1 text-sm text-muted-foreground lg:flex">
            {selectAll ? `${localRows?.length} of ${localRows?.length} row(s) selected.` : ''}
          </div>
          <div className="flex w-full items-center gap-8 lg:w-fit">
            <div className="hidden items-center gap-2 lg:flex">
              <Label htmlFor="rows-per-page" className="text-sm font-medium">
                Rows per page
              </Label>
              <Select
                value={`${pagination.pageSize}`}
                onValueChange={(value) => {
                  setPagination({
                    ...pagination,
                    pageSize: Number(value),
                    pageIndex: 0, // Reset to first page when changing page size
                  });
                }}
              >
                <SelectTrigger className="w-20" id="rows-per-page">
                  <SelectValue placeholder={pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex w-fit items-center justify-center text-sm font-medium">
              Page {pagination.pageIndex + 1} of {pageCount || 1}
            </div>
            <div className="ml-auto flex items-center gap-2 lg:ml-0">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => setPagination({ ...pagination, pageIndex: 0 })}
                disabled={!canPreviousPage}
              >
                <span className="sr-only">Go to first page</span>
                <ChevronsLeftIcon className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="size-8"
                size="icon"
                onClick={() => setPagination({ ...pagination, pageIndex: pagination.pageIndex - 1 })}
                disabled={!canPreviousPage}
              >
                <span className="sr-only">Go to previous page</span>
                <ChevronLeftIcon className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="size-8"
                size="icon"
                onClick={() => setPagination({ ...pagination, pageIndex: pagination.pageIndex + 1 })}
                disabled={!canNextPage}
              >
                <span className="sr-only">Go to next page</span>
                <ChevronRightIcon className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden size-8 lg:flex"
                size="icon"
                onClick={() => setPagination({ ...pagination, pageIndex: pageCount - 1 })}
                disabled={!canNextPage}
              >
                <span className="sr-only">Go to last page</span>
                <ChevronsRightIcon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
