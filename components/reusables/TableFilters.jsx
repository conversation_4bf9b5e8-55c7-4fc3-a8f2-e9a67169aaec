"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// import { DatePickerWithRange } from '@/components/ui/date-range-picker';

/**
 * Reusable TableFilters component that can be used across different pages
 * Supports URL query parameters for filter persistence
 */
const TableFilters = ({
  filterValues,
  onFilterApply,
  onFilterClear,
  filterOptions,
  customFilters
}) => {
  const handleInputChange = (field, value) => {
    const newFilters = { ...filterValues, [field]: value };
    onFilterApply(newFilters);
  };

  return (
    <div>
      <div className="flex flex-col p-0 gap-4">
        {/* Text Input Filters */}
        {filterOptions?.inputFilters?.map((filter) => (
          <div key={filter.key} className="relative">
            <FloatingLabelInput
              id={filter.key}
              name={filter.key}
              label={filter.label}
              type={filter.type || "text"}
              value={filterValues[filter.key] || ""}
              className="text-xs [&+label]:text-[10px]"
              onChange={(e) => handleInputChange(filter.key, e.target.value)}
            />
          </div>
        ))}

        {/* Select Filters */}
        {filterOptions?.selectFilters?.map((filter) => (
          <div key={filter.key} className="relative">
            <span className='text-[10px] font-semibold'>{filter?.label}</span>
            <Select
              value={filterValues[filter.key]}
              onValueChange={(value) => handleInputChange(filter.key, value)}
            >
              <SelectTrigger className="text-xs">
                <SelectValue placeholder={filter.placeholder} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem className="text-xs" value="all">All</SelectItem>
                {filter.options?.map((item) => (
                  <SelectItem
                    className="text-xs"
                    key={item.id || item.value}
                    value={String(item.id || item.value)}
                  >
                    {item.name || item.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ))}

        {/* Amount Filter */}
        {filterOptions?.showAmountFilter && (
          <div className="flex gap-2">
            <FloatingLabelInput
              id="minAmount"
              name="minAmount"
              label="Min Amount"
              type="number"
              value={filterValues.minAmount || ""}
              className="text-xs [&+label]:text-[10px]"
              onChange={(e) => handleInputChange('minAmount', e.target.value)}
            />
            <FloatingLabelInput
              id="maxAmount"
              name="maxAmount"
              label="Max Amount"
              type="number"
              className="text-xs [&+label]:text-[10px]"
              value={filterValues.maxAmount || ""}
              onChange={(e) => handleInputChange('maxAmount', e.target.value)}
            />
          </div>
        )}

        {/* Date Range Filter */}

        {/*
        
        {filterOptions?.showDateFilter && (
          <DatePickerWithRange
            value={filterValues.dateRange}
            onChange={(dateRange) => handleInputChange('dateRange', dateRange)}
          />
        )}

        */}

        {customFilters}

      </div>

      {/* Filter Actions */}
      <div className="flex justify-end gap-2 pt-6">
        <Button
          onClick={onFilterClear}
          variant="outline"
          size="xs"
        >
          {filterOptions?.clearButtonText || "Clear"}
        </Button>
        <Button
          onClick={() => onFilterApply(filterValues)}
          variant="default"
          size="xs"
        >
          {filterOptions?.applyButtonText || "Apply"}
        </Button>
      </div>
    </div>
  );
};

export default TableFilters;
