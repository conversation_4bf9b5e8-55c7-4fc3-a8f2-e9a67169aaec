import { formatDate } from "@/utils/Utils";
import { CalendarDays, CircleUserRound, Clipboard, DollarSign } from "lucide-react";
import React from "react";

const AllCards = ({ cardData, className, iconSize, title="Approver" }) => {
  return (

    <div className="flex flex-col sm:flex-row items-center justify-between gap-3 overflow-hidden w-full">

      <div className="p-2 sm:p-4 flex items-center justify-between gap-3 sm:my-4 border-r overflow-hidden w-full">

        <div className="flex items-center gap-4">
          <div
            className="p-2 rounded-xl border bg-[#DDF9E3] border-[#C4F5CF]"
          >
            <DollarSign size={iconSize} className="dark:text-background"/>
          </div>
          <div className="flex flex-col gap-1">
            <div className="font-normal text-sm md:text-sm">Total</div>
            <div className={`font-semibold text-base md:text-medium whitespace-nowrap ${className}`}>{cardData?.total_expenses_display}</div>
          </div>
        </div>
      </div>

      <div className="p-2 sm:p-4 flex items-center justify-between gap-3 sm:my-4 border-r overflow-hidden w-full">

        <div className="flex items-center gap-4">
          <div
            className="p-2 rounded-xl border bg-[#D8F5FB] border-[#C7EFFC]"
          >
            <CircleUserRound size={iconSize} className="dark:text-background"/>
          </div>
          <div className="flex flex-col gap-1">
            <div className="font-normal text-sm md:text-sm">{title}</div>
            <div className={`font-semibold text-base md:text-medium capitalize whitespace-nowrap ${className}`}>{cardData?.approver?.name}</div>
          </div>
        </div>
      </div>

      <div className="p-2 sm:p-4 flex items-center justify-between gap-3 sm:my-4 border-r overflow-hidden w-full">

        <div className="flex items-center gap-4">
          <div
            className="p-2 rounded-xl border bg-[#FEF3CA] border-[#FEEBAC]"
          >
            <CalendarDays size={iconSize} className="dark:text-background"/>
          </div>
          <div className="flex flex-col gap-1">
            <div className="font-normal text-sm md:text-sm">Date Submitted</div>
            <div className={`font-semibold text-base md:text-medium whitespace-nowrap ${className}`}>{formatDate(cardData?.created_at)}</div>
          </div>
        </div>
      </div>

      <div className="p-2 sm:p-4 flex items-center justify-between gap-3 sm:my-4 overflow-hidden w-full">

        <div className="flex items-center gap-4">
          <div
            className="p-2 rounded-xl border bg-[#F4F5F7] border-[#F0F0F3]"
          >
            <Clipboard size={iconSize} className="dark:text-background"/>
          </div>
          <div className="flex flex-col gap-1">
            <div className="font-normal text-sm md:text-sm">Report Type</div>
            <div className={`font-semibold text-base md:text-medium whitespace-nowrap ${className}`}>{cardData?.report_type_display}</div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default AllCards;