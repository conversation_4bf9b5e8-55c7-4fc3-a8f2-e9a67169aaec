"use client";

import React from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

const Tabaccross = ({ tabs = [], className, defaultTab}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || defaultTab;

  const handleTabClick = (tabValue) => {
    router.push(`?tab=${tabValue}`, { scroll: false });
  };

  return (
    <div className="rounded-lg p-1 flex items-center bg-[#F4F4F5] dark:bg-secondary">
      {tabs.map((tab, index) => (
        <div
          key={index}
          onClick={() => handleTabClick(tab.value)}
          className={`
            flex gap-2 whitespace-nowrap overflow-hidden text-xs font-semibold px-2 p-[4px] rounded-md cursor-pointer
            transition-all duration-200 ease-in-out
            ${className}
            ${currentTab === tab.value 
              ? 'text-gray-950 shadow-md bg-white dark:bg-primary dark:text-background' 
              : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
            }
          `}
        >
          {tab?.label}
          {(tab?.count !== undefined) && (
            <div className={`
              w-4 h-4 p-2 rounded-full flex items-center justify-center font-semibold text-[10px]
              ${currentTab === tab.value 
                ? 'bg-gray-200 dark:bg-gray-700 dark:text-gray-200' 
                : 'bg-gray-300 dark:bg-gray-600 dark:text-gray-200'
              }
            `}>
              {tab?.count}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default Tabaccross;
