import { Upload } from 'lucide-react'
import React from 'react'

export const SecondFileUpload = ({file, onFileChange, className}) => {
  return (
    <>
      <div className={`border-dashed border p-4 mb-4 rounded-md shadow-sm bg-white ${className}`}>
        {!file ? (
          <div className="flex items-center gap-4">
            <Upload strokeWidth={1} size={30} />
            <div className="text-gray-500 text-sm">
              Drop or drag receipts or {" "}
              <span
                className="font-semibold cursor-pointer"
                onClick={() => document.getElementById("fileUpload").click()}
              >
                click here
              </span>{" "}
              to upload
            </div>

            <input
              type="file"
              id="fileUpload"
              className="hidden"
              onChange={onFileChange}
            />

          </div>

        ) : (
          <div className="text-gray-500">File Attached: {file.name}</div>
        )}
      </div>
    </>
  )
}