import {
  Dialog,
  DialogClose,
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

const DeleteModal = ({
  open,
  onClose,
  onDelete,
  isDeleting,
  title = "Are you sure you want to delete this item?",
  description,
  cancelText = "Cancel",
  confirmText = "Delete",
  isDeletingText = "Deleting...",
  children,
}) => (
  <Dialog open={open} onOpenChange={onClose}>
    <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
      <DialogHeader className="flex items-center justify-center text-center mx-auto">
        <DialogTitle className="text-sm font-normal">
          {title}
        </DialogTitle>
      </DialogHeader>
      {description && (
        <div className="text-center text-xs text-muted-foreground .mb-4">{description}</div>
      )}
      {children}
      <DialogFooter className="flex items-center justify-center gap-2 mt-4">
        <DialogClose asChild>
          <Button variant="outline" size="xs" disabled={isDeleting}>
            {cancelText}
          </Button>
        </DialogClose>
        <Button
          onClick={onDelete}
          size="xs"
          disabled={isDeleting}
        >
          {isDeleting ? isDeletingText : confirmText}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
);

export default DeleteModal;