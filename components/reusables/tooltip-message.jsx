import { MessageCircle } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export const TooltipMessage = ({ message, icon = <MessageCircle strokeWidth={1.8} size={18} />,
  tooltipClassName = "rounded-full bg-gray-950 text-white text-xs"
}) => {
  if (!message) return null;
  
  return (
    <div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger onClick={(e) => {e.stopPropagation()}}>
            {icon}
          </TooltipTrigger>
          <TooltipContent className={tooltipClassName}>
            <p>{message}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};
