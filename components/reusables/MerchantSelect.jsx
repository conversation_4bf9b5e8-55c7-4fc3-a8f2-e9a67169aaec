import { useState } from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createMerchant } from "@/apis/merchant";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import ButtonLoader from "@/utils/spinner/ButtonLoader";

export const MerchantSelect = ({ 
  value, 
  onChange, 
  merchants = [], 
  error,
  className,
  merchantPlaceholder="Search or add new merchant *"
}) => {
  const [merchantSearch, setMerchantSearch] = useState("");
  const queryClient = useQueryClient();

  const { mutateAsync: merchantMutation, isPending: isCreatingMerchant } = useMutation({
    mutationKey: ["createMerchant"],
    mutationFn: createMerchant,
    onSuccess: () => {
      queryClient.invalidateQueries(["listOfMerchants"]);
    },
  });

  const handleMerchantSearchChange = (e) => {
    setMerchantSearch(e.target.value);
    e.stopPropagation();
  };

  const handleCreateMerchant = async () => {
    if (!merchantSearch.trim()) return;

    try {
      const response = await merchantMutation({
        name: merchantSearch.trim(),
      });

      if (response?.data?.id) {
        onChange(String(response.data.id));
        setMerchantSearch("");
        toast.success(response?.message || "Merchant created successfully");
      }
    } catch (error) {
      toast.error(error?.message || "Failed to create merchant");
    }
  };

  const filteredMerchants = merchants?.filter((item) =>
    item.name.toLowerCase().includes(merchantSearch.toLowerCase())
  ) || [];

  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className={`text-xs w-18 ${error ? "validate_input" : ""} ${className}`}>
        <SelectValue placeholder={merchantPlaceholder} />
      </SelectTrigger>
      <SelectContent>
        <div
          className="flex items-center gap-2 sticky top-0 z-10 p-0 pb-1"
          onKeyDown={(e) => e.stopPropagation()}
        >
          <div className="flex items-center gap-2 w-full border rounded-md px-2">
            <Search className="w-3 h-3 text-[#9F9F9F]" />
            <Input
              type="search"
              placeholder="Search merchant..."
              value={merchantSearch}
              onChange={handleMerchantSearchChange}
              onClick={(e) => e.stopPropagation()}
              className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 placeholder:text-xs"
            />
          </div>
        </div>
        {filteredMerchants?.map((item) => (
          <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
            {item?.name}
          </SelectItem>
        ))}
        {merchantSearch && filteredMerchants?.length === 0 && (
          <div className="p-2">
            <div className="text-sm text-gray-500 mb-2">
              No merchants found.
            </div>
            <button
              onClick={handleCreateMerchant}
              className="w-full px-3 py-2 bg-primary/5 hover:bg-primary/10 rounded-md text-primary text-sm font-medium transition-colors"
              type="button"
            >
              {isCreatingMerchant ? (
                <span className="flex items-center justify-center gap-2 text-xs">
                  <ButtonLoader /> Adding...
                </span>
              ) : (
                <span className="text-xs">Add &quot;{merchantSearch}&quot; as new merchant</span>
              )}
            </button>
          </div>
        )}
      </SelectContent>
    </Select>
  );
};