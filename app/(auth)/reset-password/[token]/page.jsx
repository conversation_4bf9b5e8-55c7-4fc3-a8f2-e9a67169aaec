"use client"

import { CreatenewPasswordApi } from '@/apis/auth';
import { Button } from '@/components/ui/button';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { useMutation } from '@tanstack/react-query';
import { Eye, EyeOff } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import React, { useState } from 'react'
import toast from 'react-hot-toast';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';

const Page = () => {
  const router = useRouter();
  const params = useParams();
  const token = params.token;

  const [payload, setPayload] = useState({
    token: token,
    password: "",
    confirmPassword: ""
  });

  const [backendErrors, setBackendErrors] = useState({});

  const [togglePassword, setTogglePassword] = useState({
    password: false,
    confirmPassword: false
  });

  const handleTogglePassword = (field) => {
    setTogglePassword((prev) => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleChange = (key, value) => {
    setPayload({ ...payload, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["create-password"],
    mutationFn: CreatenewPasswordApi,
  });

  const handleCreateNewPassword = async (e) => {
    e.preventDefault();

    // Client-side validation for password matching
    if (payload.password !== payload.confirmPassword) {
      setBackendErrors({
        ...backendErrors,
        confirmPassword: "Passwords do not match"
      });
      return;
    }

    // Client-side validation for password length
    if (payload.password && payload.password.length < 8) {
      setBackendErrors({
        ...backendErrors,
        password: "Password must be at least 8 characters long"
      });
      return;
    }

    try {
      // Only send token and password to the API
      const apiPayload = {
        token: payload.token,
        password: payload.password
      };
      
      const response = await mutateAsync(apiPayload);
      toast.success(response?.message || "Password reset successful");
      router.push("/");
    } catch (error) {
      console.log(error);
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error.message || "Failed to reset password");
    }
  };

  const isSubmitDisabled = !payload.password || !payload.confirmPassword || isPending;

  return (
    <>
      <div className="">
        <div className="p-4 md:px-10 rounded-xl border bg-[#FFFFFF] dark:bg-background h-[80vh] 2xl:h-[60vh] text-card-foreground shadow mx-auto w-full md:w-[45%] 2xl:w-[45%] 2xl:px-20 flex flex-col justify-center">

          <div className='flex flex-col items-center'>
            <p className="text-xl font-semibold">Create New Password</p>
            <span className='text-xs text-center my-4'>Please enter your new password</span>
          </div>

          <div className="">
            <div className="space-y-4">

              <form onSubmit={handleCreateNewPassword}>
                <div className="space-y-4">

                  <div>
                    <div className="flex flex-col gap-3 relative">
                      <FloatingLabelInput
                        id="password"
                        value={payload.password}
                        className={`placeholder:text_gray ${backendErrors.password ? "validate_input" : ""}`}
                        name="password"
                        onChange={(e) => handleChange("password", e.target.value)}
                        label="Password"
                        type={togglePassword.password ? "text" : "password"}
                      />
                      <span
                        className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer top-0 h-[42px]"
                        onClick={() => handleTogglePassword("password")}
                      >
                        {togglePassword.password ? (
                          <EyeOff size={16} className="text-gray-500" />
                        ) : (
                          <Eye size={16} className="text-gray-500" />
                        )}
                      </span>
                    </div>
                    <ErrorMessage errors={backendErrors} field="password" />
                  </div>

                  {/* Confirm Password Field */}
                  <div>
                    <div className="flex flex-col gap-3 relative">
                      <FloatingLabelInput
                        id="confirm_password"
                        value={payload.confirmPassword}
                        className={`placeholder:text_gray ${backendErrors.confirmPassword ? "validate_input" : ""}`}
                        name="confirmPassword"
                        onChange={(e) => handleChange("confirmPassword", e.target.value)}
                        label="Confirm Password"
                        type={togglePassword.confirmPassword ? "text" : "password"}
                      />
                      <span
                        className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer top-0 h-[42px]"
                        onClick={() => handleTogglePassword("confirmPassword")}
                      >
                        {togglePassword.confirmPassword ? (
                          <EyeOff size={16} className="text-gray-500" />
                        ) : (
                          <Eye size={16} className="text-gray-500" />
                        )}
                      </span>
                    </div>
                    <ErrorMessage errors={backendErrors} field="confirmPassword" />
                  </div>

                  <div className='flex items-center justify-center'>
                    <Button
                      disabled={isSubmitDisabled}
                      type="submit"
                      className={`w-full ${isSubmitDisabled ? 'btn_opacity' : ''}`} 
                      size="xs"
                    >
                      {isPending ? (
                        <span>
                          <ButtonLoader color="#FFFFFF" />
                        </span>
                      ) : (
                        <span>
                          Create Password
                        </span>
                      )}
                    </Button>
                  </div>

                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Page;