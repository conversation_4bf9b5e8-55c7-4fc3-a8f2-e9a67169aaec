"use client";

import { ValidateInvitation<PERSON><PERSON>, InvitationResponseApi } from "@/apis/auth";
import { Button } from "@/components/ui/button";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import ButtonLoader from "@/utils/spinner/ButtonLoader";
import { useMutation } from "@tanstack/react-query";
import { User, Mail, Lock, Eye, EyeOff, LayoutDashboard, Building } from "lucide-react";
import { useRouter, useParams } from "next/navigation";
import { useState, useEffect, Suspense } from "react";
import toast from "react-hot-toast";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";

const InviteUserContent = () => {
  const router = useRouter();
  const params = useParams();
  const token = params.token;

  const [inviteData, setInviteData] = useState(null);
  const [backendErrors, setBackendErrors] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [togglePassword, setTogglePassword] = useState({
    password: false,
    confirmPassword: false,
  });

  const [payload, setPayload] = useState({
    name: "",
    password: "",
    confirmPassword: "",
  });

  // Validate token on mount
  useEffect(() => {
    const validateToken = async () => {
      try {
        const response = await ValidateInvitationApi(token);
        setInviteData(response.data);
      } catch (error) {
        toast.error(error?.message || "Invalid invitation link");
        router.push("/dashboard");
      } finally {
        setIsLoading(false);
      }
    };

    validateToken();
  }, [token, router]);

  const handleChange = (key, value) => {
    setPayload({ ...payload, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["accept-invitation"],
    mutationFn: InvitationResponseApi,
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (payload.password !== payload.confirmPassword) {
      setBackendErrors({
        ...backendErrors,
        confirmPassword: "Passwords do not match",
      });
      return;
    }

    if (payload.password && payload.password.length < 8) {
      setBackendErrors({
        ...backendErrors,
        password: "Password must be at least 8 characters long",
      });
      return;
    }

    try {
      const apiPayload = {
        response: "accept",
        name: payload.name,
        password: payload.password,
      };

      await mutateAsync({ token, payload: apiPayload });
      toast.success(mutateAsync?.message || "Account setup successful!");
      router.push("/");
    } catch (error) {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to complete setup");
    }
  };

  const handleTogglePassword = (field) => {
    setTogglePassword((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ButtonLoader />
      </div>
    );
  }

  const isSubmitDisabled =
    !payload.name ||
    !payload.password ||
    !payload.confirmPassword ||
    isPending;

  return (
    <div className="min-h-[90vh] flex items-center justify-center">
      <div className="p-4 md:px-10 rounded-xl border bg-[#FFFFFF] dark:bg-background text-card-foreground shadow mx-auto w-full h-full sm:h-[80vh] lg:h-full md:w-[40%] 2xl:w-[45%] 2xl:px-20">
        <div className="flex pb-4  items-center justify-center border-b mb-4">
          <div href="/dashboard" className="flex items-center gap-2 font-semibold">
            <LayoutDashboard className="h-6 w-6" />
            <span>Eloope</span>
          </div>
        </div>

        <div className="flex flex-col items-center">
          <p className="text-xl font-semibold">Join Your Team on Eloope</p>
          <span className="text-xs text-center my-4">
            Complete your account setup to start managing expenses with your team.
          </span>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
              <User className="h-4 w-4 text-gray-500" />
            </div>
            <FloatingLabelInput
              id="name"
              label="Full Name"
              value={payload.name}
              onChange={(e) => handleChange("name", e.target.value)}
              className="pl-10"
              required
            />
            <ErrorMessage errors={backendErrors} field="name" />
          </div>

          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
              <Mail className="h-4 w-4 text-gray-500" />
            </div>
            <FloatingLabelInput
              id="email"
              label="Email"
              value={inviteData?.email || ""}
              className="pl-10"
              disabled
            />
          </div>

          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
              <Lock className="h-4 w-4 text-gray-500" />
            </div>
            <FloatingLabelInput
              id="password"
              type={togglePassword.password ? "text" : "password"}
              label="Password"
              value={payload.password}
              onChange={(e) => handleChange("password", e.target.value)}
              className="pl-10"
              required
            />
            <button
              type="button"
              onClick={() => handleTogglePassword("password")}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {togglePassword.password ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
            </button>
            <ErrorMessage errors={backendErrors} field="password" />
          </div>

          <div className="relative">
            <div className="absolute left-3 top-1/2 -translate-y-1/2 z-10">
              <Lock className="h-4 w-4 text-gray-500" />
            </div>
            <FloatingLabelInput
              id="confirmPassword"
              type={togglePassword.confirmPassword ? "text" : "password"}
              label="Confirm Password"
              value={payload.confirmPassword}
              onChange={(e) => handleChange("confirmPassword", e.target.value)}
              className="pl-10"
              required
            />
            <button
              type="button"
              onClick={() => handleTogglePassword("confirmPassword")}
              className="absolute right-3 top-1/2 -translate-y-1/2"
            >
              {togglePassword.confirmPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
            </button>
            <ErrorMessage errors={backendErrors} field="confirmPassword" />
          </div>

          {/* Company Info */}
          <div className="py-4">
            <p className="text-sm text-gray-600 flex items-center gap-2">
              You&apos;re joining: <Building size={16}/>
              <span className="font-medium">
                {inviteData?.organization?.name}
              </span>
            </p>
          </div>

          {/* Submit Button */}
          <div className="space-y-3">
            <Button
              type="submit"
              className="w-full bg-black text-white"
              size="xs"
              disabled={isLoading}
            >
              {isPending ? <ButtonLoader color="#FFFFFF" /> : "Complete Setup"}
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="w-full"
              size="xs"
              onClick={() => router.push("/")}
            >
              Cancel
            </Button>
          </div>
        </form>

        {/* Support Link */}
        <div className="text-center mt-6 text-sm text-gray-500">
          Need help? Contact{" "}
          <a
            href="mailto:<EMAIL>"
            className="text-blue-600 hover:underline"
          >
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
  );
};

// Main page component with Suspense boundary
const InviteUserPage = () => {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="w-full max-w-md p-8 bg-white rounded-xl shadow-sm">
            <div className="text-center">Loading...</div>
          </div>
        </div>
      }
    >
      <InviteUserContent />
    </Suspense>
  );
};

export default InviteUserPage;