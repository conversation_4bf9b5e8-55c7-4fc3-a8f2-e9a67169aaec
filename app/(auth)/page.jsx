"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import Link from 'next/link';
import Image from 'next/image';
import Button<PERSON>oader from '@/utils/spinner/ButtonLoader';
import { LoginApi } from '@/apis/auth';
import { Eye, EyeOff } from 'lucide-react';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';


export default function Login() {

  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  })

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value })
    setBackendErrors({ ...backendErrors, [name]: null });
  }

  const [toggle, setToggle] = useState(false)
  const togglePassword = () => setToggle(!toggle)

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["login"],
    mutationFn: LoginApi
  })

  const handleLogin = async (e) => {
    e.preventDefault();
    try {
      const response = await mutateAsync(formData)
      
      // Extract user data and token
      const userData = response?.data?.user
      const userAuthToken = response?.data?.token
      
      // Define storage keys
      const eloopeKey = 'Eloope_UserData'
      const eloopeToken = 'EloopeToken'
      
      // Ensure we're in browser environment
      if (typeof window !== "undefined") {
        // Store user data and token
        localStorage.setItem(eloopeKey, JSON.stringify({ userData }));
        localStorage.setItem(eloopeToken, JSON.stringify({ userAuthToken }));
        
        // Verify data was stored correctly
        const storedToken = localStorage.getItem(eloopeToken);
        const storedUser = localStorage.getItem(eloopeKey);
        
        if (!storedToken || !storedUser) {
          throw new Error("Failed to store authentication data");
        }
        
        // Show success message
        toast.success(`${response.message}`);
        
        // Get redirect path with a small delay to ensure localStorage is updated
        setTimeout(() => {
          const lastPath = localStorage.getItem('lastPath') || '/dashboard';
          localStorage.removeItem('lastPath'); // Clear it after use
          
          // Redirect to last path or dashboard
          window.location.href = lastPath;
        }, 100);
      }
    } catch (error) {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(`${error.message || "Login failed"}`);
    }
  }

  const check = !formData.email || !formData.password || isPending

  return (
    <div>
      <div className="p-10 md:px-10 2xl:px-20 rounded-xl border bg-[#FFFFFF] dark:bg-background h-[80vh] 2xl:h-[60vh] text-card-foreground .shadow mx-auto w-full md:w-[45%] 2xl:w-[45%] @3xl:w-[45%] flex flex-col justify-center">

        <div className="flex flex-col space-y-1.5 p-6 text-center">
          <h3 className="font-semibold tracking-tight text-base text-primary md:text-4xl text-center">Welcome back!</h3>
          <p className="text-xs text-muted-foreground">Don’t have an account yet? <Link href={"/signup"} className='text-blue-600'> Sign up now</Link></p>
        </div>

        <form onSubmit={handleLogin} className=".p-6 .pt-0">
          <div className="space-y-4">

            <div className="flex flex-col gap-3">
              <FloatingLabelInput id="email" value={formData.email} className="placeholder:text_gray" name="email" onChange={handleChange} label="Email Address *" type="email" />
              <ErrorMessage errors={backendErrors} field="email" />
            </div>

            <div className="flex flex-col gap-3 relative">
              <FloatingLabelInput id="password" value={formData.password} className="placeholder:text_gray" name="password" onChange={handleChange} label="Password *" type={toggle ? "text" : "password"} />
              <span
                className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                onClick={togglePassword}
              >
                {toggle ? (
                  <EyeOff size={16} className="text-gray-500" />
                ) : (
                  <Eye size={16} className="text-gray-500" />
                )}
              </span>
              <ErrorMessage errors={backendErrors} field="password" />
            </div>

            <div className='flex items-center justify-end text-xs'>
              <Link href={"/forgot-password"} className='text-blue-600'>Forgot password ?</Link>
            </div>


            <div className='flex items-center justify-center'>
              <Button
                disabled={check}
                type="submit"
                className={`w-full ${check ? 'btn_opacity' : ''}`} size="xs"
              >
                {isPending ? (
                  <span>
                    <ButtonLoader color="#FFFFF" />
                  </span>

                ) : (
                  <span>
                    Login
                  </span>

                )}
              </Button>
            </div>

          </div>

        </form>

        <div className="flex items-center mt-4">
          <hr className='w-full' />
          <span className='px-4'>or</span>
          <hr className='w-full' />
        </div>

        <div className='flex items-center justify-center py-4'>
          <Button className="w-full gap-3" size="lg" variant="outline">
            <Image src='/images/icons/google.svg' width={20} height={20} alt='google icon' />
            <span>
              Login with Google
            </span>
          </Button>
        </div>




      </div >

    </div >
  )
}