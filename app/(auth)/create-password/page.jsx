"use client";

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Mail, CircleAlert, EyeOff, Eye } from "lucide-react";
import { useMutation } from '@tanstack/react-query';
import { Registration } from '@/apis/auth';
import toast from 'react-hot-toast';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { useRouter, useSearchParams } from 'next/navigation';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';

const CreatePasswordContent = () => {
  const router = useRouter();

  const [countdown, setCountdown] = useState({ minutes: 0, seconds: 0 });
  const [toggle, setToggle] = useState({
    password: false,
    confirm_password: false,
  });

  const [formData, setFormData] = useState({
    otp: "",
    password: "",
    confirm_password: ""
  });

  const [backendErrors, setBackendErrors] = useState({});
  const [email, setEmail] = useState("");

  useEffect(() => {
    // Get signup data from localStorage
    const signupDataStr = localStorage.getItem('eloope_signup_data');
    if (!signupDataStr) {
      toast.error("Signup data not found. Please try signing up again.");
      router.push('/signup');
    } else {
      try {
        const signupData = JSON.parse(signupDataStr);
        setEmail(signupData.email);

        // Calculate remaining time based on the timer from backend
        const timerInMinutes = signupData.timer || 10; // Default to 10 minutes
        const savedTimestamp = signupData.timestamp || Date.now();
        const elapsedTimeInSeconds = Math.floor((Date.now() - savedTimestamp) / 1000);
        const totalTimerSeconds = timerInMinutes * 60;
        const remainingSeconds = Math.max(0, totalTimerSeconds - elapsedTimeInSeconds);

        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;

        setCountdown({ minutes, seconds });
      } catch (error) {
        toast.error("Invalid signup data. Please try signing up again.");
        router.push('/signup');
      }
    }
  }, [router]);

  useEffect(() => {
    let timer;
    if (countdown.minutes > 0 || countdown.seconds > 0) {
      timer = setInterval(() => {
        setCountdown(prev => {
          if (prev.seconds > 0) {
            return { ...prev, seconds: prev.seconds - 1 };
          } else if (prev.minutes > 0) {
            return { minutes: prev.minutes - 1, seconds: 59 };
          } else {
            return { minutes: 0, seconds: 0 };
          }
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  const { mutateAsync: resendOtp, isPending: isResending } = useMutation({
    mutationFn: Registration,
    onSuccess: (data) => {
      toast.success(data?.message || "OTP resent successfully");

      // Update the timer and timestamp in localStorage
      const signupDataStr = localStorage.getItem('eloope_signup_data');
      if (signupDataStr) {
        try {
          const signupData = JSON.parse(signupDataStr);
          const updatedData = {
            ...signupData,
            timer: data.data?.timer || 10, // Get new timer from response or default to 10
            timestamp: Date.now()
          };
          localStorage.setItem('eloope_signup_data', JSON.stringify(updatedData));

          // Reset countdown with new timer
          const timerInMinutes = updatedData.timer;
          setCountdown({ minutes: timerInMinutes, seconds: 0 });
        } catch (error) {
          console.error("Error updating timer data:", error);
        }
      }
    },
    onError: (error) => {
      toast.error(error.message || "Failed to resend OTP");
    }
  });

  const { mutateAsync: completeRegistration, isPending: isSubmitting } = useMutation({
    mutationFn: Registration,
    onSuccess: (data) => {
      toast.success(data.message || "Registration completed successfully");
      router.push('/');
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error.message || "Registration failed");
    }
  });

  const handleResendOtp = async () => {
    if (!email) {
      toast.error("Email not found. Please try signing up again.");
      return;
    }

    // Use the Registration endpoint with step=1 for resending OTP
    try {
      // Get the stored user data from signup
      const signupDataStr = localStorage.getItem('eloope_signup_data');
      if (!signupDataStr) {
        toast.error("Signup data not found. Please try signing up again.");
        return;
      }

      const signupData = JSON.parse(signupDataStr);

      const payload = {
        name: signupData.fullName,
        email: signupData.email,
        phone: signupData.phone,
        organization_name: signupData.organization_name,
        organization_short_name: signupData.organization_short_name,
        country: signupData.country.toLowerCase()
      };

      await resendOtp(payload);
    } catch (error) {
      console.error("Failed to resend OTP:", error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setBackendErrors({ ...backendErrors, [name]: null });
  };

  const togglePasswordVisibility = (field) => {
    setToggle({ ...toggle, [field]: !toggle[field] });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Client-side validation for password matching
    if (formData.password !== formData.confirm_password) {
      setBackendErrors({
        ...backendErrors,
        confirm_password: "Passwords do not match"
      });
      return;
    }

    // Client-side validation for password length
    if (formData.password && formData.password.length < 8) {
      setBackendErrors({
        ...backendErrors,
        password: "Password must be at least 8 characters long"
      });
      return;
    }

    try {
      // Get the stored user data from signup
      const signupDataStr = localStorage.getItem('eloope_signup_data');
      if (!signupDataStr) {
        toast.error("Signup data not found. Please try signing up again.");
        return;
      }

      const signupData = JSON.parse(signupDataStr);

      const payload = {
        name: signupData.fullName,
        email: signupData.email,
        phone: signupData.phone,
        organization_name: signupData.organization_name,
        organization_short_name: signupData.organization_short_name,
        country: signupData.country.toLowerCase(),
        password: formData.password,
        otp: formData.otp
      };

      await completeRegistration(payload);

      // Clear signup data after successful registration
      localStorage.removeItem('eloope_signup_data');
    } catch (error) {
      console.error("Registration error:", error);
    }
  };

  // Format countdown for display
  const formattedCountdown = `${countdown.minutes.toString().padStart(2, '0')}:${countdown.seconds.toString().padStart(2, '0')}`;
  const isCountdownActive = countdown.minutes > 0 || countdown.seconds > 0;

  return (
    <div className="p-10 md:px-10 rounded-xl border bg-white dark:bg-background h-[80vh] 2xl:h-[60vh] text-card-foreground shadow mx-auto w-full md:w-[45%] 2xl:w-[45%] 2xl:px-20 flex flex-col justify-center">
      <div className="flex flex-col items-center justify-center">
        <h3 className='font-semibold text-2xl mb-4'>
          Create Password
        </h3>
        <p className='text-sm text-center text-gray-600'>
          Please enter the 6 digit code sent to <span className='font-medium text-gray-950'>{email}</span>
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4 pt-4">
        <div className="flex flex-col">
          <FloatingLabelInput
            id="otp"
            value={formData.otp}
            className={`placeholder:text_gray ${backendErrors.otp ? "validate_input" : ""}`}
            name="otp"
            onChange={handleChange}
            label="Enter OTP"
            type="text"
            maxLength={6}
          />
          <ErrorMessage errors={backendErrors} field="otp" />
        </div>

        <div>

          <div className="flex flex-col gap-3 relative">
            <FloatingLabelInput
              id="password"
              value={formData.password}
              className={`placeholder:text_gray ${backendErrors.password ? "validate_input" : ""}`}
              name="password"
              onChange={handleChange}
              label="New Password"
              type={toggle.password ? "text" : "password"}
            />
            <span
              className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
              onClick={() => togglePasswordVisibility('password')}
            >
              {toggle.password ? (
                <EyeOff size={16} className="text-gray-500" />
              ) : (
                <Eye size={16} className="text-gray-500" />
              )}
            </span>
          </div>
          <ErrorMessage errors={backendErrors} field="password" />
        </div>


        <div>

          <div className="flex flex-col gap-3 relative">
            <FloatingLabelInput
              id="confirm_password"
              value={formData.confirm_password}
              className={`placeholder:text_gray ${backendErrors.confirm_password ? "validate_input" : ""}`}
              name="confirm_password"
              onChange={handleChange}
              label="Confirm Password"
              type={toggle.confirm_password ? "text" : "password"}
            />
            <span
              className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
              onClick={() => togglePasswordVisibility('confirm_password')}
            >
              {toggle.confirm_password ? (
                <EyeOff size={16} className="text-gray-500" />
              ) : (
                <Eye size={16} className="text-gray-500" />
              )}
            </span>
          </div>
          <ErrorMessage errors={backendErrors} field="confirm_password" />
        </div>


        <div className="flex justify-center mt-4">
          {isCountdownActive ? (
            <p className="text-sm text-center">Resend code in {formattedCountdown}</p>
          ) : (
            <Button
              type="button"
              variant="ghost"
              onClick={handleResendOtp}
              disabled={isResending}
            >
              {isResending ? (
                <span><ButtonLoader color="#000000" /></span>
              ) : (
                "Resend OTP"
              )}
            </Button>
          )}
        </div>

        <Button
          type="submit"
          className="w-full mt-6"
          size="xs"
        >
          {(isSubmitting) ? (
            <span><ButtonLoader color="#FFFFFF" /></span>
          ) : (
            'Create Password'
          )}
        </Button>
      </form>
    </div>
  );
};

// Main component with Suspense boundary for useSearchParams
export default function CreatePassword() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <CreatePasswordContent />
    </React.Suspense>
  );
}
