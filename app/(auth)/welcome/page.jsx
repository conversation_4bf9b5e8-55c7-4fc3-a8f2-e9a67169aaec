import { But<PERSON> } from '@/components/ui/button';
import React from 'react'
import { Check, Star } from "lucide-react"
import Link from 'next/link';

export default function Welcome () {
  return (
    <>
      <div className="">
        <div className="p-10 md:px-10 rounded-xl border bg-card-gray text-card-foreground shadow mx-auto w-full h-full lg:h-[60vh] md:w-[45%] 2xl:w-[45%] 2xl:px-20 flex flex-col justify-center">

          <div className="flex flex-col space-y-1.5 pb-6 text-center">
            <h3 className="md:font-semibold tracking-tight text-3xl text-center">Welcome to Eloope</h3>
          </div>

          <div className="flex items-center gap-2 bg-slate-100 w-fit p-1 px-3 rounded-full text-xs text-card-foreground mb-4">
            <Star size={14} strokeWidth={1.6}/>
            <span>Recommended</span>
          </div>

          <div>
            <p className=" text-start text-lg font-semibold textTitle">We highly encourage setting up <br /> your base currency</p>
          </div>

          <div className='mt-4 text-sm text-muted-foreground space-y-3'>
            <div className="flex items-center gap-2">
              <Check size={"16px"} />
              <p>Ensures accurate financial reporting</p>
            </div>

            <div className="flex items-center gap-2">
              <Check size={"16px"} />
              <p>Simplifies international transactions</p>
            </div>

          </div>

          <div className="">
            <div className="space-y-4">


              <div className='flex items-center justify-center pt-6'>
                <Link href="/dashboard" className='w-full'>
                  <Button className="w-full" size="lg">
                    Get Started
                  </Button>
                </Link>
              </div>

              <p className="text-center text-sm">Don’t want to set up currency right now?</p>
              <p className="text-center text-xs text-muted-foreground">You can set it at anytime thorugh Account Settings &gt; Currencies</p>

              <div className='flex items-center justify-center .pt-6'>
                <Link href="/admin-settings" className='w-full'>
                  <Button className="w-full" size="lg" variant="outline">
                    Admin Settings
                  </Button>
                </Link>
              </div>

            </div>

          </div>

        </div>
      </div>
    </>
  )
}