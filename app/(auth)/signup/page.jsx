"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import React, { useState, useEffect } from 'react'
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from '@/components/ui/label';
import { useMutation } from '@tanstack/react-query';
import { Registration } from '/apis/auth';
import toast from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import Link from 'next/link';
import Image from 'next/image';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import IntlTelInput from 'react-intl-tel-input';
import 'react-intl-tel-input/dist/main.css';

export default function Signup() {

  const router = useRouter()
  const [formData, setFormData] = useState({
    // for concatenation
    firstName: "",
    lastName: "",
    //
    name: "",
    email: "",
    phone: "",
    organization_name: "",
    organization_short_name: "",
    country: "united state" // Default country
  })

  const [backendErrors, setBackendErrors] = useState({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Add useEffect to handle client-side rendering for the IntlTelInput
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value })
    setBackendErrors({ ...backendErrors, [name]: null });
  }

  // Handle phone input change
  const handlePhoneChange = (isValid, value, selectedCountryData, fullNumber) => {
    setFormData(prev => ({
      ...prev,
      phone: fullNumber,
      country: selectedCountryData.name // Update country when phone country changes
    }));
    setBackendErrors({ ...backendErrors, phone: null });
    console.log("selectedCountryData", selectedCountryData)
  }

  const { mutateAsync: signup, isPending } = useMutation({
    mutationFn: async (data) => {
      const payload = {
        name: `${data.firstName} ${data.lastName}`.trim(),
        email: data.email,
        phone: data.phone,
        organization_name: data.organization_name,
        organization_short_name: data.organization_short_name,
        country: data.country.toLowerCase() // Ensure country is lowercase
      };
      return Registration(payload);
    }
  });

  const handleSignUp = async (e) => {
    e.preventDefault();

    if (!termsAccepted) {
      toast.error("Please accept the terms and conditions");
      return;
    }

    try {
      const response = await signup(formData)

      if (response) {
        toast.success(response.message);

        // Store all user data in a single object
        const signupData = {
          email: formData.email,
          fullName: `${formData.firstName} ${formData.lastName}`.trim(),
          phone: formData.phone,
          organization_name: formData.organization_name,
          organization_short_name: formData.organization_short_name,
          country: formData.country.toLowerCase(),
          // Store the timer from the backend (in minutes)
          timer: response.data?.timer || 10, // Default to 10 minutes if not provided
          // Store the timestamp when this data was saved
          timestamp: Date.now()
        };

        // Store the single object in localStorage
        localStorage.setItem('eloope_signup_data', JSON.stringify(signupData));

        // Redirect to create password page
        router.push('/create-password');
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error("Signup error:", error);
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error.message || "Signup failed");
    }
  }

  return (
    <div>

      <div className="flex flex-col justify-center .p-4 md:px-10 2xl:px-20 rounded-xl border bg-[#FFFFFF] dark:bg-background text-card-foreground shadow mx-auto w-full md:w-[45%] 2xl:w-[45%] h-full 2xl:h-[60vh]">

        <div className="flex flex-col space-y-1.5 p-6 text-center">
          <h3 className="md:font-semibold tracking-tight text-base md:text-2xl text-center">Sign up as an organization</h3>
          <p className="text-xs text-muted-foreground">Enter your email below to login to your account</p>

          <div className='flex items-center justify-center py-4'>
            <Button className="w-full gap-3" size="lg" variant="outline">
              <Image src='/images/icons/google.svg' width={20} height={20} alt='google icon' />
              <span>
                Login with Google
              </span>
            </Button>
          </div>

          <div className="flex items-center">
            <hr className='w-full' />
            <span className='px-4'>or</span>
            <hr className='w-full' />
          </div>

        </div>

        <form onSubmit={handleSignUp} className="p-6 pt-0">
          <div className="space-y-4">

            <div className="flex flex-col">
              <FloatingLabelInput id="firstname" value={formData.firstName} onChange={handleChange} name="firstName" label="First Name" type="text" />
              <ErrorMessage errors={backendErrors} field="firstname" />
            </div>

            <div className="flex flex-col">
              <FloatingLabelInput id="lastname" value={formData.lastName} onChange={handleChange} name="lastName" label="Last Name" type="text" />
              <ErrorMessage errors={backendErrors} field="lastname" />
            </div>

            <div className="flex flex-col">
              <FloatingLabelInput id="email" value={formData.email} className="placeholder:text_gray" name="email" onChange={handleChange} label="Email Address" type="email" />
              <ErrorMessage errors={backendErrors} field="email" />
            </div>

            <div className="flex flex-col">
              <div className="relative">
                {isClient && (
                  <IntlTelInput
                    containerClassName="intl-tel-input"
                    inputClassName="form-control w-full h-8 rounded-lg border border-input px-3 py-1 text-sm"
                    preferredCountries={['us']}
                    defaultCountry={formData.country}
                    onPhoneNumberChange={(isValid, value, selectedCountryData, fullNumber) => {
                      handlePhoneChange(isValid, value, selectedCountryData, fullNumber);
                    }}
                    format={true}
                    // placeholder="Phone number"
                  />
                )}
              </div>
              <ErrorMessage errors={backendErrors} field="phone" />
            </div>

            <div className="flex flex-col">
              <FloatingLabelInput id="organization_name" value={formData.organization_name} className="placeholder:text_gray" name="organization_name" onChange={handleChange} label="Organization Name" type="text" />
              <ErrorMessage errors={backendErrors} field="organization_name" />
            </div>

            <div className="relative">

              <div className="h-8 flex items-center border rounded-lg overflow-hidden font-light">
                <div className="px-4 py-2 bg-gray-100 dark:bg-background text-gray-500 w-1/2 text-start text-sm">
                  .eloope.com/expense
                </div>
                <input
                  type="text"
                  value={formData.organization_short_name}
                  onChange={handleChange}
                  name='organization_short_name'
                  placeholder="company-slug"
                  className="px-4 py-2 placeholder:font-medium placeholder:text_gray placeholder:text-xs focus:outline-none w-1/2"
                />

              </div>

              <ErrorMessage errors={backendErrors} field="organization_short_name" />

            </div>


            <div className='flex items-center justify-center'>
              <Button
                disabled={isPending}
                type="submit"
                className="w-full" 
                size="xs"
              >
                {isPending ? (
                  <span>
                    <ButtonLoader color="#FFFFFF" />
                  </span>
                ) : (
                  <span>
                    Create your Eloope Account
                  </span>
                )}
              </Button>
            </div>

          </div>

          <div className='mt-4 flex justify-center text-center'>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={termsAccepted}
                onCheckedChange={setTermsAccepted}
              />
              <Label htmlFor="terms" className="text-[11px] font-normal">
                By confirming your email, you agree to our Terms of Service and Privacy Policy
              </Label>
            </div>
          </div>

          <div className="mt-6 text-center text-sm">
            Already have an account? <Link className="underline" href="/"> Login</Link>
          </div>

        </form>

      </div>

    </div>
  )
}
