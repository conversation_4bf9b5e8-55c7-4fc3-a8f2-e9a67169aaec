"use client"

import { ForgotPasswordApi } from '@/apis/auth';
import { Button } from '@/components/ui/button';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react'
import toast from 'react-hot-toast';

const Page = () => {

  const navigate = useRouter()

  const [email, setEmail] = useState("");

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["forgotpassword"],
    mutationFn: ForgotPasswordApi,
  });

  const handleForgotPassword = async (e) => {
    e.preventDefault();
    try {
      if (!email) {
        toast.error("Email is not available. Please try again.");
        return;
      }

      const response = await mutateAsync({ email });
      toast.success(response?.message || "Password reset link has been sent to your email");
      
      // No need to redirect - user will click link in email
      // Just show success message and optionally clear the form
      setEmail("");
    } catch (error) {
      console.log(error);
      toast.error(`${error.message}`);
    }
  };

  const check = !email || isPending

  return (
    <>
      <div className="">
        <div className="p-10 md:px-10 rounded-xl border bg-[#FFFFFF] dark:bg-background h-[80vh] 2xl:h-[60vh] text-card-foreground shadow mx-auto w-full md:w-[45%] 2xl:w-[45%] 2xl:px-20 flex flex-col justify-center">

          <div className='flex flex-col items-center'>
            <p className=" text-xl font-semibold">Forgot Password</p>
            <span className='text-xs text-center my-6 md:px-2'>Enter your email address and we’ll send you a link to reset your password.</span>
          </div>

          <div className="">
            <div className="space-y-4">

              <form onSubmit={handleForgotPassword}>
                <div className="space-y-4">

                  <div className="flex flex-col gap-3">
                    <FloatingLabelInput id="email" value={email} className="placeholder:text_gray" name="email" onChange={(e)=>setEmail(e.target.value)} label="Email Address" type="email" />
                  </div>

                  <div className='flex items-center justify-center'>
                    <Button
                      disabled={check}
                      type="submit"
                      className={`w-full ${check ? 'btn_opacity' : ''}`}
                      size="xs"
                    >
                      {isPending ? (
                        <span>
                          <ButtonLoader color="#FFFFF" />
                        </span>

                      ) : (
                        <span>
                          Proceed
                        </span>

                      )}
                    </Button>
                  </div>

                </div>

              </form>

            </div>

          </div>

        </div>
      </div>
    </>
  )
}

export default Page;
