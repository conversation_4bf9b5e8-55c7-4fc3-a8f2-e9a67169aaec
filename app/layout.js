import "./globals.css";
import Progress from "./progress";
import ReactQueryProvider from "@/utils/providers/ReactQueryProvider";
import { ThemeProvider } from "@/components/reusables/theme-provider";
import { geistSans } from "./fonts/fonts";

export const metadata = {
  title: "Eloope",
  description: "Expense Management System Website",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={`${geistSans.variable}`}>
      <body
        className={`font-sans dark:bg-secondary antialiased p-0 m-0`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Progress>
            <ReactQueryProvider>
              {children}
            </ReactQueryProvider>
          </Progress>
        </ThemeProvider>
      </body>
    </html>
  );
}
