"use client";

import Link from "next/link";
import React, { useState, useEffect } from "react";
import {
  Bell,
  CircleUser,
  LayoutDashboard,
  Search,
  Sun,
} from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DSidebar } from "@/components/reusables/DSidebar";
import { MSidebar } from "@/components/reusables/MSidebar";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useQuery } from "@tanstack/react-query";
import { getProfileApi } from "@/apis/profile-management";
import { Notification } from "./components/notification/Notification";
import { ModeToggle } from "./components/mode/Mode";
import { OnboardingModal } from "./components/onboarding/OnboardingModal";
import Image from "next/image";

export default function Dashboardlayout({ children }) {
  const navigate = useRouter();
  const [collapsed, setCollapsed] = useState(false);
  const [isOnboardingModalOpen, setIsOnboardingModalOpen] = useState(false);

  const handleLogout = (e) => {
    e.preventDefault();
    if (typeof window !== "undefined") {
      localStorage.removeItem('Eloope_UserData');
      localStorage.removeItem('EloopeToken');
      localStorage.removeItem('lastPath')
      localStorage.removeItem('eloope_signup_data')
    }
    toast.success("You've successfully logged out");
    navigate.push("/");
  };

  const { data: myProfile } = useQuery({
    queryKey: ["profile-settings"],
    queryFn: getProfileApi,
  });

  // Check onboarding status and show modal instead of redirecting
  useEffect(() => {
    if (myProfile?.data?.profile && myProfile.data.profile.has_completed_onboarding === false) {
      setIsOnboardingModalOpen(true);
    }
  }, [myProfile]);

  return (
    <>
      <div className="flex h-screen overflow-hidden bg-white dark:bg-background">
        {/* Fixed Sidebar */}
        <div className={`hidden md:flex flex-col fixed left-0 top-0 h-screen bg-white dark:bg-background border-r
          ${collapsed ? "w-[64px]" : "w-[220px] lg:w-[260px]"}
          transition-all duration-300`}
        >
          {/* Header */}
          <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
            <Link href="/dashboard" className="flex items-center gap-2 font-semibold">
              <LayoutDashboard className="h-6 w-6" />
              {!collapsed && <span>Eloope</span>}
            </Link>
          </div>

          {/* Sidebar */}
          <DSidebar collapsed={collapsed} setCollapsed={setCollapsed} />
        </div>

        {/* Main Content Area */}
        <div className={`flex-1 flex flex-col min-h-screen
          ${collapsed ? "md:ml-[64px]" : "md:ml-[220px] lg:ml-[260px]"}
          transition-all duration-300`}
        >
          {/* Fixed Header */}
          <header className="sticky top-0 z-40 flex h-14 items-center gap-4 border-b px-4 .lg:h-[60px] lg:px-6 bg-white dark:bg-background backdrop-blur-md shadow-sm">
            <MSidebar />

            <div className="flex items-center justify-end w-full gap-2">
              <div className="flex items-center gap-2">

                <Notification />

                <ModeToggle />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <div className="flex items-center gap-3 cursor-pointer">
                    {myProfile?.data?.profile?.profile_picture ? (
                      <Image
                        src={myProfile?.data?.profile?.profile_picture}
                        alt="Profile"
                        className="h-8 w-8 rounded-full object-cover"
                        width={32}
                        height={32}
                        unoptimized={true}
                      />
                    ) : (
                      <div className="flex items-center justify-center p-2 font-bold text-xs text-white dark:text-background rounded-full h-8 w-8 bg-foreground dark:bg-primary">
                        {myProfile?.data?.initials}
                      </div>
                    )}
                    <div className="flex items-end flex-col justify-center">
                      <span className="text-[12px]">{myProfile?.data?.name}</span>
                      <span className="text-[8px] text-gray-600 dark:text-primary">
                        {myProfile?.data?.profile?.roles?.join(", ")}
                      </span>
                    </div>
                  </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="text-xs">
                  <DropdownMenuLabel className="text-xs">My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <Link href={"/profile-settings"}>
                    <DropdownMenuItem className="text-xs">Profile</DropdownMenuItem>
                  </Link>
                  <Link href={"/notifications"}>
                    <DropdownMenuItem className="text-xs">Notification</DropdownMenuItem>
                  </Link>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="text-xs" onClick={handleLogout}>
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Scrollable Main Content */}
          <main className="flex-1 overflow-auto dark:bg-background">
            {children}
          </main>
        </div>
      </div>

      {/* Onboarding Modal */}
      <OnboardingModal
        isOpen={isOnboardingModalOpen}
        onClose={() => setIsOnboardingModalOpen(false)}
      />
    </>
  );
}
