import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import React, { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { changePasswordApi } from "@/apis/profile-management";
import ButtonLoader from "@/utils/spinner/ButtonLoader";

export const ChangeAdminPassword = ({ isOpen, onClose }) => {

  const [toggle, setToggle] = useState({
    old_password: false,
    new_password: false,
    confirm_new_password: false,
  })

  const handleTogglePasswords = (key) => {
    setToggle(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const [formData, setFormData] = useState({
    old_password: "",
    new_password: "",
  });

  const [confirmnewpassword, setconfirmnewpassword] = useState("")

  const { mutateAsync: changePasswordMutation, isPending: isChanging } = useMutation({
    mutationFn: changePasswordApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Password Changed successfully");
      onClose();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to change password");
    },
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (formData.new_password.length < 8 || formData?.old_password < 8) {
      toast.error("password or new password must be at least 8 characters long.");
      return; // Stop the submission
    }

    // Check if new password and confirm new password match
    if (formData.new_password !== confirmnewpassword) {
      toast.error("New password does not matches with confirm new password.");
      return; // Stop the submission
    }

    try {
      const response = await changePasswordMutation(formData);
      console.log(response);
      toast.success(response?.message || "Password Changed successfully");
      onClose();
    } catch (error) {
      console.log(error)
    }
  }

  const check = !formData.new_password || !formData.old_password || !confirmnewpassword || isChanging

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 pt-4">

            <div className="flex flex-col gap-3 relative">
              <FloatingLabelInput id="password" value={formData.old_password} className="placeholder:text_gray" name="password" onChange={(e) => setFormData(prev => ({ ...prev, old_password: e.target.value }))} label="Password" type={toggle.old_password ? "text" : "password"} />
              <span
                className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                onClick={() => handleTogglePasswords("old_password")}
              >
                {toggle.old_password ? (
                  <EyeOff size={16} className="text-gray-500" />
                ) : (
                  <Eye size={16} className="text-gray-500" />
                )}
              </span>
            </div>

            <div className="flex flex-col gap-3 relative">
              <FloatingLabelInput id="newpassword" value={formData.new_password} className="placeholder:text_gray" name="new_password" onChange={(e) => setFormData(prev => ({ ...prev, new_password: e.target.value }))} label="New Password" type={toggle.new_password ? "text" : "password"} />
              <span
                className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                onClick={() => handleTogglePasswords("new_password")}
              >
                {toggle.new_password ? (
                  <EyeOff size={16} className="text-gray-500" />
                ) : (
                  <Eye size={16} className="text-gray-500" />
                )}
              </span>
            </div>

            <div className="flex flex-col gap-3 relative">
              <FloatingLabelInput id="confirm_newpassword" value={confirmnewpassword} className="placeholder:text_gray" label="Confirm New Password" type={toggle.confirm_new_password ? "text" : "password"} onChange={(e) => setconfirmnewpassword(e.target.value)} />
              <span
                className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                onClick={() => handleTogglePasswords("confirm_new_password")}
              >
                {toggle.confirm_new_password ? (
                  <EyeOff size={16} className="text-gray-500" />
                ) : (
                  <Eye size={16} className="text-gray-500" />
                )}
              </span>
            </div>

          </div>

          {/* Submit and Cancel Buttons */}
          <div className="flex items-center mt-4 gap-2">
            <Button
              size="lg"
              type="submit"
              className="gap-1 text-xs p-3 w-full"
              disabled={check}
            >
              {isChanging ? (
                <React.Fragment>
                  <ButtonLoader color={"text-white"}/>
                  <span>Changing...</span>
                </React.Fragment>
              ) : (
                <span>Change Password</span>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}