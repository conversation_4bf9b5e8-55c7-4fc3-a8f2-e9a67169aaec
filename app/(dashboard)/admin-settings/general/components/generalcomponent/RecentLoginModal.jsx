import { loginActivity } from '@/apis/profile-management';
import { TableComponent } from '@/components/reusables/table'
import { Checkbox } from '@/components/ui/checkbox';
import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useState } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { formatDate } from '@/utils/Utils';
import { Button } from '@/components/ui/button';
import { LogoutAllApi } from '@/apis/auth';
import toast from 'react-hot-toast';
import ButtonLoader from '@/utils/spinner/ButtonLoader';

const RecentLoginModal = ({ isOpen, onClose, apiResponse, isLoading, refetch }) => {
  const [backendErrors, setBackendErrors] = useState({});

  // const { data: apiResponse, isLoading, refetch } = useQuery({
  //   queryKey: ["listOfLoginActivity"],
  //   queryFn: loginActivity,
  // });

  const { mutateAsync: logOutAllMutation, isPending } = useMutation({
    mutationKey: ["logout-all"],
    mutationFn: LogoutAllApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Successfully logged out from all devices");
      refetch(); // Refresh the login activity data
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to logout from all devices");
    }
  });

  const handleLogoutAll = async () => {
    try {
      await logOutAllMutation();
    } catch (error) {
      console.error("Error logging out from all devices:", error);
    }
  };

  const rows = apiResponse?.results;

  const columns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      ),
      sortable: false,
    },
    {
      title: "Device",
      accessor: (row) => row.device,
      sortable: true,
    },

    {
      title: "Browser",
      accessor: (row) => row.browser,
      sortable: true,
    },

    {
      title: "Timestamp",
      accessor: (row) => formatDate(row.timestamp),
      sortable: true,
    },

  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className=".my-4 w-[90%] h-96 sm:max-w-[50rem] overflow-scroll">
        
        <div className='!min-w-full flex items-center justify-between'>
          <span>Login Activity</span>
          <span>
            <Button 
              variant='outline' 
              onClick={handleLogoutAll}
              disabled={isPending}
            >
              {isPending ? (
                <span className="flex items-center gap-2">
                  <ButtonLoader color="#000000" /> <span>Logging out...</span>
                </span>
              ) : (
                "Log Out From All Devices"
              )}
            </Button>
          </span>
        </div>

        <div className='no-scrollbar'>
          <TableComponent
            rows={rows}
            columns={columns}
            tableTitle={""}
            // onRowClick={(row) => router.push(`/reports/${row?.id}`)}
            NoavailableTitle={`Login Activity`}
            showImportExport={false}
            isLoading={isLoading}
            showColumnFilter={false}
            showFilter={false}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default RecentLoginModal