"use client"

import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { Circle<PERSON>he<PERSON>, MapPin, Monitor } from "lucide-react";
import { useState, useEffect } from "react";
import RecentLoginModal from "./RecentLoginModal";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { onboardingChoicesApi, timezoneList } from "@/apis/utilapi";
import ButtonLoader from "@/utils/spinner/ButtonLoader";
import { Button } from "@/components/ui/button";
import { updateOrgData } from "@/apis/settings";
import toast from "react-hot-toast";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { Label } from "@/components/ui/label";
import { SearchableSelect } from '@/components/reusables/SearchableSelect';
import { loginActivity } from "@/apis/profile-management";
import { formatDate } from "@/utils/Utils";

export const Information = ({ orgData }) => {
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);
  const [backendErrors, setBackendErrors] = useState({});

  console.log("orgData", orgData)

  const [formData, setFormData] = useState({
    name: orgData?.name,
    short_name: orgData?.short_name,
    website: orgData?.website,
    recovery_phone: orgData?.recovery_phone,
    timezone: orgData?.timezone?.id,
    country: orgData?.country,
    default_currency: orgData?.default_currency
  });

  // Add state to track changed fields
  const [changedFields, setChangedFields] = useState({});

  // Update form data when orgData changes
  useEffect(() => {
    if (orgData) {
      setFormData({
        name: orgData?.name || "",
        short_name: orgData?.short_name || "",
        website: orgData?.website || "",
        recovery_phone: orgData?.recovery_phone || "",
        timezone: orgData?.timezone?.id ? String(orgData.timezone.id) : "",
        country: orgData?.country || "",
      });
    }
  }, [orgData]);

  // Update handleChange function
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Track that this field was changed
    setChangedFields(prev => ({
      ...prev,
      [field]: true
    }));
  };

  const { data: timezoneData, isLoading: isLoadingTimezone } = useQuery({
    queryKey: ["listOfTimezone"],
    queryFn: timezoneList,
  });

  const { data: choicesData, isLoading: isLoadingCountries } = useQuery({
    queryKey: ["choices"],
    queryFn: onboardingChoicesApi,
  });

  const { data: apiResponse, isLoading, refetch } = useQuery({
    queryKey: ["listOfLoginActivity"],
    queryFn: loginActivity,
  });

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["update-orgdata"],
    mutationFn: updateOrgData,
    onSuccess: (data) => {
      toast.success(data?.message || "Company information updated successfully");
      queryClient.invalidateQueries(["org-detail"])
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to update company information");
    }
  });

  // Update form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setBackendErrors({});

    try {
      // Create payload with only changed fields
      const updatedFields = Object.keys(changedFields).reduce((acc, field) => {
        if (changedFields[field]) {
          acc[field] = formData[field];
        }
        return acc;
      }, {});

      // Only submit if there are changes
      if (Object.keys(updatedFields).length > 0) {
        await mutateAsync(updatedFields);
      } else {
        toast.error("No changes made");
      }
    } catch (error) {
      console.error("Error updating organization:", error);
    }
  };

  const getFirstLogin = apiResponse?.results[0]

  return (
    <>
      <div className="flex flex-col sm:flex-row w-full items-start gap-4 my-6">
        <div className="sm:w-3/4">
          <h2 className="text-sm font-semibold text-gray-900 mb-4">
            General Information
          </h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>

              <FloatingLabelInput
                label="Company Name"
                id="company-name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                className={`${backendErrors.name && "validate_input"}`}
                type="text"
              />

              <ErrorMessage errors={backendErrors} field="name" />

            </div>


            <div>
              <FloatingLabelInput
                label="Short Name"
                id="short-name"
                value={formData.short_name}
                onChange={(e) => handleChange("short_name", e.target.value)}
                className={`${backendErrors.short_name && "validate_input"}`}
                type="text"
              />
              <ErrorMessage errors={backendErrors} field="short_name" />
            </div>

            <div>
              <FloatingLabelInput
                label="Recovery Phone"
                id="recovery_phone"
                value={formData.recovery_phone}
                onChange={(e) => handleChange("recovery_phone", e.target.value)}
                className={`${backendErrors.recovery_phone && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="recovery_phone" />
            </div>


            <div>
              <FloatingLabelInput
                label="Website"
                id="website"
                value={formData.website}
                onChange={(e) => handleChange("website", e.target.value)}
                className={`${backendErrors.website && "validate_input"}`}
                type="url"
              />
              <ErrorMessage errors={backendErrors} field="website" />
            </div>

            <div className="relative">
              <Label className="text-xs">Timezone</Label>
              <div className="relative">
                <SearchableSelect
                  value={formData.timezone}
                  onValueChange={(value) => handleChange("timezone", value)}
                  searchPlaceholder="Search timezone..."
                  placeholder="Select Timezone"
                  items={timezoneData?.results}
                  error={backendErrors.timezone}
                  isLoading={isLoadingTimezone}
                />
              </div>
              <ErrorMessage errors={backendErrors} field="timezone" />
            </div>


            <div className="relative">
              <Label className="text-xs">Country</Label>
              <div className="relative">
                <SearchableSelect
                  value={formData.country}
                  onValueChange={(value) => handleChange("country", value)}
                  searchPlaceholder="Search country..."
                  placeholder="Select Country"
                  items={choicesData?.data?.countries}
                  error={backendErrors.country}
                  isLoading={isLoadingCountries}
                />
              </div>
              <ErrorMessage errors={backendErrors} field="country" />
            </div>

            <div className="relative">
              <FloatingLabelInput
                label="Default currency"
                id="default_currency"
                value={formData.default_currency}
                disabled
              />
            </div>




            <div className='flex items-center justify-end'>
              <Button
                type="submit"
                className={` ${isPending ? 'btn_opacity' : ''}`}
                size="xs"
                disabled={isPending}
              >
                {isPending ? (
                  <span className="flex items-center gap-2">
                    <ButtonLoader color="#FFFFFF" /> <span>Updating...</span>
                  </span>
                ) : (
                  <span>
                    Update
                  </span>
                )}
              </Button>
            </div>
          </form>
        </div>

        <div className="sm:w-1/3">
          <h2 className="text-sm font-semibold text-gray-900 mb-3">
            Quick Settings
          </h2>
          <div className="space-y-4 rounded-xl shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <CircleCheck className="w-4 h-4" />
              <div>
                <p className="text-xs font-medium">
                  Change Password
                </p>
                <p className="text-xs text-gray-500">Change</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Monitor className="w-4 h-4" />
              <div className="text-xs">
                <p className="font-medium">Recent Login</p>
                <div className="text-gray-500">
                  <p>{getFirstLogin?.device}, {getFirstLogin?.browser}</p>
                  {/* <p>06/03/2024</p> */}
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span>{formatDate(getFirstLogin?.timestamp)}</span>
                  </div>
                  <p className="cursor-pointer text-blue-500"
                    onClick={() => setIsOpen(true)}
                  >See Recent Login</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isOpen && (
        <RecentLoginModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          apiResponse={apiResponse}
          isLoading={isLoading}
          refetch={refetch}
        />
      )}
    </>
  );
}