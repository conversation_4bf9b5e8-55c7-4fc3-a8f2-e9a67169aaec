"use client"

import { But<PERSON> } from "@/components/ui/button"
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input"
import { Check, Phone, SquarePen, User } from "lucide-react"
import { useState } from "react";
import { ChangeAdminPassword } from "../ChangeAdminPassword";
import { ChangeAdminEmail } from "../settingsmodal/ChangeAdminEmail";
import { Changepassword } from "@/app/(dashboard)/profile-settings/components/Changepassword";

export const LoginInformation = ({ orgDetail }) => {
  const [open, setOpen] = useState(false);
  const [openChangeEmail, setOpenChangeEmail] = useState(false);

  return (
    <>
      <div>
        <div className="py-6">
          <h2 className="text-sm font-semibold mb-4">
            Login Information
          </h2>
          <div className="space-y-4">

            <div className="flex items-center justify-between gap-4 py-3">
              <div className="relative w-full">
                <FloatingLabelInput
                  label="Primary Email"
                  id="primary-email"
                  defaultValue={orgDetail?.email || ""}
                  disabled
                  className="!border"
                />
                {/* <span className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer">
                  <SquarePen size={16} className="text-gray-500" />
                </span> */}
              </div>

              <div>
                <Button variant="ghost" className="flex items-center gap-2 text-xs">
                  <Check size={15} />
                  Verified
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between gap-4 py-3">
              <div className="relative w-full">
                <FloatingLabelInput
                  label="Password"
                  id="password"
                  type="password"
                  defaultValue="********"
                  disabled
                />
              </div>

              <div>
                <div
                  onClick={() => setOpen(true)}
                  className="whitespace-nowrap cursor-pointer flex items-center gap-2 text-sm text-blue-600">
                  Edit Password
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div className="my-4 space-y-4">

        <div className="flex items-center justify-between py-3">
          <div className="flex items-center space-x-3">
            <User className="w-5 h-5 text-gray-500" />
            <div className="space-y-2">
              <p className="text-sm">Primary Contact</p>
              <p className="text-xs">
                {orgDetail?.contact?.name} <br /> {orgDetail?.contact?.email}
              </p>
            </div>
          </div>
          <button
            onClick={() => setOpenChangeEmail(true)}
            className="text-blue-600 text-xs font-medium">Change Primary Contact</button>
        </div>

      </div>


      {open && (
        <Changepassword
          isOpen={open}
          onClose={() => setOpen(false)}
        />
      )}

      {openChangeEmail && (
        <ChangeAdminEmail
          isOpen={openChangeEmail}
          onClose={() => setOpenChangeEmail(false)}
        />
      )}
    </>
  )
}