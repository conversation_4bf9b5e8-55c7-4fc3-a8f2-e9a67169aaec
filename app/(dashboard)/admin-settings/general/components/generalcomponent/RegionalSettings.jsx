import { currencyList, onboardingChoicesApi, timezoneList } from "@/apis/utilapi";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";

export const RegionalSettings = ({ orgData }) => {

  const { data: currencyData } = useQuery({
    queryKey: ["listOfcurrencies"],
    queryFn: currencyList,
  });

  const { data: timezoneData } = useQuery({
    queryKey: ["listOfTimezone"],
    queryFn: timezoneList,
  });

  const { data: choicesData } = useQuery({
    queryKey: ["choices"],
    queryFn: onboardingChoicesApi,
  });


  return (
    <>
      <div className="flex items-center gap-4 my-6">
        <div className="w-3/4 py-6">
          <h2 className="text-sm font-semibold text-gray-900 mb-4">
            Regional Settings
          </h2>
          <div className="space-y-4">
            <div className="relative">

              <Select
              // onValueChange={(value) => handleChange("roles", [parseInt(value)])}
              >
                <SelectTrigger className={`text-gray-500 text-xs`}>
                  <SelectValue placeholder="Time Zone" />
                </SelectTrigger>
                <SelectContent className="h-40">
                  {timezoneData?.results?.map((item) => (
                    <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                      {item?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

            </div>

            <div className="relative">

              <Select
              // onValueChange={(value) => handleChange("roles", [parseInt(value)])}
              >
                <SelectTrigger className={`text-gray-500 text-xs`}>
                  <SelectValue placeholder="Country" />
                </SelectTrigger>
                <SelectContent className="h-40">
                  {choicesData?.data?.countries?.map((item) => (
                    <SelectItem className="text-xs" key={item?.value} value={item?.value}>
                      {item?.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

            </div>

            <div className="relative">
              <Select defaultValue={orgData.default_currency || "USD"}>
                <SelectTrigger className="text-gray-500">
                  <SelectValue placeholder="Select Currency" />
                </SelectTrigger>
                <SelectContent>
                </SelectContent>
              </Select>
            </div>

          </div>
        </div>
      </div>
    </>
  )
}