import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { useMutation, useQuery } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import React, { useState } from "react";
import { Checkbox } from "@/components/ui/checkbox"
import { TableComponent } from "@/components/reusables/table";
import { usersApi } from "@/apis/admin/users";
import { updateOrgData } from "@/apis/settings";
import { useQueryClient } from "@tanstack/react-query";

export const ChangeAdminEmail = ({ isOpen, onClose }) => {
  const queryClient = useQueryClient();
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfEmployee"],
    queryFn: () => usersApi({ invitation_status: "approved" }),
  });

  const [selectedUser, setSelectedUser] = useState(null);

  const handleCheckboxChange = (row) => {
    setSelectedUser(selectedUser?.id === row.id ? null : row);
  };

  // Add mutation
  const { mutateAsync: updateSettings, isPending } = useMutation({
    mutationFn: updateOrgData,
    onSuccess: (data) => {
      toast.success(data?.message || "Admin email updated successfully");
      queryClient.invalidateQueries(["organizationSettings"]);
      onClose();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to update admin email");
    }
  });

  const handleSave = async () => {
    if (!selectedUser) {
      toast.error("Please select a user first");
      return;
    }

    try {
      await updateSettings({
        contact: selectedUser.id
      });
    } catch (error) {
      console.error("Error updating admin email:", error);
    }
  };

  const columns = [
    {
      id: "select",
      header: "",
      cell: ({ row }) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedUser?.id === row.original.id}
            onCheckedChange={() => handleCheckboxChange(row.original)}
          />
        </div>
      )
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original?.name
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: ({ row }) => row.original?.email
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[50rem] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-sm font-semibold">
            Change Admin Email
          </DialogTitle>
        </DialogHeader>

        <div className="no-scrollbar">
          <TableComponent
            rows={apiResponse?.results || []}
            columns={columns}
            // tableTitle="Organization Contacts"
            showImportExport={false}
            NoavailableTitle={isLoading ? "Loading..." : "Contacts"}
            showColumnFilter={false}
            showFilter={false}
          />
        </div>

        <div className="flex items-center justify-end p-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="xs" onClick={onClose}>
              Cancel
            </Button>

            <Button
              variant="default"
              size="xs"
              onClick={handleSave}
              disabled={isPending || !selectedUser}
            >
              {isPending ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>

      </DialogContent>
    </Dialog>
  );
};
