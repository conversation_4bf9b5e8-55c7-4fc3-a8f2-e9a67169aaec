"use client";

import {
  Camera,
} from "lucide-react";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState, useRef } from "react";
import { Information } from "./components/generalcomponent/Information";
import { LoginInformation } from "./components/generalcomponent/LoginInformation";
import { retrieveOrgData, updateOrgData } from "@/apis/settings";
import toast from "react-hot-toast";
import Image from "next/image";
import Pageloader from "@/utils/spinner/Pageloader";

const Page = () => {

  const queryClient = useQueryClient();
  const { data: apiResponse, isPending: isLoadingOrgData } = useQuery({
    queryKey: ["org-detail"],
    queryFn: retrieveOrgData
  });

  const [backendErrors, setBackendErrors] = useState({});
  const [previewUrl, setPreviewUrl] = useState(null);

  // Create a ref for the hidden file input
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create a preview URL for the selected image
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Upload the logo immediately when a file is selected
      uploadLogo(file);
    }
  };

  const uploadLogo = async (file) => {
    try {
      const formData = new FormData();
      formData.append('logo', file);

      // Use the mutation instead of direct API call
      await mutateAsync({ data: formData, isFormData: true });
    } catch (error) {
      console.error("Error uploading logo:", error);
    }
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["update-orgdata"],
    mutationFn: (payload) => {
      // Check if payload contains isFormData flag
      if (payload.isFormData) {
        return updateOrgData(payload.data, true);
      }
      return updateOrgData(payload);
    },
    onSuccess: (data) => {
      toast.success(data?.message || "Company information updated successfully");
      // Refresh the organization data to show the updated logo
      queryClient.invalidateQueries(["org-detail"]);
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to update company information");
    }
  });

  console.log("api response", apiResponse);


  return (
    <>
      {isLoadingOrgData ? (
        <div className="flex items-center justify-center min-h-full">
          <Pageloader />
        </div>
      ) : (
        <div className="min-h-screen">
          <div className=".max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
            {/* Header */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold">
                  Admin Settings
                </h1>
                <p className=" mt-1 text-xs">
                  View and update your account details, profile and more.
                </p>
              </div>
              <div className="relative">
                <div className='relative h-16 w-16 rounded-full overflow-hidden'>
                  {/* Check if the image URL is from an external domain */}
                  {previewUrl || (apiResponse?.data?.logo === null ? (
                    <Image
                      src="/images/default.png"
                      alt='Company logo'
                      height={100}
                      width={100}
                      className='object-cover w-full h-full'
                    />
                  ) : (
                    // For external URLs, use a regular img tag or add the domain to next.config.js
                    <Image
                      src={apiResponse?.data?.logo}
                      alt='Company logo'
                      className='object-cover w-full h-full'
                      height={100}
                      width={100}
                    />
                  ))}
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept="image/*"
                  className="hidden"
                />
                <button
                  className="absolute bottom-0 right-0 bg-white p-1.5 rounded-full shadow-lg border border-gray-200"
                  onClick={() => fileInputRef.current.click()}
                  disabled={isPending}
                >
                  {isPending ? (
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Camera className="w-4 h-4 text-gray-600" />
                  )}
                </button>
              </div>
            </div>

            {/* Company Information */}

            <Information orgData={apiResponse?.data} />

            <hr className="w-full h-1 text-gray-200" />

            <LoginInformation orgDetail={apiResponse?.data} />

            <hr className="w-full h-1 text-gray-200" />

            {/* Regional Settings */}

            {/* <RegionalSettings orgData={orgData}/> */}

          </div>
        </div>

      )}


    </>
  );
};

export default Page;