"use client"

import { TableComponent } from "@/components/reusables/table";
import { But<PERSON> } from "@/components/ui/button";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  EllipsisVertical,
  Plus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"

import { Checkbox } from "@/components/ui/checkbox";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import AddNewRole from "./components/AddNewRole";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import { RolesApi, duplicateRolesApi, exportRolesCSVApi } from "@/apis/admin/roles";
import { formatDate } from "@/utils/Utils";
import DeleteRole from "./components/DeleteRole";
import { useRouter } from "next/navigation";
import toast from 'react-hot-toast';
import { useExportCSV } from '@/hooks/useExportCSV';
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { useSearchParams } from 'next/navigation';

const RolesManagementContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();
  const navigate = useRouter();

  // Initialize useTableFilter
  const initialFilters = useMemo(() => ({
    name: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for roles data
  const { data: apiResponse, isLoading: isLoadingData } = useQuery({
    queryKey: ["listOfROles", filters],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      return RolesApi(apiParams);
    },
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    return apiResponse.results;
  }, [apiResponse?.results]);

  // Initialize useSort with filtered data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isOpenRole, setIsOpenRole] = useState(false);

  // Add this mutation
  const { mutate: duplicateRole, isPending: isDuplicating } = useMutation({
    mutationFn: duplicateRolesApi,
    onSuccess: (response) => {
      toast.success(response?.message || "Role duplicated successfully");
      queryClient.invalidateQueries(["listOfROles"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to duplicate role");
    }
  });

  // Setup CSV export
  const { handleExport } = useExportCSV(exportRolesCSVApi, { filename: 'roles.csv' });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Memoize filterOptions
  const filterOptions = useMemo(() => ({
    showDateFilter: true,
    inputFilters: [
      {
        key: 'name',
        label: 'Filter by role name',
        type: 'text'
      }
    ],
  }), []);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true // Make select column always visible
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => (
        <div>
          {row.original.name}
          <span className="rounded-md border p-1 text-[8px] ml-2">{row.original.permissions_count}</span>
        </div>
      ),
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <div onClick={(e) => e.stopPropagation()}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button aria-haspopup="true" size="icon" variant="ghost">
                  <EllipsisVertical />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setIsEditModalOpen(true);
                    setSelectedRow(row.original);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    duplicateRole({ id: row.original.id });
                  }}
                  disabled={isDuplicating}
                >
                  {isDuplicating ? "Duplicating..." : "Duplicate"}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setIsDeleteModalOpen(true);
                    setSelectedRow(row.original);
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      sticky: true,
      unhidable: true // Make actions column always visible
    }
  ], [handleSelectAll, selectedRows, sortedData, isDuplicating, duplicateRole]);

  const handleRowClick = (row) => {
    navigate.push(`/admin-settings/roles-management/${row?.id}/manage-permission`);
  };

  return (
    <>
      <div className="min-h-screen">
        <div className="mx-auto px-4 sm:px-6 border-b">
          {/* Header */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-4">
            <HeaderAndSub
              header="Role Management"
              subheader={"Manage and monitor all roles and employees across departments."}
            />

            <Button
              size="xs"
              onClick={() => setIsOpenRole(true)}
            >
              <Plus className="mr-1" />
              <span>Add New Role</span>
            </Button>
          </div>
        </div>


        {/* Table */}
        <TableComponent
          columns={columns}
          rows={sortedData}
          showImportExport={selectedRows.length > 0}
          exportToCSV={handleExportCSV}
          tableTitle={`All Roles (${apiResponse?.count || 0})`}
          isLoading={isLoadingData}
          NoavailableTitle={isLoadingData ? "Loading..." : "roles"}
          tableDescription={
            "Define and customize user roles with specific permissions and access levels. Organize team responsibilities and security controls."
          }
          onSort={handleSort}
          sortOrder={sortState.order}
          sortColumn={sortState.column}
          filterComponents={
            <FilterWrapper
              filterValues={filters}
              onFilterApply={handleFilterApply}
              onFilterClear={handleFilterClear}
              filterOptions={filterOptions}
              isLoading={isLoadingData}
            />
          }
          onRowClick={handleRowClick}
        />
      </div>

      {/* Modals */}
      {isOpenRole && (
        <AddNewRole
          isOpen={isOpenRole}
          onClose={() => setIsOpenRole(false)}
        />
      )}

      {isEditModalOpen && selectedRow && (
        <AddNewRole
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          mode="edit"
          roleData={selectedRow}
        />
      )}

      {isDeleteModalOpen && selectedRow && (
        <DeleteRole
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          roleId={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RolesManagementContent />
    </Suspense>
  )
}

export default Page;