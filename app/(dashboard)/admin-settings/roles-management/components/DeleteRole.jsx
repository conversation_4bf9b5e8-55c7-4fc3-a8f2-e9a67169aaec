import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteRolesApi } from '@/apis/admin/roles';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteRole = ({ onClose, isOpen, roleId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deteleRoleMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-role"],
    mutationFn: deleteRolesApi
  })

  const handleDeleteRole = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleRoleMutation({ id: roleId?.id })
      toast.success(response?.message || "Role Deleted Successfully")
      onClose()
      queryClient.invalidateQueries(["listOfROles"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (

    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteRole}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{roleId?.name}</span>?</>
      }
      description="This action will permanently delete the role, permissions and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteRole;
