import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import { createRolesApi, updateRolesApi } from '@/apis/admin/roles';

const AddNewRole = ({ isOpen, onClose, mode = "create", roleData }) => {

  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    name: roleData?.name || "",
    description: roleData?.description || "",
  })

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value })
    setBackendErrors({ ...backendErrors, [key]: null });
  }

  const { mutateAsync: createRoleMutation, isPending: isCreating } = useMutation({
    mutationKey: ["create-role"],
    mutationFn: createRolesApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfRoles"])
    },
    onError: (error) => {
      if (error?.details) {
        const errorDetails = error?.details || {};
        setBackendErrors(errorDetails);
      } else {
        toast.error(error?.message)
      }
    }
  })

  const { mutateAsync: updateRoleMutation, isPending: isUpdating } = useMutation({
    mutationKey: ["update-role"],
    mutationFn: updateRolesApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfRoles"])
    },
    onError: (error) => {
      if (error?.details) {
        const errorDetails = error?.details || {};
        setBackendErrors(errorDetails);
      } else {
        toast.error(error?.message)
      }

    }
  })

  const handleAdd = async (e) => {
    e.preventDefault();
    try {
      if (mode === "edit") {
        await updateRoleMutation({ id: roleData?.id, ...formData })
      } else {
        await createRoleMutation(formData)
      }
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">

        <form className="p-6 pt-2" onSubmit={handleAdd}>

          <DialogHeader>
            <DialogTitle>{mode === "create" ? "Add a new role" : "Update Role"}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 pt-4">
            <div className='text-xs'>Design new roles with precise permissions, granting users only the access they require.</div>

            <div>
              <FloatingLabelInput
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                id="role_name" name="name" label="Role Name *" type={"text"}
                className={`${backendErrors.name && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="name" />
            </div>


            <div className="flex flex-col gap-3">
              <Textarea
                id="description"
                placeholder="Description (Optional)"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                className={`${backendErrors.description && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="description" />

            </div>

            <div className="flex items-center mt-4 gap-2">
              <Button
                size="xs"
                type="submit"
                className="w-full"
                disabled={isCreating || isUpdating}
              >
                <span>
                  {isCreating || isUpdating
                    ? `${mode === "create" ? "Creating..." : "Updating..."}`
                    : `${mode === "create" ? "Create Role" : "Update Role"}`}

                </span>

              </Button>
            </div>

          </div>


        </form>

      </DialogContent>
    </Dialog>

  )
}

export default AddNewRole;