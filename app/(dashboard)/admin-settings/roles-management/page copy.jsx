"use client"

import { listOfAdvances } from "@/apis/advances";
import { TableComponent } from "@/components/reusables/table";
import { Button } from "@/components/ui/button";
import { useQuery } from "@tanstack/react-query";
import {
  EllipsisVertical,
  Plus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"

import { Checkbox } from "@/components/ui/checkbox";
import { useState } from "react";
import AddNewRole from "./components/AddNewRole";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import { RolesApi } from "@/apis/admin/roles";
import { formatDate } from "@/utils/Utils";
import DeleteRole from "./components/DeleteRole";
import { useRouter } from "next/navigation";
import { duplicateRolesApi } from "@/apis/admin/roles";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from 'react-hot-toast';

const Page = () => {

  const { data: apiResponse } = useQuery({
    queryKey: ["listOfROles"],
    queryFn: RolesApi
  })

  const rows = apiResponse?.results

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);


  // Add this near other state declarations
  const queryClient = useQueryClient();

  // Add this mutation
  const { mutate: duplicateRole, isPending: isDuplicating } = useMutation({
    mutationFn: duplicateRolesApi,
    onSuccess: (response) => {
      toast.success(response?.message || "Role duplicated successfully");
      queryClient.invalidateQueries(["listOfROles"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to duplicate role");
    }
  });

  const columns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      )
    },
    {
      title: "Name", accessor: (row) => (
        <div>{row.name} <span className="rounded-md border p-1 text-[8px] ml-2">{row.permissions_count}</span></div>
      )
    },
    { title: "Created At", accessor: (row) => formatDate(row.created_at) },
    {
      title: "Actions",
      accessor: (row) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              
            <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditModalOpen(true);
                  setSelectedRow(row);
                }}>Edit</DropdownMenuItem>

                <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  duplicateRole({ id: row.id });
                }}
                disabled={isDuplicating}
                onSelect={(e) => {
                  if (isDuplicating) {
                    e.preventDefault();
                  }
                }}
              >
                {isDuplicating ? "Duplicating..." : "Duplicate"}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteModalOpen(true);
                  setSelectedRow(row);
                }}
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  const [isOpenRole, setIsOpenRole] = useState(false)
  const navigate = useRouter()

  const handleRowClick = (row) => {
    // e.preventDefault();
    navigate.push(`/admin-settings/roles-management/${row?.id}/manage-permission`)
  }


  return (
    <>

      <div className="min-h-screen">
        <div className=".max-w-4xl mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start">

            <HeaderAndSub
              header="Role Management"
              subheader={"Manage and monitor all roles and employees across departments."}
            />

            <div>
              <Button
                size="xs"
                // className={`gap-1 rounded-full p-3`}
                onClick={() => setIsOpenRole(true)}
              >
                <span>Add New Role</span>
              </Button>
            </div>

          </div>

        </div>

        <div className="no-scrollbar">
          <TableComponent
            rows={rows}
            columns={columns}
            // showImportExport={false}
            tableTitle={`All Roles`}
            onRowClick={(row) => handleRowClick(row)}
            NoavailableTitle='No available Roles'
          />
        </div>


      </div>

      {isOpenRole && (
        <AddNewRole
          isOpen={isOpenRole}
          onClose={() => setIsOpenRole(false)}
        // onAddCategoryLimit={handleAddCategoryLimit}
        />
      )}

      {isEditModalOpen && (
        <AddNewRole
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          mode="edit"
          roleData={selectedRow}
        />
      )}

      {(isDeleteModalOpen && selectedRow) && (
        <DeleteRole
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          roleId={selectedRow}
        />
      )}

    </>
  );
}

export default Page;