"use client"

import { retrieveRolesApi } from '@/apis/admin/roles'
import { useParams } from 'next/navigation'
import { HeaderAndSub } from '@/components/reusables/HeaderAndSub'
import { TableComponent } from '@/components/reusables/table'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { EllipsisVertical } from 'lucide-react'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import Navigateback from '@/utils/navigateback'

const Page = () => {
  const { id } = useParams()

  const { data: roleDetailData, isPending } = useQuery({
    queryKey: ["retrieveRole", id],
    queryFn: () => retrieveRolesApi({ id }),
    enabled: !!id,
  });

  console.log(roleDetailData)

  const rows = roleDetailData?.data?.employees


  const columns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      )
    },
    {
      title: "Name", accessor: (row) => (
        <div>{row?.name}</div>
      )
    },
    {
      title: "Email", accessor: (row) => (
        <div>{row?.email}</div>
      )
    },

    {
      title: "Actions",
      accessor: (row) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
              >Edit</DropdownMenuItem>
              <DropdownMenuItem
              >Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  return (
    <>
      <div className="min-h-screen">

        <div className=".max-w-4xl mx-auto pr-4 sm:py-4 lg:py-6">
          <div className="flex justify-between items-center .mb-8">

            <HeaderAndSub
              header="Manage Roles "
              subheader={"Manager user roles and permissions for expenses management systemManager user roles and permissions for expenses management system"}
              className={"max-w-[70%]"}
            />

            <div>
              <Button
                size="xs"
                className={`gap-1 rounded-full p-3`}
              >
                <span>Save Changes</span>
              </Button>
            </div>
          </div>

          <div className='relative mt-4'>

            <Select required >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem>
                  {roleDetailData?.data?.name}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>


        </div>

        <div className="no-scrollbar">
          <TableComponent
            rows={rows}
            columns={columns}
            tableTitle={`Manage Employee Data`}
            NoavailableTitle='Users'
          />
        </div>


      </div>
    </>
  )
}

export default Page