"use client"

import { HeaderAndSub } from '@/components/reusables/HeaderAndSub'
import { TableComponent } from '@/components/reusables/table'
import { Button } from '@/components/ui/button'
import React, { useEffect, useState } from 'react'
import { Checkbox } from '@/components/ui/checkbox'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { retrieveRolesApi, updateRolesApi } from '@/apis/admin/roles'
import { useParams } from 'next/navigation'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import toast from 'react-hot-toast'
import { permissionApi } from '@/apis/utilapi'
import Pageloader from '@/utils/spinner/Pageloader'

const Page = () => {
  const { id } = useParams()
  const [organizedPermissions, setOrganizedPermissions] = useState([])
  const queryClient = useQueryClient()

  const { data: roleDetailData, isPending } = useQuery({
    queryKey: ["retrieveRole", id],
    queryFn: () => retrieveRolesApi({ id }),
    enabled: !!id,
  });

  const { data: permissionData } = useQuery({
    queryKey: ["get-all-permissions"],
    queryFn: permissionApi,
  });

  const updateRoleMutation = useMutation({
    mutationFn: updateRolesApi,
    onSuccess: (response) => {
      toast.success(response?.message || "Permissions updated successfully")
      queryClient.invalidateQueries(["retrieveRole", id])
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update permissions")
    }
  })

  const handlePermissionChange = (appName, action, checked) => {
    setOrganizedPermissions(prev =>
      prev.map(app => {
        if (app.app_name === appName) {
          return {
            ...app,
            permissions: {
              ...app.permissions,
              [action]: {
                ...app.permissions[action],
                isSelected: checked
              }
            }
          };
        }
        return app;
      })
    );
  };

  const handleSaveChanges = () => {
    const selectedPermissions = organizedPermissions.reduce((acc, app) => {
      const appPermissions = Object.values(app.permissions)
        .filter(permission => permission?.isSelected)
        .map(permission => permission.id);
      return [...acc, ...appPermissions];
    }, []);

    updateRoleMutation.mutate({
      id,
      name: roleDetailData?.data?.name,
      permissions: selectedPermissions
    });
  };

  useEffect(() => {
    if (permissionData?.results) {
      const permissionsByApp = {};

      permissionData.results.forEach(permission => {
        if (!permissionsByApp[permission.app_name]) {
          permissionsByApp[permission.app_name] = {
            app_name: permission.app_name,
            permissions: {}
          };
        }

        const action = permission.codename.split('_')[0];
        permissionsByApp[permission.app_name].permissions[action] = {
          ...permission,
          isSelected: roleDetailData?.data?.permissions?.some(
            p => p.id === permission.id
          ) || false
        };
      });

      setOrganizedPermissions(Object.values(permissionsByApp));
    }
  }, [permissionData, roleDetailData]);

  const isSystemRole = roleDetailData?.data?.is_system_role || false;

  const columns = [
    {
      title: "Permissions",
      accessor: (row) => <div>{row.app_name}</div>
    },
    {
      title: "View",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.view?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'view', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    },
    {
      title: "Create",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.add?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'add', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    },
    {
      title: "Edit",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.change?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'change', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    },
    {
      title: "Delete",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.delete?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'delete', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    },
    {
      title: "Approve",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.approve?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'approve', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    },
    {
      title: "Export",
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={!!row.permissions.export?.isSelected}
            onCheckedChange={(checked) =>
              handlePermissionChange(row.app_name, 'export', checked)
            }
            disabled={isSystemRole}
          />
        </div>
      )
    }
  ];

  // if (isPending) {
  //   return <div className="flex items-center justify-center h-screen">
  //     <Pageloader />
  //   </div>;
  // }

  return (
    <>
      <div className="min-h-screen">
        <div className="mx-auto px-4">
          <div className="flex justify-between items-center">
            <HeaderAndSub
              header="Manage Permission"
              subheader="Manager user roles and permissions for expenses management system"
              className="max-w-[70%]"
            />
            <div className='flex items-center gap-4'>
              <Button
                size="xs"
                // className="gap-1 rounded-full p-3"
                onClick={handleSaveChanges}
                disabled={updateRoleMutation.isPending || isSystemRole}
              >
                <span>
                  {isSystemRole ? 'System Role (Cannot Edit)' :
                    updateRoleMutation.isPending ? 'Saving...' : 'Save Changes'}
                </span>
              </Button>
            </div>
          </div>
        </div>

        <div className='relative mt-4 px-4'>
          <Select required
            disabled
          >
            <SelectTrigger>
              <SelectValue placeholder={roleDetailData?.data?.name} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={roleDetailData?.data?.name}>
                {roleDetailData?.data?.name}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="no-scrollbar mt-4">
          <TableComponent
            rows={organizedPermissions}
            columns={columns}
            tableTitle="Role Selection"
            NoavailableTitle={isPending ? <Pageloader /> : `No available permissions for a particular role`}
            showImportExport={false}
            showFilter={false}
          />
        </div>
      </div>
    </>
  )
}

export default Page