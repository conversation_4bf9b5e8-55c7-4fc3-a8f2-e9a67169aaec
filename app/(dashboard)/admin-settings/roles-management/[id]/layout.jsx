"use client"

import <PERSON><PERSON><PERSON><PERSON> from '@/components/reusables/MyTabs'
import Navigateback from '@/utils/navigateback'
import { useParams } from 'next/navigation'
import React from 'react'

const RolesAndPermissionsLayout = ({ children }) => {
  const { id } = useParams()

  return (
    <div className="overflow-x-hidden auto-rows-max">
      <div className="w-fit px-4">
        <Navigateback />
        {/* <Tabaccross
          links={[
            { route: `/admin-settings/roles-management/${id}/manage-role`, text: 'Manage Roles' },
            { route: `/admin-settings/roles-management/${id}/manage-permission`, text: 'Manage Permissions' },
          ]}
        /> */}
      </div>

      <main className="overflow-x-scroll">{children}</main>
    </div>
  )
}

export default RolesAndPermissionsLayout