import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deletePolicyApi } from '@/apis/admin/policies_apis';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeletePolicy = ({ onClose, isOpen, policyId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: detelePolicyMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-policy"],
    mutationFn: deletePolicyApi
  })

  const handleDeletePolicy = async (e) => {
    e.preventDefault()
    try {
      const response = await detelePolicyMutation({ id: policyId?.id })
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfPolicies"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeletePolicy}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{policyId?.name}</span>?</>
      }
      description="This action will permanently delete the policy and all its associated data. This cannot be undone."
    />
  )
}

export default DeletePolicy;
