import React from 'react'
import {
  Sheet,
  SheetClose,
  SheetContent,
} from "@/components/ui/sheet"
import { formatDate } from '@/utils/Utils'

const PolicyDetail = ({ sheetOpen, setSheetOpen, selectedRow }) => {

  return (
    <>
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        {selectedRow && (
          <SheetContent side="right" className=".w-1/3 rounded-s-xl flex flex-col gap-4">
            <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-gray-950 text-[10px] font-medium leading-8'>
                <div className='border-b text-sm py-2'>
                  <span>{selectedRow?.name} Policy Details</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Name:</span> {selectedRow.name}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Max Amount:</span> {selectedRow.max_amount_display}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Max Amount Receipt Requirement:</span> {selectedRow.max_amount_receipt_requirement_display}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Created:</span> {formatDate(selectedRow.created_at)}
                </div>
              </div>
            </div>

            <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-table_gray_text text-xs font-medium leading-8 overflow-y-scroll max-h-96 no-scrollbar'>
                <div className='border-b sticky top-0 z-10 bg-bg_gray'>
                  <span className='font-semibold text-sm'>Description</span>
                </div>

                <div className='flex justify-between items-center'>
                  <span dangerouslySetInnerHTML={{__html: selectedRow?.description}}></span>
                </div>
              </div>
            </div>
          </SheetContent>
        )}
      </Sheet>
    </>
  )
}

export default PolicyDetail