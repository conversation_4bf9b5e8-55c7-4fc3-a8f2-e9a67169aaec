"use client"

import { TableComponent } from "@/components/reusables/table";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button";
import { EllipsisVertical } from "lucide-react";
import { listLimit } from "@/apis/admin/category";
import UpdateCategory from "./components/UpdateCategory";
import { useState, useEffect, Suspense } from "react";
import { getProfileApi } from "@/apis/profile-management";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { QuillEditor } from "@/hooks/QuillEditor";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import Link from "next/link";
import { createNew<PERSON><PERSON>y<PERSON><PERSON>, updateP<PERSON>y<PERSON><PERSON>, retreivePolicy } from "@/apis/admin/policies_apis";
import toast from "react-hot-toast";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { useRouter, useSearchParams } from "next/navigation";
import { listOfExpenseType } from "@/apis/expense-types";
import Pageloader from "@/utils/spinner/Pageloader";
import Navigateback from "@/utils/navigateback";

// Create a client component that safely uses useSearchParams
const PolicyFormWithParams = () => {
  const navigate = useRouter();
  const searchParams = useSearchParams();
  const policyId = searchParams.get('id');
  const isEditMode = !!policyId;

  return <PolicyForm policyId={policyId} isEditMode={isEditMode} navigate={navigate} />;
};

// Main component that doesn't directly use useSearchParams
const PolicyForm = ({ policyId, isEditMode, navigate }) => {

  // Fetch policy data if in edit mode
  const { data: policyData, isLoading: isPolicyLoading } = useQuery({
    queryKey: ["policy-details", policyId],
    queryFn: () => retreivePolicy(policyId),
    enabled: !!policyId,
  });

  const { data: apiResponse } = useQuery({
    queryKey: ["expense-type"],
    queryFn: listOfExpenseType
  });

  const { data: listLimitResponse } = useQuery({
    queryKey: ["list-limit", policyId],
    queryFn: () => listLimit({ policy: policyId })
  });

  const { data: profileData, isPending: isLoading, error } = useQuery({
    queryKey: ["listOfcurrencies"],
    queryFn: getProfileApi,
  });

  const defaultCurrency = profileData?.data?.profile?.organization?.default_currency;

  const rows = apiResponse?.results;
  const policyRows = listLimitResponse?.results;


  const [formData, setFormData] = useState({
    name: "",
    description: "",
    max_amount: "1000000",
    max_amount_currency: defaultCurrency || "",
    max_amount_receipt_requirement: "1000000",
    max_amount_receipt_requirement_currency: defaultCurrency || "",
    category_limits: []
  });

  // Populate form data when policy data is loaded
  useEffect(() => {
    if (policyData && isEditMode) {
      const policy = policyData?.data;
      console.log("pol", policy)
      setFormData({
        name: policy.name || "",
        description: policy.description || "",
        max_amount: policy.max_amount || "1000000",
        max_amount_currency: policy.max_amount_currency || defaultCurrency || "",
        max_amount_receipt_requirement: policy.max_amount_receipt_requirement || "1000000",
        max_amount_receipt_requirement_currency: policy.max_amount_receipt_requirement_currency || defaultCurrency || "",
        category_limits: policy.category_limits || []
      });
    }
  }, [policyData, isEditMode, defaultCurrency]);


  const columns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      )
    },
    { title: "Category Name", accessor: (row) => row?.name },
    {
      title: "Maximum Expense Amount", accessor: (row) => (
        <div>{profileData?.data?.profile?.organization?.default_currency} {parseFloat(formData?.max_amount).toLocaleString()}</div>
      )
    },
    {
      title: "Maximum Amount Receipt Required", accessor: (row) => (
        <div>
          {profileData?.data?.profile?.organization?.default_currency} {parseFloat(formData?.max_amount_receipt_requirement).toLocaleString()}
        </div>
      )
    },
    // {
    //   accessor: (row) => (
    //     <div>
    //       <DropdownMenu>
    //         <DropdownMenuTrigger asChild>
    //           <Button aria-haspopup="true" size="icon" variant="ghost">
    //             <EllipsisVertical />
    //           </Button>
    //         </DropdownMenuTrigger>
    //         <DropdownMenuContent align="end">
    //           <DropdownMenuItem>Edit</DropdownMenuItem>
    //           <DropdownMenuItem>Delete</DropdownMenuItem>
    //         </DropdownMenuContent>
    //       </DropdownMenu>
    //     </div>
    //   ),
    // },
  ];

  const policyColumns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      )
    },
    { title: "Expense Type", accessor: (row) => row?.expense_type?.name },
    { title: "Max Amounttt", accessor: (row) => row?.max_amount_display },
    { title: "Max Amount Receipt Requirement", accessor: (row) => row?.max_amount_receipt_requirement_display },
    {
      accessor: (row) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteModalOpen(true);
                  setSelectedRow(row); // Pass the current row to the edit modal
                }}>Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  const [selectedRow, setSelectedRow] = useState()

  const [open, setOpen] = useState(false);

  const handleRowClick = (row) => {
    setOpen(true)
    setSelectedRow(row)
  }

  // Update form data when default currency changes
  useEffect(() => {
    if (!isEditMode && defaultCurrency) {
      setFormData(prev => ({
        ...prev,
        max_amount_currency: defaultCurrency,
        max_amount_receipt_requirement_currency: defaultCurrency
      }));
    }
  }, [defaultCurrency, isEditMode]);

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  // Create mutation
  const { mutateAsync: createMutate, isPending: isCreating } = useMutation({
    mutationKey: ["create-new-policy"],
    mutationFn: createNewPolicyApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Policy created successfully!");
      navigate.push("/admin-settings/policies");
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    }
  });

  // Update mutation
  const { mutateAsync: updateMutate, isPending: isUpdating } = useMutation({
    mutationKey: ["update-policy"],
    mutationFn: updatePolicyApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Policy updated successfully!");
      navigate.push("/admin-settings/policies");
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    }
  });

  const isPending = isCreating || isUpdating;

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (isEditMode) {
        // Create a copy of formData without the category_limits property
        const { category_limits, ...updatePayload } = formData;
        await updateMutate({ id: policyId, ...updatePayload });
      } else {
        // For new policy, include category_limits
        const category_limits = rows?.map(row => ({
          expense_type: row.id,
          max_amount: formData.max_amount,
          max_amount_currency: formData.max_amount_currency || defaultCurrency,
          max_amount_receipt_requirement: formData.max_amount_receipt_requirement,
          max_amount_receipt_requirement_currency: formData.max_amount_receipt_requirement_currency || defaultCurrency
        })) || [];

        await createMutate({
          ...formData,
          category_limits
        });
      }
    } catch (error) {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} policy:`, error);
    }
  };

  return (
    <>
      <div>
        <div className="mb-4 w-full px-4">
          <Navigateback />
          <div>
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start mb-8">
              <HeaderAndSub
                header={isEditMode ? "Edit Policy" : "New Policy"}
                subheader={`${isEditMode ? "Edit" : "Create"} and manage custom reimbursement policies to ensure employee compliance.`}
              />

              <div className="flex items-center gap-3">
                <Link href={"/admin-settings/policies"}>
                  <Button
                    size="xs"
                    variant="outline"
                    // className={`gap-1 rounded-full p-3`}
                  >
                    <span>All Policies</span>
                  </Button>
                </Link>

                <Button
                  size="xs"
                  // className={`gap-1 rounded-full p-3`}
                  variant="outline"
                  onClick={() => navigate.push("/admin-settings/policies")}
                >
                  <span>Cancel</span>
                </Button>

                <Button
                  size="xs"
                  // className={`gap-1 rounded-full p-3`}
                  onClick={handleSubmit}
                  disabled={isPending || isPolicyLoading}
                >
                  <span>
                    {isPending
                      ? (isEditMode ? "Updating..." : "Saving...")
                      : (isEditMode ? "Update" : "Save")}
                  </span>
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">

              <div className=".border rounded-md .p-3 space-y-4">
                {/* <h4 className="text-sm font-semibold">Add New Policy</h4> */}

                <div className="relative">

                  <FloatingLabelInput
                    label="Policy Name"
                    id="policy-name"
                    value={formData.name}
                    onChange={(e) => handleChange("name", e.target.value)}
                    className={`${backendErrors.name && "validate_input"}`}
                  />
                  <ErrorMessage errors={backendErrors} field="name" />

                </div>

                <div className="overflow-hidden relative">
                  <QuillEditor value={formData?.description}
                    onChange={(value) => handleChange("description", value)} className={`${backendErrors.description && "validate_input"}`} />

                  <ErrorMessage errors={backendErrors} field="description" />
                </div>

              </div>

              <div className="border rounded-md p-3 space-y-4 h-fit">
                <h4 className="text-sm font-semibold border-b pb-2">Set General Limits</h4>

                <div>
                  <div className="py-4 text-xs flex items-center justify-between gap-4">
                    <p>Maximum expense amount</p>
                    <div>
                      <div className="flex items-center gap-3">

                        <div>
                          {profileData?.data?.profile?.organization?.default_currency}
                        </div>

                        <div className="">
                          <FloatingLabelInput
                            id="max_amountmax_amount"
                            label="Amount *"
                            type="text"
                            value={Number(formData.max_amount).toLocaleString()}
                            onChange={(e) => {
                              // Remove commas and convert to number before updating state
                              const numericValue = e.target.value.replace(/,/g, '');
                              if (!isNaN(numericValue) || numericValue === '') {
                                handleChange("max_amount", numericValue);
                              }
                            }}
                            className={`w-24`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="py-4 text-xs flex items-center justify-between gap-4">
                    <p>Max amount receipt requirement</p>
                    <div>
                      <div className="flex items-center gap-3">
                        <div>
                          {profileData?.data?.profile?.organization?.default_currency}
                        </div>

                        <div className="">
                          <FloatingLabelInput
                            id="amount"
                            label="Amount *"
                            type="text"
                            value={Number(formData.max_amount_receipt_requirement).toLocaleString()}
                            onChange={(e) => {
                              // Remove commas and convert to number before updating state
                              const numericValue = e.target.value.replace(/,/g, '');
                              if (!isNaN(numericValue) || numericValue === '') {
                                handleChange("max_amount_receipt_requirement", numericValue);
                              }
                            }}
                            className={`w-24`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                  <ErrorMessage errors={backendErrors} field="max_amount_receipt_requirement" />
                </div>

              </div>

            </div>
          </div>

        </div>


        {isEditMode ? (
          <div className="no-scrollbar">

            <TableComponent
              rows={policyRows}
              columns={policyColumns}
              tableTitle={`Expense Category`}
              onRowClick={(row) => handleRowClick(row)}
              NoavailableTitle={"No policy data"}
              showColumnFilter={false}
              showFilter={false}
              showImportExport={false}
            />

          </div>
        ) : (
          <div className="no-scrollbar">

            <TableComponent
              rows={rows}
              columns={columns}
              tableTitle={`Expense Categories`}
              // onRowClick={(row) => handleRowClick(row)}
              NoavailableTitle={"No expense category"}
              showFilter={false}
              showColumnFilter={false}
              showImportExport={false}
            />

          </div>

        )}


      </div>

      {(open && selectedRow) && (
        <UpdateCategory
          isOpen={open}
          onClose={() => setOpen(false)}
          selectedRow={selectedRow}
          categoryData={formData}
        />
      )}

    </>

  );
};

// Main page component with Suspense boundary
const Page = () => {
  return (
    <Suspense fallback={<div><Pageloader /></div>}>
      <PolicyFormWithParams />
    </Suspense>
  );
};

export default Page;