import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { DialogClose } from '@radix-ui/react-dialog';
import { Button } from '@/components/ui/button';
import { updateCategoryApi } from '@/apis/admin/category';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { Checkbox } from '@/components/ui/checkbox';

const UpdateCategory = ({ isOpen, onClose, selectedRow }) => {

  console.log("sel", selectedRow)

  const queryClient = useQueryClient()

  const [backendErrors, setBackendErrors] = useState({});

  const [formData, setFormData] = useState({
    expense_type: selectedRow?.expense_type?.id, // Assuming selectedRow has an `id` field
    max_amount: parseFloat(selectedRow?.max_amount),
    max_amount_currency: selectedRow?.max_amount_currency || "", // Replace with actual currency if available
    max_amount_receipt_requirement: parseFloat(selectedRow?.max_amount_receipt_requirement) || "",
    max_amount_receipt_requirement_currency: selectedRow?.max_amount_receipt_requirement_currency || "", // Replace with actual currency if available
    override_general_limit: false
  })

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    // Clear the specific error when user starts typing
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["update-category"],
    mutationFn: updateCategoryApi,
    onSuccess: (data) => {
      toast.success(data?.message);
      onClose();
      queryClient.invalidateQueries(["list-limit"])
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    }
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      await mutateAsync({ id: selectedRow?.id, ...formData });
    } catch (error) {
      console.error("Error creating policy:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Update {selectedRow?.policy?.name}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 pt-4">

          <div className="flex items-center space-x-2">
            <Checkbox
              id="override_general_limit"
              checked={formData.override_general_limit}
              onCheckedChange={(checked) => handleChange("override_general_limit", checked)}
            />
            <label
              htmlFor="override_general_limit"
              className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Override General Limits for <span className='font-semibold'>{selectedRow?.expense_type?.name}</span>
            </label>
          </div>

          <div className=" border-b py-4 text-xs flex items-center justify-between gap-4">
            <p>Maximum expense amount</p>
            <div>
              <div className="flex items-center gap-3">
                <div>
                  {selectedRow?.max_amount_currency}
                </div>
                <div className="">
                  <FloatingLabelInput
                    id="max_amount"
                    label="Amount *"
                    type="number"
                    value={formData.max_amount}
                    onChange={(e) => handleChange("max_amount", e.target.value)}
                    className={`w-24`}
                    disabled={!formData.override_general_limit}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className=" border-b py-4 text-xs flex items-center justify-between gap-4">
            <p>Max amount receipt requirement</p>
            <div>
              <div className="flex items-center gap-3">
                <div>
                  {selectedRow?.max_amount_receipt_requirement_currency}
                </div>
                <div className="">
                  <FloatingLabelInput
                    id="max_amount_receipt_requirement"
                    label="Amount *"
                    type="number"
                    value={formData.max_amount_receipt_requirement}
                    onChange={(e) => handleChange("max_amount_receipt_requirement", e.target.value)}
                    className={`w-24`}
                    disabled={!formData.override_general_limit}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end gap-3">
            <DialogClose asChild>
              <Button
                size="xs"
                // className={`gap-1 rounded-full p-3`}
                variant="outline"
              >
                <span>Cancel</span>
              </Button>
            </DialogClose>

            <Button
              size="xs"
              // className={`gap-1 rounded-full p-3`}
              disabled={isPending}
            >
              <span>{isPending ? "Updating..." : "Update"}</span>
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default UpdateCategory;