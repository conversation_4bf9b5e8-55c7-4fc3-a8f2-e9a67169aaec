"use client"

import { TableComponent } from "@/components/reusables/table";
import { But<PERSON> } from "@/components/ui/button";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  EllipsisVertical,
  Plus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox";
import { exportPoliciesCSVApi, listOfPolicies } from "@/apis/expense-policies";
import Link from "next/link";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import PolicyDetail from "./components/PolicyDetail";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import DeletePolicy from "./components/DeletePolicy";
import { useRouter, useSearchParams } from "next/navigation";
import FilterWrapper from '@/components/reusables/FilterWrapper';
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import { formatDate } from "@/utils/Utils";
import { useExportCSV } from "@/hooks/useExportCSV";

const PoliciesContent = () => {
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const navigate = useRouter();

  // Initialize states
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Initialize filters
  const initialFilters = useMemo(() => ({
    search: "",
    dateRange: {
      from: "",
      to: ""
    }
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for policies data
  const { data: apiResponse, isLoading: isLoadingData } = useQuery({
    queryKey: ["listOfPolicies", filters],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }
      return listOfPolicies(apiParams);
    },
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    return apiResponse.results;
  }, [apiResponse?.results]);

  // Initialize sorting
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData || []);

  // Setup CSV export
  const { handleExport } = useExportCSV(exportPoliciesCSVApi, { filename: 'policies.csv' });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  // Handle select all
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(row => row.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Filter options
  const filterOptions = useMemo(() => ({
    showDateFilter: true,
    dateFilter: {
      label: "Filter by date",
      fromLabel: "From date",
      toLabel: "To date"
    },
    inputFilters: [
      {
        key: 'search',
        label: 'Search policies',
        type: 'text',
        placeholder: 'Search by name...'
      }
    ],
  }), []);

  // Memoize columns
  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original.name,
      sortable: true
    },
    {
      accessorKey: "max_amount_display",
      header: "Max Amount",
      cell: ({ row }) => row.original.max_amount_display,
      sortable: true
    },
    {
      accessorKey: "max_amount_receipt_requirement_display",
      header: "Max Amount Receipt Requirement",
      cell: ({ row }) => row.original.max_amount_receipt_requirement_display,
      sortable: true
    },

    {
      accessorKey: "last_updated_by",
      header: "Last Updated by",
      cell: ({ row }) => row.original.last_updated_by?.name,
      sortable: true
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div onClick={(e) => e.stopPropagation()}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  navigate.push(`/admin-settings/policies/create?id=${row.original.id}`);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setIsDeleteModalOpen(true);
                  setSelectedRow(row.original);
                }}
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      unhidable: true,
      sticky: true
    }
  ], [handleSelectAll, selectedRows, sortedData, navigate]);

  const handleRowClick = (row) => {
    setSelectedRow(row);
    setSheetOpen(true);
  };

  return (
    <div className="min-h-screen">
      <div className=".max-w-4xl mx-auto px-4 border-b">
        {/* Header */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center mb-2">
          <HeaderAndSub
            header="All Policies"
            subheader="Create and manage custom reimbursement policies to ensure employee compliance."
          />

          <Link href="/admin-settings/policies/create">
            <Button size="xs">
              <Plus className="mr-1" />
              <span>New Policy</span>
            </Button>
          </Link>
        </div>
      </div>


      <TableComponent
        columns={columns}
        rows={sortedData}
        showImportExport={selectedRows.length > 0}
        exportToCSV={handleExportCSV}
        tableTitle={`All Policies (${apiResponse?.count || 0})`}
        isLoading={isLoadingData}
        NoavailableTitle={isLoadingData ? "Loading..." : "Policies"}
        tableDescription={
          "Set up expense policies, define spending limits, and establish approval workflows. Configure rules for expense submissions and reimbursements."
        }
        onSort={handleSort}
        sortOrder={sortState.order}
        sortColumn={sortState.column}
        filterComponents={
          <FilterWrapper
            filterValues={filters}
            onFilterApply={handleFilterApply}
            onFilterClear={handleFilterClear}
            filterOptions={filterOptions}
            isLoading={isLoadingData}
          />
        }
        onRowClick={handleRowClick}
      />

      <PolicyDetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedRow}
      />

      {isDeleteModalOpen && selectedRow && (
        <DeletePolicy
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          policyId={selectedRow}
        />
      )}
    </div>
  );
};


const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PoliciesContent />
    </Suspense>
  )
}

export default Page;