"use client"

import React from 'react'
import Settingstab from './components/Settingstab'
import { usePathname } from 'next/navigation';

const AdminLayout = ({ children }) => {

  // const pathname = usePathname();
  // const hideAdminSidebar = pathname.includes('/manage-role') || pathname.includes('/manage-permission') || pathname.includes('/users-management');

  return (
    <div className='overflow-x-hidden grid'>
      <div className="flex overflow-x-scroll">

        {/* {!hideAdminSidebar && ( */}
          <div className='w-60'>
            <Settingstab />
          </div>
        {/* )} */}

        <div className='w-full overflow-x-scroll relative mt-4'>
          {children}
        </div>
      </div>

    </div>
  )
}

export default AdminLayout;