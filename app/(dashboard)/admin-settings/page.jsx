"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Check, Dot } from "lucide-react";
import { useEffect, useState } from "react";
import { retrieveOrgData } from "@/apis/settings";
import { useQuery } from "@tanstack/react-query";
import Pageloader from "@/utils/spinner/Pageloader";
import { useRouter } from "next/navigation";

const Page = () => {
  // Calculate completed steps from API data instead of hardcoded values
  const { data: profileData, isPending: isLoading } = useQuery({
    queryKey: ["retrieve-orgdata"],
    queryFn: retrieveOrgData,
  });
  
  // Calculate completed steps based on setup_progress array
  const totalSteps = profileData?.data?.setup_progress?.length || 0;
  const completedSteps = profileData?.data?.setup_progress?.filter(item => item.value === true)?.length || 0;

  const navigate = useRouter()
  // console.log("pro", profile?.userData)

  return (
    <>
      {isLoading ? (
        <div className="flex items-center justify-center min-h-full">
        <Pageloader />
        </div>
      ) : (
        <Card className="p-10 mx-auto border-none .bg-red-400 rounded-none shadow-none">
          <CardHeader className="leading-10">
            <CardTitle className="text-2xl font-bold">Welcome back, <span className="capitalize">{profileData?.data?.name}</span></CardTitle>
          </CardHeader>
          <CardContent className="border rounded-lg py-10">

            {/* Progress Bar */}
            <div className="mb-6 flex items-center justify-between">
              <CardDescription className="text-gray-600">
                Let&apos;s set up your business
              </CardDescription>
              <div className="flex items-center gap-4 w-[50%]">
                <div>
                  <span className="text-xs whitespace-nowrap">
                    {completedSteps} of {totalSteps} Completed
                  </span>
                </div>
                <Progress 
                  value={totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0} 
                  className="h-1" 
                />
              </div>
            </div>

            {/* Setup Steps */}
            <div className="space-y-4">

              {profileData?.data?.setup_progress.map((item, index) => (
                <div key={index} className="flex gap-3 items-center p-3 border rounded-lg">
                  {item?.value ? (
                    <Check size={14} />
                  ) : (
                    <Dot size={14} />
                  )}
                  <span className={`text-xs font-medium ${item?.value === true && 'line-through'}`}>{item?.label}</span>
                </div>
              ))}

            </div>

            {/* "I'll do this later" Button */}
          </CardContent>

          <div className={`flex items-center justify-end mt-6 ${completedSteps === totalSteps && 'hidden'}`}>
            <Button 
              variant="outline"
            >
              I&apos;ll do this later
            </Button>
          </div>

        </Card>

      )}
    </>
  );
}

export default Page;
