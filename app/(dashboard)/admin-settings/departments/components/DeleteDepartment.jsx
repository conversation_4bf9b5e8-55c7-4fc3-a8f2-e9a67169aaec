import React from 'react'
import {
  <PERSON><PERSON>,
  Di<PERSON>Close,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteDepartmentsApi } from '@/apis/admin/departments';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteDepartment = ({ onClose, isOpen, departmentId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deteleDepartmentMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-department"],
    mutationFn: deleteDepartmentsApi
  })

  const handleDelete = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleDepartmentMutation({ id: departmentId?.id })
      toast.success(response?.message || "Department Deleted Successfully")
      onClose()
      queryClient.invalidateQueries(["listOfDepartments"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (

    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDelete}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{departmentId?.name}</span>?</>
      }
      description="This action will permanently delete the department and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteDepartment;
