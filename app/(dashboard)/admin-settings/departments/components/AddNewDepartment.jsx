import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { Button } from '@/components/ui/button';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import { createDepartmentsApi, updateDepartmentsApi } from '@/apis/admin/departments';
import { usersApi } from '@/apis/admin/users';
import { Label } from '@/components/ui/label';
import { SearchableSelect } from '@/components/reusables/SearchableSelect';

const AddNewDepartment = ({ isOpen, onClose, userData = null, mode = "create" }) => {

  console.log("usd", userData)
  const queryClient = useQueryClient();

  const { data: usersData, isPending: isLoadingUser } = useQuery({
    queryKey: ["users-list"],
    queryFn: usersApi,
    enabled: isOpen
  });

  const usersWithLabels = usersData?.results?.map(user => ({
    id: String(user.id), // Convert id to string
    label: (
      <div className="flex items-center gap-4">
        <div className="rounded-full bg-gray-200 p-1 text-[8px] font-semibold h-8 w-8 flex items-center justify-center">
          {user?.initials}
        </div>
        <div className="flex flex-col items-start">
          <span className="font-medium">{user?.name}</span>
          <span>{user?.email}</span>
        </div>
      </div>
    )
  }));

  const [formData, setFormData] = useState({
    name: mode === "edit" ? userData?.name : "",
    head: mode === "edit" ? String(userData?.head?.id) : ""
  })

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value })
    setBackendErrors({ ...backendErrors, [key]: null });
  }


  const { mutateAsync: createDepartmentMutation, isPending: isCreating } = useMutation({
    mutationKey: ["create-department"],
    mutationFn: createDepartmentsApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfDepartments"])
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    }
  })

  // Update mutation
  const { mutateAsync: updateDepartmentMutation, isPending: isUpdating } = useMutation({
    queryKey: ["update-department"],
    mutationFn: updateDepartmentsApi,
    onSuccess: (response) => {
      toast.success(response?.message);
      onClose();
      queryClient.invalidateQueries(["listOfDepartments", userData?.id]);
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    },
  });

  const handleAdd = async (e) => {
    e.preventDefault();
    try {

      if (mode === "create") {
        await createDepartmentMutation(formData); // Call create API
      } else {
        await updateDepartmentMutation({ id: userData?.id, ...formData }); // Call update API
      }

    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">

        <DialogHeader>
          <DialogTitle>{mode === "create" ? "Add a new department" : "Update department"}</DialogTitle>
        </DialogHeader>

        <form className="py-6 pt-2" onSubmit={handleAdd}>


          <div className="space-y-4 pt-4">
            <div>
              <FloatingLabelInput
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                id="department_name" name="name" label="Department name *" type={"text"}
                className={`${backendErrors.name && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="name" />
            </div>

            <div>
              <Label htmlFor="head" className="text-xs font-medium mb-1 block">Head (Optional )</Label>
              <SearchableSelect
                value={formData.head}
                onValueChange={(value) => handleChange("head", value)}
                placeholder="Select User"
                searchPlaceholder="Search users..."
                items={usersWithLabels}
                error={backendErrors.head}
                className={"py-5"}
                isLoading={isLoadingUser}
              />
              <ErrorMessage errors={backendErrors} field="head" />
            </div>


            <div className="flex items-center mt-4 gap-2">
              <Button
                size="xs"
                type="submit"
                className="w-full"
                disabled={isCreating || isUpdating}
              >
                <span>

                  {isCreating || isUpdating
                    ? `${mode === "create" ? "Adding..." : "Updating..."}`
                    : `${mode === "create" ? "Add New Department" : "Update Department"}`}

                </span>

              </Button>
            </div>

          </div>


        </form>

      </DialogContent>
    </Dialog>

  )
}

export default AddNewDepartment;