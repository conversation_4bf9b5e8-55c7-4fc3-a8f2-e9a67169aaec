"use client"

import { TableComponent } from "@/components/reusables/table";
import { But<PERSON> } from "@/components/ui/button";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { EllipsisVertical, Plus } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import { formatDate } from "@/utils/Utils";
import { useRouter, useSearchParams } from "next/navigation";
import AddNewDepartment from "./components/AddNewDepartment";
import DeleteDepartment from "./components/DeleteDepartment";
import { departmentsApi, exportDepartmentsCSVApi } from "@/apis/admin/departments";
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { useExportCSV } from '@/hooks/useExportCSV';

const DepartmentsContent = () => {
  const queryClient = useQueryClient();
  const navigate = useRouter();

  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';

  // Initialize states
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Initialize useTableFilter
  const initialFilters = useMemo(() => ({
    name: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for departments data
  const { data: apiResponse, isLoading: isLoadingData } = useQuery({
    queryKey: ["listOfDepartments", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }
      return departmentsApi(apiParams);
    },
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    return apiResponse.results;
  }, [apiResponse?.results]);

  // Initialize sorting
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData || []);

  // Setup CSV export
  const { handleExport } = useExportCSV(exportDepartmentsCSVApi, {
    filename: 'departments.csv'
  });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  // Handle select all
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(row => row.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters, currentTab]);

  // Filter options
  const filterOptions = useMemo(() => ({
    showDateFilter: true,
    dateFilter: {
      label: "Filter by date",
      fromLabel: "From date",
      toLabel: "To date"
    },
    inputFilters: [
      {
        key: 'search',
        label: 'Search departments',
        type: 'text',
        placeholder: 'Type to search...'
      }
    ],
  }), []);

  // Memoize columns
  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: false,
    },
    {
      accessorKey: "head",
      header: "Head",
      cell: ({ row }) => (
        <div className="flex flex-col items-start">
          <span>{row.original.head?.name}</span>
          <span className="text-[10px]">{row.original.head?.email}</span>
        </div>
      ),
      sortable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div onClick={(e) => e.stopPropagation()}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => {
                  setIsEditModalOpen(true);
                  setSelectedRow(row.original);
                }}
              >
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setIsDeleteModalOpen(true);
                  setSelectedRow(row.original);
                }}
              >
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      unhidable: true
    }
  ], [handleSelectAll, selectedRows, sortedData]);

  return (
    <>
      <div className="min-h-screen">
        <div className=".max-w-4xl mx-auto px-4 p-2 .sm:p-6 .lg:p-8 border-b">
          {/* Header */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-center">
            <HeaderAndSub
              header="Departments"
              subheader="Manage and monitor all departments."
            />

            <Button
              size="xs"
              onClick={() => setIsOpen(true)}
            >
              <Plus className="mr-1" />
              <span>Add New Department</span>
            </Button>
          </div>
        </div>


        <TableComponent
          columns={columns}
          rows={sortedData}
          showImportExport={selectedRows.length > 0}
          exportToCSV={handleExportCSV}
          tableTitle={`All Departments (${apiResponse?.count || 0})`}
          isLoading={isLoadingData}
          NoavailableTitle={isLoadingData ? "Loading..." : "Departments"}
          createTitle={<Button onClick={() => setIsOpen(true)} size="xs"> <Plus /> Add Department </Button>}

          tableDescription={
            "Structure your organization by creating and managing departments. Assign department heads and organize team hierarchies."
          }
          onSort={handleSort}
          sortOrder={sortState.order}
          sortColumn={sortState.column}
          filterComponents={
            <FilterWrapper
              filterValues={filters}
              onFilterApply={handleFilterApply}
              onFilterClear={handleFilterClear}
              filterOptions={filterOptions}
              isLoading={isLoadingData}
            />
          }
        />
      </div>

      {/* Modals */}
      {isOpen && (
        <AddNewDepartment
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
      )}

      {isEditModalOpen && selectedRow && (
        <AddNewDepartment
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          userData={selectedRow}
          mode="edit"
        />
      )}

      {isDeleteModalOpen && selectedRow && (
        <DeleteDepartment
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          departmentId={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DepartmentsContent />
    </Suspense>
  );
};
export default Page;