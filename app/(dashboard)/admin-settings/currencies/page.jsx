"use client";
import { TableComponent } from '@/components/reusables/table';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { EllipsisVertical, Ban, Plus } from "lucide-react";
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { formatDate } from '@/utils/Utils';
import { Checkbox } from '@/components/ui/checkbox';
import { useExportCSV } from '@/hooks/useExportCSV';
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { useSearchParams } from 'next/navigation';
import { exportExchangeRateCSVApi, listOfCurrencyRate } from '@/apis/admin/currency-rate';
import Addcurrency from './components/Addcurrency';
import DeleteCurrency from './components/DeleteCurrency';
import { Switch } from '@/components/ui/switch';
import { retrieveOrgData, updateOrgData } from '@/apis/settings';
import toast from 'react-hot-toast';
import { HeaderAndSub } from '@/components/reusables/HeaderAndSub';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const CurrencyContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();

  // Initialize useTableFilter
  const initialFilters = useMemo(() => ({
    reference: "",
    title: "",
    type: "",
    minAmount: "",
    maxAmount: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for expense data
  const { data: apiResponse, isLoading: isLoadingData } = useQuery({
    queryKey: ["list-of-currency-rate", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'unsubmitted') {
        apiParams.has_report = false; // Only show expenses without reports
      }

      return listOfCurrencyRate(apiParams);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    keepPreviousData: true
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    if (currentTab === 'unsubmitted') {
      return apiResponse.results.filter(expense => expense.report === null);
    }

    return apiResponse.results;
  }, [apiResponse?.results, currentTab]);

  // Initialize useSort with filtered data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const { data: profileData, isPending: isLoading, error } = useQuery({
    queryKey: ["live-rate"],
    queryFn: retrieveOrgData,
  });

  const defaultCurrency = profileData?.data?.default_currency

  const [liveRatePayload, setLiveRatePayload] = useState({
    default_currency: defaultCurrency,
    use_live_exchange_rate: null,
  });

  // Update state when profile data is loaded
  useEffect(() => {
    if (profileData?.data) {
      setLiveRatePayload({
        default_currency: profileData.data.default_currency,
        use_live_exchange_rate: profileData.data.use_live_exchange_rate,
      });
    }
  }, [profileData]);

  //use live exchange rate
  const { mutateAsync: liveRateMutation, isPending: isUpdatingLiveRate } = useMutation({
    mutationKey: ["live-rate"],
    mutationFn: updateOrgData,
    onSuccess: (response) => {
      toast.success(response?.message)
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
    }
  })

  const handleLiveRateToggle = async (checked) => {
    try {
      const updatedPayload = {
        ...liveRatePayload,
        use_live_exchange_rate: checked
      };
      setLiveRatePayload(updatedPayload);
      await liveRateMutation(updatedPayload);
      queryClient.invalidateQueries("live-rate");
    } catch (error) {
      console.error("Error updating live rate settings:", error);
    }
  };

  const { handleExport } = useExportCSV(exportExchangeRateCSVApi, { filename: 'currencies.csv' });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? filteredData.map(r => r.id) : []);
  }, [filteredData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Memoize filterOptions
  const filterOptions = useMemo(() => ({
    showAmountFilter: true,
    showDateFilter: true,
    inputFilters: [
      {
        key: 'title',
        label: 'Filter by expense name',
        type: 'text'
      },
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      }
    ],
  }), []);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox checked={selectedRows.length === apiResponse?.results?.length} onCheckedChange={handleSelectAll} />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true // Make select column always visible
    },
    {
      accessorKey: "amount_from_display",
      header: "Amount From",
      cell: ({ row }) => row.original.amount_from_display,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "amount_to_display",
      header: "Amount To",
      cell: ({ row }) => row.original.amount_to_display,
      sortable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button aria-haspopup="true" size="icon" variant="ghost">
                  <EllipsisVertical />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault();
                    setIsEditModalOpen(true);
                    setSelectedRow(row);
                  }}
                >Edit</DropdownMenuItem>
                <DropdownMenuItem
                  onSelect={(e) => {
                    e.preventDefault();
                    setIsDeleteModalOpen(true);
                    setSelectedRow(row);
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      sticky: true,
      unhidable: true // Make actions column always visible
    }
  ], [handleSelectAll, selectedRows, apiResponse]);


  const [createCurrency, setCreateCurrency] = useState(false)


  return (
    <>
      <div className="min-h-screen">
        <div className="mx-auto p-4 sm:p-6 lg:p-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start">
            <HeaderAndSub
              header="Currencies"
              subheader={
                <>
                  <div className="flex items-center gap-6">
                    <p className="text-xs">Live exchange rates are automatically fetched. To manually update exchange rate for each currency, use the switch to disable it</p>
                    <Switch
                      size="md"
                      className="text-xs"
                      checked={liveRatePayload.use_live_exchange_rate}
                      onCheckedChange={handleLiveRateToggle}
                      disabled={isUpdatingLiveRate}
                    />
                  </div>
                </>
              }
              subheaderClassName={"w-[70%]"}
            />

            <Button
              onClick={() => setCreateCurrency(true)}
              size="xs"
              disabled={liveRatePayload.use_live_exchange_rate}
            >
              <Plus />
              <span>Add New Currency</span>
            </Button>

          </div>

        </div>

        <TableComponent
          columns={columns}
          rows={sortedData}
          showImportExport={selectedRows.length > 0}
          exportToCSV={handleExportCSV}
          tableTitle={`All Currencies (${apiResponse?.count || 0})`}
          isLoading={isLoadingData}
          NoavailableTitle={isLoadingData ? "Loading..." : apiResponse?.count <= 0 ? "Currencies" : ""}
          tableDescription={
            "Manage organization currencies, set exchange rates, and configure currency-related settings for expense calculations and reports."
          }
          createTitle={
            <Button
              onClick={() => setCreateCurrency(true)}
              size="xs"
              disabled={liveRatePayload.use_live_exchange_rate}
            >
              <Plus />
              Add New Currency
            </Button>
          }
          onSort={handleSort}
          sortOrder={sortState.order}
          sortColumn={sortState.column}
          filterComponents={
            <FilterWrapper
              filterValues={filters}
              onFilterApply={handleFilterApply}
              onFilterClear={handleFilterClear}
              filterOptions={filterOptions}
            />
          }
        />
      </div>

      {isEditModalOpen && selectedRow && (
        <Addcurrency
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          profileData={profileData}
          mode="update"
          selectedCurrencyData={selectedRow?.original}
          isLoading={isLoading}
        />
      )}

      {(isDeleteModalOpen && selectedRow) && (
        <DeleteCurrency
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          currencyId={selectedRow?.original}
        />
      )}

      {createCurrency && (
        <Addcurrency
          isOpen={createCurrency}
          onClose={() => setCreateCurrency(false)}
          profileData={profileData}
          isLoading={isLoading}
          mode="create"
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CurrencyContent />
    </Suspense>
  );
}

export default Page;