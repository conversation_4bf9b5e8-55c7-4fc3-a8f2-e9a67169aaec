"use client"

import { <PERSON><PERSON> } from '@/components/ui/button';
import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog";
import CurrencyFlag from 'react-currency-flags';
import { currencyList } from '@/apis/utilapi';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { exchangeRateApi, updateExchangeRateApi } from '@/apis/admin/currency-rate';
import toast from 'react-hot-toast';

const Addcurrency = ({ isOpen, onClose, isLoading, profileData, mode = "create", selectedCurrencyData }) => {
  
  const defaultCurrency = profileData?.data?.default_currency
  const queryClient = useQueryClient()

  console.log("selectedCurrencyData", selectedCurrencyData)

  const [searchTerm, setSearchTerm] = useState('');
  // Update the initial state to include selectedCurrencyData
  const [selectedCurrency, setSelectedCurrency] = useState(
    selectedCurrencyData?.amount_to_currency || ''
  );

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSearchTerm('');
      // Set the selected currency from the data when in update mode
      setSelectedCurrency(selectedCurrencyData?.amount_to_currency || '');
      setPayload({
        amount_from: selectedCurrencyData?.amount_from || 1.00,
        amount_from_currency: selectedCurrencyData?.amount_from_currency || defaultCurrency,
        amount_to: selectedCurrencyData?.amount_to || 0,
        amount_to_currency: selectedCurrencyData?.amount_to_currency || "",
      });
    }
  }, [isOpen, defaultCurrency, selectedCurrencyData]);

  const [payload, setPayload] = useState({
    amount_from: selectedCurrencyData?.amount_from || 1.00,
    amount_from_currency: selectedCurrencyData?.amount_from_currency || defaultCurrency,
    amount_to: selectedCurrencyData?.amount_to || 0,
    amount_to_currency: selectedCurrencyData?.amount_to_currency || "",
  })

  const { data: currencyData } = useQuery({
    queryKey: ["currency-list"],
    queryFn: currencyList,
  });

  // Filter currencies based on search term
  const filteredCurrencies = currencyData?.data?.filter((item) =>
    item.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleChange = (key, value) => {
    setPayload({ ...payload, [key]: value })
  }

  const handleCurrencySelect = (value) => {
    setSelectedCurrency(value);
    handleChange("amount_to_currency", value);
  }

  const { mutateAsync: addExchangeRateMutation, isPending } = useMutation({
    mutationKey: ["exchange-rate"],
    mutationFn: exchangeRateApi,
    onSuccess: (response) => {
      toast.success(response?.message);
      onClose();
      queryClient.invalidateQueries("list-of-currency-rate");
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      toast.error(error?.message || "Failed to set exchange rate");
    },
  });

  const { mutateAsync: updateExchangeRateMutation, isPending: isUpdating } = useMutation({
    mutationKey: ["update-exchange-rate"],
    mutationFn: updateExchangeRateApi,
    onSuccess: (response) => {
      toast.success(response?.message);
      onClose();
      queryClient.invalidateQueries("list-of-currency-rate");
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      toast.error(error?.message || "Failed to update exchange rate");
    },
  });

  const handleExchangeRate = async (e) => {
    e.preventDefault();

    // Validate form
    if (!payload.amount_to || !payload.amount_to_currency) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      if (mode === "update") {
        await updateExchangeRateMutation({ ...payload, id: selectedCurrencyData?.id });
      } else {
        await addExchangeRateMutation(payload);
      }
    } catch (error) {
      console.log(error);
    }
  }

  // Remove the getCurrencyFlag function as we'll use react-currency-flags instead

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
        <div>
          <div className="rounded-full border w-fit px-3 p-1 text-xs font-semibold">
            1 {defaultCurrency} = {(payload.amount_to) || "0.00"} {payload.amount_to_currency || ""}
          </div>

          <div className="mt-4 space-y-4">
            <div className="rounded-lg p-2 opacity-70 border">
              <p className="text-xs font-semibold">Convert from</p>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  {defaultCurrency && (
                    <CurrencyFlag currency={defaultCurrency} size="md" className="rounded-full overflow-hidden" />
                  )}
                  <h3 className="font-semibold text-sm">{defaultCurrency}</h3>
                </div>

                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-lg">{payload.amount_from}</h3>
                </div>
              </div>
            </div>

            <div className="rounded-lg p-2 border">
              <p className="text-xs font-semibold">Convert to</p>
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center">
                  {selectedCurrency && (
                    <CurrencyFlag currency={selectedCurrency} size="md" className="rounded-full overflow-hidden" />
                  )}
                  <Select
                    value={selectedCurrency}
                    onValueChange={handleCurrencySelect}
                  >
                    <SelectTrigger className="text-[10px] bg-transparent border-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 font-semibold">
                      <SelectValue placeholder="Select currency">
                        {selectedCurrency}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent className="h-56">
                      {/* Search input */}
                      <div className="p-2 sticky top-0 w-full z-10">
                        <Input
                          placeholder="Search currency..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="w-full"
                        />
                      </div>

                      {/* Filtered currency list */}
                      {filteredCurrencies?.map((item, index) => (
                        <SelectItem key={`${item.code}-${index}`} value={item.code} className="text-[10px]">
                          {item.code} - {item.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center gap-2 border-b border-black">
                  <FloatingLabelInput
                    className="font-semibold text-sm border-0 bg-transparent focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                    label="Amount"
                    type="text"
                    step="0.01"
                    min="0"
                    value={(payload.amount_to)}
                    onChange={(e) => handleChange("amount_to", e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-center mt-4">
            <Button
              className="w-full"
              size="xs"
              onClick={handleExchangeRate}
              disabled={isPending || isUpdating || !payload.amount_to || !payload.amount_to_currency}
            >

              <span>
                {isPending || isUpdating
                  ? `${mode === "create" ? "Setting Rate..." : "Updating Rate..."}`
                  : `${mode === "create" ? "Set Rate" : "Update Rate"}`}

              </span>

            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Addcurrency;