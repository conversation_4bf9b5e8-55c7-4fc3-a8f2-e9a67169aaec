import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteExchangeRateApi } from '@/apis/admin/currency-rate';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteCurrency = ({ onClose, isOpen, currencyId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deteleCurrencyMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-exchange-rate"],
    mutationFn: deleteExchangeRateApi
  })

  const handleDelete = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleCurrencyMutation({ id: currencyId?.id })
      toast.success(response?.message || "Currency Deleted Successfully")
      onClose()
      queryClient.invalidateQueries(["list-of-currency-rate"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDelete}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{currencyId?.amount_from_display} {" => "} {currencyId?.amount_to_display}</span>?</>
      }
      description="This action will permanently delete the convertion rate and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteCurrency;