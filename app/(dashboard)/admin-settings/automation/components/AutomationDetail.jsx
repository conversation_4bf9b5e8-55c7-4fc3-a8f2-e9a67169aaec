import React from 'react'
import {
  Sheet,
  SheetContent,
} from "@/components/ui/sheet"
import { formatDate } from '@/utils/Utils'
import styles from '@/utils/status.module.css'
import { useQuery } from '@tanstack/react-query'
import { retrieveAutomaation } from '@/apis/admin/automation'
import Pageloader from '@/utils/spinner/Pageloader'

const AutomationDetail = ({ sheetOpen, setSheetOpen, selectedRow }) => {

  const { data: retrieveAutomationData, isPending } = useQuery({
    queryKey: ["retireve-automation", selectedRow?.id],
    queryFn: () => retrieveAutomaation({ id: selectedRow?.id }),
    enabled: !!selectedRow?.id,
  })

  console.log("selectedRow", selectedRow)
  return (
    <>
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        {selectedRow && (
          <>

            <SheetContent side="right" className="w-[500px] sm:w-[600px] rounded-s-xl">
              {isPending ? (
                <div className="flex items-center justify-center h-full">

                  <Pageloader />
                </div>
              ) : (
                <>
                  {/* Header Info */}
                  <div id="detail" className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
                    <div className='text-table_gray_text text-xs font-medium leading-8'>
                      <div className='border-b'>
                        <span className='font-semibold text-sm'>Automation Details</span>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>Name</span>
                        <span className='text-primary'>{retrieveAutomationData?.data?.name}</span>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>Application</span>
                        <span className='text-primary'>{retrieveAutomationData?.data?.app?.name}</span>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>Type</span>
                        <span>{retrieveAutomationData?.data?.automation_type_display}</span>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>Status</span>
                        <div className={`${styles.statusBadge} ${retrieveAutomationData?.data?.is_active ? styles.approved : styles.pending}`}>
                          {retrieveAutomationData?.data.is_active ? 'Active' : 'Inactive'}
                        </div>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>Created At</span>
                        <span>{formatDate(retrieveAutomationData?.data?.created_at)}</span>
                      </div>

                      <div className='flex justify-between items-center'>
                        <span>App</span>
                        <span>{retrieveAutomationData?.data?.app?.app?.name} - {retrieveAutomationData?.data?.app?.name}</span>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  {retrieveAutomationData?.data?.actions && (

                    <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-4 rounded-xl text-xs">
                      <h3 className='font-semibold text-sm'>Actions:</h3>
                      {retrieveAutomationData?.data?.actions_display?.map((action, index) => (
                        <div key={index} className='flex justify-between items-center'>
                          <span>{action}</span>
                        </div>
                      ))}

                      {retrieveAutomationData?.data?.actions?.map((item) => (
                        <>
                          <div className='text-table_gray_text text-xs font-medium leading-8'>

                            <div className='flex justify-between items-center'>
                              <span>Subject</span>
                              <span>{item?.parameters?.subject || 'No subject provided'}</span>
                            </div>

                            <div className='flex justify-between items-center'>
                              <span>Recipient</span>
                              <span>{item?.parameters?.recipient || 'No recipient provided'}</span>
                            </div>
                          </div>

                          <div className='text-table_gray_text text-xs font-medium leading-8'>
                            <div className='border-b'>
                              <span className='text-sm'>Message</span>
                            </div>
                            <div className='flex justify-between items-center'>
                              <span>{item?.parameters?.message || 'No message provided'}</span>
                            </div>
                          </div>

                          <div>

                          </div>
                        </>

                      ))}
                    </div>
                  )}

                  {/* Triggers */}
                  {retrieveAutomationData?.data?.triggers_display && (

                    <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-4 rounded-xl">
                      <div className='text-table_gray_text text-xs font-medium leading-8'>
                        <div className='border-b'>
                          <span className='font-semibold text-sm'>Triggers</span>
                        </div>

                        {retrieveAutomationData?.data.triggers_display?.length > 0 ? (
                          retrieveAutomationData?.data?.triggers_display.map((trigger, index) => (
                            <div key={index} className='flex justify-between items-center'>
                              <span>{trigger}</span>
                            </div>
                          ))
                        ) : (
                          <div className='flex justify-center items-center text-gray-500'>
                            No triggers configured
                          </div>
                        )}
                      </div>
                    </div>
                  )}


                  {/* Actions */}
                  {/* {retrieveAutomationData?.data.actions_display && (

                  <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-4 rounded-xl">
                    <div className='text-table_gray_text text-xs font-medium leading-8'>
                      <div className='border-b'>
                        <span className='font-semibold text-sm'>Actions</span>
                      </div>

                      {retrieveAutomationData?.data.actions_display?.length > 0 ? (
                        retrieveAutomationData?.data?.actions_display?.map((action, index) => (
                          <div key={index} className='flex justify-between items-center'>
                            <span>{action}</span>
                          </div>
                        ))
                      ) : (
                        <div className='flex justify-center items-center text-gray-500'>
                          No actions configured
                        </div>
                      )}
                    </div>
                  </div>
                )} */}

                </>
              )}


            </SheetContent>

          </>

        )}
      </Sheet>
    </>
  )
}

export default AutomationDetail