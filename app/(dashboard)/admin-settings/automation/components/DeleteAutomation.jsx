import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteAutomationApi } from '@/apis/admin/automation';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteAutomation = ({ onClose, isOpen, automationId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deleteAutomationMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-automation"],
    mutationFn: deleteAutomationApi
  })

  const handleDeleteAutomation = async (e) => {
    e.preventDefault()
    try {
      const response = await deleteAutomationMutation({ id: automationId?.id })
      toast.success(response?.message || "Automation deleted sucessfully")
      onClose()
      queryClient.invalidateQueries(["listOfAutomations"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteAutomation}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{automationId?.name}</span>?</>
      }
      description="This action will permanently delete the automation and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteAutomation;