"use client"

import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { Suspense } from 'react'
import { useQuery } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { EllipsisVertical, Eye, Plus, SquarePen } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { formatDate } from '@/utils/Utils'
import { useRouter, useSearchParams } from 'next/navigation'
import { Checkbox } from '@/components/ui/checkbox'
import { useExportCSV } from '@/hooks/useExportCSV'
import FilterWrapper from '@/components/reusables/FilterWrapper'
import { choicesApi } from '@/apis/utilapi'
import useSort from '@/hooks/useSort'
import useTableFilter from '@/hooks/useTableFilter'
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input'
import { exportAutomationCSVApi, listOfAutomations } from '@/apis/admin/automation'
import DeleteAutomation from './DeleteAutomation'

import styles from '@/utils/status.module.css';
import AutomationDetail from './AutomationDetail'

const AutomationsContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';

  // State management
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedAutomation, setSelectedAutomation] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    type: "",
    status: "",
    name: "",
    date: null,
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices for dropdown options
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch report data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfAutomations", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            date: "date"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'draft') {
        apiParams.is_submitted = false;
      } else if (currentTab !== 'all') {
        apiParams.status = currentTab;
        if (currentTab === 'pending') {
          apiParams.is_submitted = true;
        }
      }

      return listOfAutomations(apiParams);
    },
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    switch (currentTab) {
      case 'draft':
        return apiResponse.results.filter(report => !report.is_submitted);
      case 'pending':
        return apiResponse.results.filter(report => report.is_submitted && report.status === 'pending');
      case 'approved':
        return apiResponse.results.filter(report => report.status === 'approved');
      case 'rejected':
        return apiResponse.results.filter(report => report.status === 'rejected');
      default:
        return apiResponse.results;
    }
  }, [apiResponse?.results, currentTab]);

  // Sort functionality
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  // Handle row selection
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Handle CSV export
  const { handleExport } = useExportCSV(exportAutomationCSVApi, { filename: 'automations.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData?.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true,
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "app",
      header: "Application",
      cell: ({ row }) => row.original.app?.name,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "automation_type_display",
      header: "Type",
      cell: ({ row }) => row.original.automation_type_display,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "automation_type_display",
      header: "Triggers",
      cell: ({ row }) => (
        <div className='flex flex-col gap-2 break-words'>
          {row.original.triggers_display.join(",")}
        </div>
      ),
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "automation_type_display",
      header: "Action",
      cell: ({ row }) => (
        <div className='flex flex-col gap-2 break-words'>
          {row.original.actions_display.join(",")}
        </div>
      ),
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "status_display",
      header: "Status",
      cell: ({ row }) => (
        <div className={`${styles.statusBadge} ${row.original.is_active ? styles.approved : styles.pending}`}>
          {row.original.is_active ? 'Active' : 'Inactive'}
        </div>
      ),
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        // Check if status is approved or rejected
        const isApprovedOrRejected = row.original.status === "approved" || row.original.status === "rejected";

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild >
              <Button aria-haspopup="true" size="icon" variant="ghost"
                className={isApprovedOrRejected ? "opacity-50 cursor-not-allowed" : ""}
              >
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {/* <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  setIsEditModalOpen(true);
                  setSelectedAutomation(row.original);
                }}>
                  Edit
                </DropdownMenuItem> */}
              <DropdownMenuItem
                onClick={(e) => { e.stopPropagation(); setSelectedAutomation(row.original); setSheetOpen(true); }}
              >
                View
              </DropdownMenuItem>

              <DropdownMenuItem onClick={(e) => {
                e.stopPropagation();
                setIsDeleteModalOpen(true);
                setSelectedAutomation(row.original);
              }}>
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>

          </DropdownMenu>
        );
      },
      sticky: true,
      unhidable: true
    }
  ], [selectedRows, sortedData, handleSelectAll]);

  // Memoize filterOptions
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
      {
        key: 'name',
        label: 'Filter by report name',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'type',
        placeholder: 'Report Type',
        options: choicesList?.data?.report_types || [],
        label: "Report Type",
      },
      {
        key: 'status',
        placeholder: 'Status',
        options: choicesList?.data?.approval_status || [],
        label: "Status"
      }
    ],
    showAmountFilter: true,
  }), [choicesList?.data]);

  // Custom filters specific to reports
  const reportCustomFilters = useMemo(() => (
    <>
      <div className="relative">
        <FloatingLabelInput
          id="name"
          name="name"
          label="Filter by report name"
          type="text"
          value={filters.name || ""}
          onChange={(e) => handleFilterApply({ ...filters, name: e.target.value })}
        />
      </div>
    </>
  ), [filters, handleFilterApply]);


  return (
    <>
      <div className="w-full">
        <div className="no-scrollbar">
          <TableComponent
            rows={sortedData || []}
            columns={columns}
            tableTitle={`Automations (${apiResponse?.count})`}
            // onRowClick={}
            NoavailableTitle={isLoading ? "Loading..." : apiResponse?.count <= 0 ? "Automation" : ""}
            createTitle={<Button onClick={() => router.push("/admin-settings/automation/new")} size="xs"> <Plus /> New Automation</Button>}
            tableDescription={
              "Configure and manage automated workflows for expenses, approvals, and notifications. Set up triggers and actions to streamline business processes."
            }
            filterComponents={
              <FilterWrapper
                filterValues={filters}
                onFilterApply={handleFilterApply}
                onFilterClear={handleFilterClear}
                filterOptions={filterOptions}
              />
            }
            exportToCSV={handleExportCSV}
            showImportExport={selectedRows.length > 0}
            onSort={handleSort}
            sortOrder={sortState.order}
            sortColumn={sortState.column}
          />
        </div>
      </div>

      <AutomationDetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedAutomation}
      />
      {isDeleteModalOpen && <DeleteAutomation isOpen onClose={() => setIsDeleteModalOpen(false)} automationId={selectedAutomation} />}

    </>
  );
}

const AutomationList = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AutomationsContent />
    </Suspense>
  );
}

export default AutomationList;
