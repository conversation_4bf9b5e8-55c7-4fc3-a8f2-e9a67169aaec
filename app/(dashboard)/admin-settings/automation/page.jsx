"use client"

import { HeaderAndSub } from '@/components/reusables/HeaderAndSub';
import { Button } from '@/components/ui/button';
import { AlarmClock, AlignEndHorizontal, AudioWaveform, ChevronLeft, ChevronRight, Code, HandCoins, Pencil, Plus, SatelliteDish, Trash2 } from 'lucide-react';
import Link from 'next/link';
import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AutomationList from './components/AutomationList';

const AutomationTemplates = [
  {
    id: 1,
    title: "Expense Report Generation",
    description: "Automatically generate and send expense reports.",
    icon: <AlignEndHorizontal className="h-5 w-5" />
  },
  {
    id: 2,
    title: "Receipt Processing",
    description: "Automatically process and validate receipts.",
    icon: <AlarmClock className="h-5 w-5" />
  },
  {
    id: 3,
    title: "Budget Tracking",
    description: "Automatically track and alert on budget thresholds.",
    icon: <AudioWaveform className="h-5 w-5" />
  },
  {
    id: 4,
    title: "Approval Workflows",
    description: "Automate expense approval processes based on rules.",
    icon: <HandCoins className="h-5 w-5" />
  },
  {
    id: 5,
    title: "Payment Reminders",
    description: "Send automated reminders for pending payments.",
    icon: <SatelliteDish className="h-5 w-5" />
  }
];

const Page = () => {
  const router = useRouter();
  const [activeComponent, setActiveComponent] = useState("active");
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef(null);

  const handleChange = (component) => {
    setActiveComponent(component);
  };

  const handleSetupTemplate = (templateId) => {
    // Navigate to new automation page with template pre-selected
    // router.push(`/admin-settings/automation/new?template=${templateId}`);
  };

  const nextSlide = () => {
    if (currentSlide < Math.ceil(AutomationTemplates.length / 3) - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  // Update slider position when currentSlide changes
  useEffect(() => {
    if (sliderRef.current) {
      sliderRef.current.style.transform = `translateX(-${currentSlide * 100}%)`;
    }
  }, [currentSlide]);

  // Calculate if we should show navigation buttons
  const showNavButtons = AutomationTemplates.length > 3;
  const canGoNext = currentSlide < Math.ceil(AutomationTemplates.length / 3) - 1;
  const canGoPrev = currentSlide > 0;

  return (
    <div className="min-h-screen">
      <div className="flex justify-between items-center p-4">
        <div className="flex items-center justify-between w-full">
          <HeaderAndSub
            header="Automation"
            subheader={"Manage user accounts and assign permissions to control access and roles within the system."}
          />

          <div className="flex my-4">
            <Link href={"/admin-settings/automation/new"}>
              <Button
                size="xs"
              >
                <Plus className="mr-1 h-4 w-4" />
                <span>New Automation</span>
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Template Slider Section */}
      <div className="relative p-4 mb-8">
      <h2 className="text-lg font-medium mb-4">Quick Start Templates</h2>
        <div className="relative">
          {showNavButtons && (
            <button
              onClick={prevSlide}
              disabled={!canGoPrev}
              className={`absolute -left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md ${!canGoPrev ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
                }`}
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
          )}

          <div className="overflow-hidden .px-4">
            <div
              ref={sliderRef}
              className="flex transition-transform duration-300 ease-in-out"
              style={{
                transform: `translateX(-${(currentSlide * 100) / Math.ceil(AutomationTemplates.length / 3)}%)`,
              }}
            >
              {AutomationTemplates.map((template) => (
                <div
                  key={template.id}
                  className="w-1/3 flex-shrink-0 px-2"
                >
                  <div className="p-3 border border-gray-200 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow h-full">
                    <div className="flex gap-x-4">
                      <div className="p-1 bg-gray-100 rounded-md h-8 w-8 flex items-center justify-center">
                        {template.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-sm mb-2">{template.title}</h3>
                        <p className="text-gray-500 text-xs mb-4">{template.description}</p>
                        <Button
                          size="xs"
                          onClick={() => handleSetupTemplate(template.id)}
                        >
                          Set Up
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {showNavButtons && (
            <button
              onClick={nextSlide}
              disabled={!canGoNext}
              className={`absolute -right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-md ${!canGoNext ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
                }`}
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Pagination dots */}
        {showNavButtons && (
          <div className="flex justify-center mt-4">
            {Array.from({ length: Math.ceil(AutomationTemplates.length / 3) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`h-2 w-2 rounded-full mx-1 transition-colors ${currentSlide === index ? 'bg-gray-800' : 'bg-gray-300'
                  }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>


      <AutomationList />
    </div>
  )
}

export default Page;
