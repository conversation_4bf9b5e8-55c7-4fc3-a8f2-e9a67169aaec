import React from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';

const EmailAction = ({
  action,
  index,
  updateAction,
  choicesData,
  handleVariableInsert
}) => {
  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="email-config">
        <AccordionTrigger className="py-2">Email Configuration</AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4 p-2">

            <div className="space-y-2">
              <Label>Recipient</Label>
              <Select
                value={action.recipient || 'submitter'}
                onValueChange={(value) => {
                  updateAction(index, {
                    ...action,
                    recipient: value
                  });
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  {choicesData?.data?.recipients?.map(recipient => (
                    <SelectItem className="text-xs" key={recipient.value} value={recipient.value}>
                      {recipient.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Subject</Label>
              <FloatingLabelInput
                id="email_action"
                label="Enter email subject"
                value={action.subject || ''}
                onChange={(e) => {
                  updateAction(index, {
                    ...action,
                    subject: e.target.value
                  });
                }}
              />
            </div>
            <div className="space-y-2">
              <Label>Message Template</Label>
              <div className="border rounded-md p-2 bg-white">
                {/* <div className="flex flex-wrap gap-2 mb-2">
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{User Name}')}
                  >
                    {'{'}User Name{'}'}
                  </Badge>
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{Report ID}')}
                  >
                    {'{'}Report ID{'}'}
                  </Badge>
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{Amount}')}
                  >
                    {'{'}Amount{'}'}
                  </Badge>
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{Date}')}
                  >
                    {'{'}Date{'}'}
                  </Badge>
                </div> */}
                <Textarea
                  id="email-message-textarea"
                  placeholder="Enter your email message here..."
                  className="min-h-[150px]"
                  value={action.message || ''}
                  onChange={(e) => {
                    updateAction(index, {
                      ...action,
                      message: e.target.value
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default EmailAction;
