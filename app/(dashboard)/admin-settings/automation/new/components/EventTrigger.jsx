import React, { useEffect, useState } from 'react';
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useQuery } from '@tanstack/react-query';
import { getEventTypesChoices, getEventTypesOptions } from '@/apis/admin/automation';
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import ReviewSummary from './ReviewSummary';

const EventTrigger = ({
  trigger,
  index,
  eventTypesData,
  isLoadingEventTypes,
  updateTrigger,
  applicationContext,
  subAppsData
}) => {
  const selectedTriggerType = eventTypesData?.results?.find(t => t.codename === trigger.trigger_type);
  const inputRequired = selectedTriggerType?.input_required;
  const inputIsChoice = selectedTriggerType?.input_is_choice;
  const inputIsChoiceFromDB = selectedTriggerType?.input_choices_from_db;
  
  // Find the selected sub-app from subAppsData
  const selectedSubApp = subAppsData?.results?.find(sa => sa.codename === applicationContext);
  
  // Get app and sub-app information
  const appCodename = selectedSubApp?.app?.codename;
  const subAppCodename = selectedSubApp?.codename;
  
  // Extract event from the trigger_type (the last part after the last underscore)
  const event = selectedTriggerType?.codename;

  // Fetch choices if needed
  const { data: choicesData, isLoading: isLoadingChoices } = useQuery({
    queryKey: ['eventTypeChoices',selectedSubApp?.app?.codename, selectedSubApp?.codename, event, trigger.trigger_type],
    queryFn: () => inputIsChoiceFromDB ? 
      getEventTypesOptions({app: selectedSubApp?.app?.codename, sub_app: selectedSubApp?.codename, event: event}) : 
      getEventTypesChoices({app: selectedSubApp?.app?.codename, sub_app: selectedSubApp?.codename, event: event}),
    enabled: !!trigger.trigger_type && (inputIsChoice || inputIsChoiceFromDB),
  });

  // Refetch choices when trigger type changes
  // useEffect(() => {
  //   if (trigger.trigger_type && inputRequired && inputIsChoice && appCodename && subAppCodename && event) {
  //     refetch();
  //   }
  // }, [trigger.trigger_type, inputRequired, inputIsChoice, appCodename, subAppCodename, event, refetch]);

  return (
    <>
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Trigger Type</Label>
        <Select
          value={trigger.trigger_type}
          onValueChange={(value) => {
            updateTrigger(index, {
              ...trigger,
              trigger_type: value,
              trigger_value: '' // Reset value when type changes
            });
          }}
        >
          <SelectTrigger className="text-xs">
            <SelectValue placeholder="Select trigger type" />
          </SelectTrigger>
          <SelectContent>
            {isLoadingEventTypes ? (
              <SelectItem value="loading" disabled>Loading...</SelectItem>
            ) : (
              eventTypesData?.results?.map(type => (
                <SelectItem className="text-xs" key={type.codename} value={type.codename}>
                  {type.name}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>

      {/* Show value input if required */}
      {trigger.trigger_type && inputRequired && (
        <div className="space-y-2">
          {(inputIsChoice || inputIsChoiceFromDB) ? (
            <div>
              <Label>Value</Label>
              <Select
                value={trigger.trigger_value}
                onValueChange={(value) => {
                  let label = value;
                  if (inputIsChoiceFromDB) {
                    const found = choicesData?.results?.find(option => option.id.toString() === value);
                    label = found?.name || value;
                  } else {
                    const found = choicesData?.data?.find(choice => choice.value.toString() === value);
                    label = found?.label || value;
                  }
                  updateTrigger(index, {
                    ...trigger,
                    trigger_value: value,
                    trigger_name_label: label, // <-- Add this
                  });
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="Select value" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingChoices ? (
                    <SelectItem className="text-xs" value="loading" disabled>Loading choices...</SelectItem>
                  ) : (
                    inputIsChoiceFromDB ? 
                      // For options API (DB choices)
                      choicesData?.results?.map(option => (
                        <SelectItem className="text-xs" key={option.id} value={option.id.toString()}>
                          {option.name}
                        </SelectItem>
                      )) :
                      // For choices API
                      choicesData?.data?.map(choice => (
                        <SelectItem className="text-xs" key={choice.value} value={choice.value}>
                          {choice.label}
                        </SelectItem>
                      ))
                  )}
                </SelectContent>
              </Select>
            </div>
          ) : (
            <Input
              id="event-trigger-value"
              type={selectedTriggerType?.input_type}
              value={trigger.trigger_value}
              onChange={(e) => {
                updateTrigger(index, {
                  ...trigger,
                  trigger_value: e.target.value
                });
              }}
              placeholder={`Enter value for ${selectedTriggerType?.name} *`}
            />
          )}
        </div>
      )}
    </div>
    </>

  );
};

export default EventTrigger;

