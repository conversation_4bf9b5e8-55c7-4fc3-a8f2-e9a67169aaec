import React, { useMemo } from 'react';
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const TimeTrigger = ({ 
  trigger, 
  index, 
  dateTimeTypesData, 
  isLoadingDateTimeTypes, 
  choicesData,
  updateTrigger 
}) => {
  // Find the selected date field type
  const selectedDateField = dateTimeTypesData?.results?.find(
    type => type.codename === trigger.trigger_type
  );

  // Filter date_time_conditions based on can_trigger_after and can_trigger_before
  const filteredConditions = useMemo(() => {
    if (!selectedDateField || !choicesData?.data?.date_time_conditions) {
      return choicesData?.data?.date_time_conditions || [];
    }

    return choicesData.data.date_time_conditions.filter(condition => {
      if (condition.value === 'after' && !selectedDateField.can_trigger_after) {
        return false;
      }
      if (condition.value === 'before' && !selectedDateField.can_trigger_before) {
        return false;
      }
      return true;
    });
  }, [selectedDateField, choicesData?.data?.date_time_conditions]);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Date Field</Label>
        <Select
          value={trigger.trigger_type}
          onValueChange={(value) => {
            // When changing date field, reset condition if it's not valid for the new field
            const newField = dateTimeTypesData?.results?.find(type => type.codename === value);
            let newCondition = trigger.trigger_condition;
            
            if (newField) {
              if (newCondition === 'after' && !newField.can_trigger_after) {
                newCondition = newField.can_trigger_before ? 'before' : '';
              } else if (newCondition === 'before' && !newField.can_trigger_before) {
                newCondition = newField.can_trigger_after ? 'after' : '';
              }
            }
            
            updateTrigger(index, {
              ...trigger,
              trigger_type: value,
              trigger_condition: newCondition
            });
          }}
        >
          <SelectTrigger className="text-xs">
            <SelectValue placeholder="Select date field" />
          </SelectTrigger>
          <SelectContent>
            {isLoadingDateTimeTypes ? (
              <SelectItem value="loading" disabled>Loading...</SelectItem>
            ) : (
              dateTimeTypesData?.results?.map(type => (
                <SelectItem className="text-xs" key={type.codename} value={type.codename}>
                  {type.name}
                </SelectItem>
              ))
            )}
          </SelectContent>
        </Select>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Relative Time</Label>
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              className="w-20"
              placeholder="5"
              value={trigger.trigger_count}
              onChange={(e) => {
                updateTrigger(index, {
                  ...trigger,
                  trigger_count: e.target.value
                });
              }}
            />
            <Select
              value={trigger.trigger_unit}
              onValueChange={(value) => {
                updateTrigger(index, {
                  ...trigger,
                  trigger_unit: value
                });
              }}
            >
              <SelectTrigger className="w-full text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {choicesData?.data?.date_time_units?.map(unit => (
                  <SelectItem key={unit.value} value={unit.value}>
                    {unit.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select
              value={trigger.trigger_condition}
              onValueChange={(value) => {
                updateTrigger(index, {
                  ...trigger,
                  trigger_condition: value
                });
              }}
            >
              <SelectTrigger className="w-full text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {filteredConditions.map(condition => (
                  <SelectItem className="text-xs" key={condition.value} value={condition.value}>
                    {condition.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-2">
          <Label>Time of Day</Label>
          <Input
            type="time"
            className="mt-1"
            value={trigger.trigger_time}
            onChange={(e) => {
              updateTrigger(index, {
                ...trigger,
                trigger_time: e.target.value
              });
            }}
          />
        </div>
      </div>
      
      <div className="mt-3">
        <Label className="text-sm">Execution Cycle</Label>
        <RadioGroup
          value={trigger.trigger_frequency}
          onValueChange={(value) => {
            updateTrigger(index, {
              ...trigger,
              trigger_frequency: value
            });
          }}
          className="flex space-x-4 mt-1"
        >
          {choicesData?.data?.date_time_frequencies?.map(frequency => (
            <div key={frequency.value} className="flex items-center space-x-2">
              <RadioGroupItem value={frequency.value} id={`${frequency.value}-${trigger.id}`} />
              <Label htmlFor={`${frequency.value}-${trigger.id}`} className="text-sm cursor-pointer">
                {frequency.label}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    </div>
  );
};

export default TimeTrigger;
