import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, ArrowRight, Plus, Trash } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import EmailAction from './EmailAction';
import UpdateFieldsAction from './UpdateFieldsAction';
import NotificationAction from './NotificationAction';
import { useMutation } from '@tanstack/react-query';
import { validateActions } from '@/apis/admin/automation';
import { toast } from 'react-hot-toast';

const ActionsConfig = ({ 
  actions, 
  setActions, 
  actionTypesData, 
  isLoadingActionTypes,
  actionTypeFieldsData,
  isLoadingActionTypeFields,
  choicesData,
  handleBack, 
  handleNext, 
  addAction,
  handleVariableInsert,
  isStepComplete,
  subAppsData,
  applicationContext
}) => {
  const [isValidating, setIsValidating] = useState(false);

  const updateAction = (index, updatedAction) => {
    const newActions = [...actions];
    newActions[index] = updatedAction;
    setActions(newActions);
  };

  // Validation mutation
  const { mutate: validateActionsMutation } = useMutation({
    mutationFn: validateActions,
    onSuccess: (data) => {
      // If validation is successful, proceed to next step
      toast.success(data?.message || "Actions validated successfully");
      handleNext();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to validate actions");
    },
    onSettled: () => {
      setIsValidating(false);
    }
  });

  const handleValidateAndNext = () => {
    if (!isStepComplete(4)) return;
    
    setIsValidating(true);
    
    // Prepare payload for validation
    const payload = {
      actions: actions.map(action => {
        if (action.action_type === 'email_alert') {
          return {
            action_type: action.action_type,
            subject: action.subject,
            recipient: action.recipient,
            message: action.message
          };
        } else if (action.action_type === 'update_fields') {
          return {
            action_type: action.action_type,
            field: action.field,
            value: action.value
          };
        } else if (action.action_type === 'in_app_notifications') {
          return {
            action_type: action.action_type,
            notification_type: action.notification_type,
            subject: action.subject,
            recipient: action.recipient,
            message: action.message
          };
        }
        return { action_type: action.action_type };
      })
    };
    
    // Call validation API
    validateActionsMutation(payload);
  };

  return (
    <Card className="w-full .max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Configure Actions</CardTitle>
        <CardDescription>
          Define what happens when your workflow runs
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <ScrollArea className="h-[400px] pr-4">
          {actions.map((action, index) => (
            <div key={action.id} className="mb-6 border rounded-lg p-4 bg-gray-50">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Action {index + 1}</h3>
                {actions.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newActions = [...actions];
                      newActions.splice(index, 1);
                      setActions(newActions);
                    }}
                    className="h-7 text-xs"
                  >
                    <Trash className="mr-1 h-4 w-4" /> Remove
                  </Button>
                )}
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Action Type</Label>
                  <Select
                    value={action.action_type}
                    onValueChange={(value) => {
                      updateAction(index, {
                        ...action,
                        action_type: value,
                        // Reset action-specific fields
                        subject: '',
                        recipient: "submitter",
                        message: '',
                        field: '',
                        value: '',
                        notification_type: 'info',
                        // employees: []
                      });
                    }}
                  >
                    <SelectTrigger className="text-xs">
                      <SelectValue placeholder="Select action type" />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingActionTypes ? (
                        <SelectItem value="loading" disabled>Loading...</SelectItem>
                      ) : (
                        actionTypesData?.results?.map(type => (
                          <SelectItem className="text-xs" key={type.codename} value={type.codename}>
                            {type.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                {action.action_type === 'email_alert' && (
                  <EmailAction 
                    action={action}
                    index={index}
                    updateAction={updateAction}
                    choicesData={choicesData}
                    handleVariableInsert={handleVariableInsert}
                  />
                )}
                
                {action.action_type === 'update_fields' && (
                  <UpdateFieldsAction 
                    action={action}
                    index={index}
                    updateAction={updateAction}
                    actionTypeFieldsData={actionTypeFieldsData}
                    isLoadingActionTypeFields={isLoadingActionTypeFields}
                    subAppsData={subAppsData}
                    applicationContext={applicationContext}
                  />
                )}
                
                {action.action_type === 'in_app_notifications' && (
                  <NotificationAction 
                    action={action}
                    index={index}
                    updateAction={updateAction}
                    choicesData={choicesData}
                    handleVariableInsert={handleVariableInsert}
                  />
                )}
              </div>
            </div>
          ))}
        </ScrollArea>
        <Button
          variant="outline"
          onClick={addAction}
          className="w-full !rounded-button whitespace-nowrap"
        >
          <Plus className="mr-2" /> Add Another Action
        </Button>
      </CardContent>
      <CardFooter className="justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          size="xs"
        >
          <ArrowLeft className="mr-2" /> Back
        </Button>
        <Button
          onClick={handleValidateAndNext}
          disabled={!isStepComplete(4) || isValidating}
          size="xs"
        >
          {isValidating ? "Validating..." : "Continue"} {!isValidating && <ArrowRight className="ml-2" />}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ActionsConfig;
