import React from 'react';
import { Label } from "@/components/ui/label";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useQuery } from '@tanstack/react-query';
import { getActionTypeFieldChoices, getActionTypeFieldOptions } from '@/apis/admin/automation';

const UpdateFieldsAction = ({
  action,
  index,
  updateAction,
  actionTypeFieldsData,
  isLoadingActionTypeFields,
  subAppsData,
  applicationContext
}) => {
  const selectedField = actionTypeFieldsData?.results?.find(field => field.codename === action.field);
  
  const inputIsChoice = selectedField?.input_is_choice;
  const inputIsChoiceFromDB = selectedField?.input_choices_from_db;

  const selectedSubApp = subAppsData?.results?.find(sa => sa.codename === applicationContext);

  // Only fetch choices data if we need it (input is choice-based)
  const { data: actionChoicesData, isLoading: isLoadingChoices } = useQuery({
    queryKey: ['actionTypeChoices', selectedSubApp?.app?.codename, selectedSubApp?.codename, action.action_type, action.field],
    queryFn: () => inputIsChoiceFromDB ?
      getActionTypeFieldOptions({ app: selectedSubApp?.app?.codename, sub_app: selectedSubApp?.codename, action: action.action_type, field: action.field }) :
      getActionTypeFieldChoices({ app: selectedSubApp?.app?.codename, sub_app: selectedSubApp?.codename, action: action.action_type, field: action.field }),
    enabled: !!action.field && !!selectedSubApp && (inputIsChoice || inputIsChoiceFromDB)
  });

  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="update-fields-config">
        <AccordionTrigger className="py-2">Update Fields Configuration</AccordionTrigger>
        <AccordionContent>

          <div className="px-2 mt-4">
            <Label>Field to Update</Label>
          </div>

          <div className=" p-2 flex items-center gap-4 justify-between">
            <div className="space-y-2 w-full">
              <Select
                value={action.field || ''}
                onValueChange={(value) => {
                  updateAction(index, {
                    ...action,
                    field: value,
                    value: '' // Reset value when field changes
                  });
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="Select field" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingActionTypeFields ? (
                    <SelectItem value="loading" disabled>Loading...</SelectItem>
                  ) : (
                    actionTypeFieldsData?.results?.map(field => (
                      <SelectItem className="text-xs" key={field.codename} value={field.codename}>
                        {field.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>

            <div>
              <p className='font-medium whitespace-nowrap text-xs'>Set to</p>
            </div>

            <div className="space-y-2 w-full">
              {/* <Label>New Value</Label> */}
              {(inputIsChoice || inputIsChoiceFromDB) ? (
                <Select
                  value={action.value || ''}
                  onValueChange={(value) => {
                    updateAction(index, {
                      ...action,
                      value: value
                    });
                  }}
                >
                  <SelectTrigger className="text-xs">
                    <SelectValue placeholder="Select value" />
                  </SelectTrigger>
                  <SelectContent>

                    {isLoadingChoices ? (
                      <SelectItem className="text-xs" value="loading" disabled>Loading choices...</SelectItem>
                    ) : (
                      inputIsChoiceFromDB ?
                        // For options API (DB choices)
                        actionChoicesData?.results?.map(option => (
                          <SelectItem className="text-xs" key={option.id} value={option.id.toString()}>
                            {option.name}
                          </SelectItem>
                        )) :
                        // For choices API
                        actionChoicesData?.data?.map(choice => (
                          <SelectItem className="text-xs" key={choice.value} value={choice.value}>
                            {choice.label}
                          </SelectItem>
                        ))
                    )}

                  </SelectContent>
                </Select>
              ) : (
                <Input
                  type={selectedField?.input_type}
                  placeholder={action.field ? `Enter ${selectedField?.name}`: `Enter Value`}
                  value={action.value || ''}
                  onChange={(e) => {
                    updateAction(index, {
                      ...action,
                      value: e.target.value
                    });
                  }}
                />
              )}
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default UpdateFieldsAction;
