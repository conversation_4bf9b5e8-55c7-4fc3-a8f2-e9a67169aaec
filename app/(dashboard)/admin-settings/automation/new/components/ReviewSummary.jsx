import React from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, TestTubeDiagonal, Zap } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";

const ReviewSummary = ({ 
  workflowName, 
  workflowType, 
  applicationContext, 
  triggers,
  actions, 
  subAppsData,
  eventTypesData,
  dateTimeTypesData,
  actionTypesData,
  handleBack, 
  handleCreateAutomation,
  isCreating,
  
}) => {
  // Helper function to get display names
  const getDisplayName = (collection, codename, property = 'name') => {
    const item = collection?.results?.find(item => item.codename === codename);
    return item ? item[property] : codename;
  };

  const getAppName = () => {
    const app = subAppsData?.results?.find(app => app.codename === applicationContext);
    return app ? app.name : applicationContext;
  };

  console.log("tri", triggers)

  return (
    <Card className="w-full .max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Review Automation</CardTitle>
        <CardDescription>
          Review your automation workflow before creating it
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-gray-500">Workflow Name</h3>
              <p className="mt-1 text-lg">{workflowName}</p>
            </div>
            
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-gray-500">Workflow Type</h3>
              <p className="mt-1">
                <Badge variant="outline" className="text-xs">
                  {workflowType === 'event' ? 'Event-based' : 'Date-based'}
                </Badge>
              </p>
            </div>
            
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-gray-500">Application</h3>
              <p className="mt-1">{getAppName()}</p>
            </div>
            
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-gray-500">Triggers</h3>
              <div className="mt-2 space-y-2">
                {triggers.map((trigger, index) => (
                  <div key={index} className="bg-gray-50 p-3 rounded-md">
                    {workflowType === 'event' ? (
                      <p>
                        <span className="font-medium">
                          {getDisplayName(eventTypesData, trigger.trigger_type)}
                        </span>
                        {trigger.trigger_value && (
                          <span className="ml-1">
                            ({trigger.trigger_name_label || trigger.trigger_value})
                          </span>
                        )}
                      </p>
                    ) : (
                      <p>
                        <span className="font-medium">
                          {trigger.trigger_count} {trigger.trigger_unit} {trigger.trigger_condition}
                        </span>
                        <span className="ml-1">
                          {getDisplayName(dateTimeTypesData, trigger.trigger_type)}
                        </span>
                        <span className="ml-1">
                          at {trigger.trigger_time}, {trigger.trigger_frequency}
                        </span>
                      </p>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="border-b pb-3">
              <h3 className="text-sm font-medium text-gray-500">Actions</h3>
              <div className="mt-2 space-y-2">
                {actions.map((action, index) => (
                  <div key={index} className="bg-gray-50 p-3 rounded-md">
                    <p className="font-medium">
                      {getDisplayName(actionTypesData, action.action_type)}
                    </p>
                    {action.action_type === 'email_alert' && (
                      <div className="mt-1 text-sm">
                        <p>To: {action.recipient}</p>
                        <p>Subject: {action.subject}</p>
                      </div>
                    )}
                    {action.action_type === 'update_fields' && (
                      <div className="mt-1 text-sm">
                        <p>Field: {action.field}</p>
                        <p>Value: {action.value}</p>
                      </div>
                    )}
                    {action.action_type === 'in_app_notifications' && (
                      <div className="mt-1 text-sm">
                        <p>Type: {action.notification_type}</p>
                        <p>Recipients: {action.recipient}</p>
                        <p>subject: {action.subject}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            <Alert className="bg-amber-50 border-amber-200">
              <AlertDescription>
                <i className="fas fa-info-circle text-amber-500 mr-2"></i>
                This workflow will run automatically based on your configured triggers. You can edit or deactivate it at any time.
              </AlertDescription>
            </Alert>
          </div>
        </ScrollArea>
      </CardContent>
      <CardFooter className="justify-between">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={handleBack}
            size="xs"
          >
            <ArrowLeft className="mr-2" /> Back
          </Button>
          {/* <Button
            variant="outline"
            size="xs"
          >
            <TestTubeDiagonal className="mr-2" /> Test Workflow
          </Button> */}
        </div>
        <Button
          size="xs"
          className="bg-green-600 hover:bg-green-700 !rounded-button whitespace-nowrap"
          onClick={handleCreateAutomation}
          disabled={isCreating}
        >
          <Zap className="mr-2" /> {isCreating ? "Creating..." : "Activate Workflow"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ReviewSummary;
