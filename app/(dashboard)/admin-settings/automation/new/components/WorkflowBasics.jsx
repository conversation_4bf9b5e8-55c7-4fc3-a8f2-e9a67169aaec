import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { ArrowRight } from 'lucide-react';

const WorkflowBasics = ({ 
  workflowName, 
  setWorkflowName, 
  workflowType, 
  setWorkflowType, 
  handleNext, 
  isStepComplete 
}) => {
  return (
    <Card className="w-full .max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Define Workflow Basics</CardTitle>
        <CardDescription>
          Start by naming your workflow and selecting its type
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <FloatingLabelInput
            id="workflow-name"
            label="Workflow Name *"
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
            className="w-full"
          />
        </div>
        <div className="space-y-3">
          <Label>Workflow Type</Label>
          <RadioGroup
            value={workflowType}
            onValueChange={setWorkflowType}
            className="flex flex-col space-y-3"
          >
            <div
              className={`flex items-start space-x-3 p-4 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors ${workflowType === 'event' ? 'border-black bg-gray-50' : ''}`}
              onClick={() => setWorkflowType('event')}
            >
              <RadioGroupItem value="event" id="event-based" className="mt-1" />
              <div className="space-y-1">
                <Label htmlFor="event-based" className="font-medium cursor-pointer">Event-based</Label>
                <p className="text-sm text-gray-500">Triggered by user or system actions (e.g., Approved, Submitted)</p>
              </div>
            </div>
            <div
              className={`flex items-start space-x-3 p-4 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors ${workflowType === 'time' ? 'border-black bg-gray-50' : ''}`}
              onClick={() => setWorkflowType('time')}
            >
              <RadioGroupItem value="time" id="date-based" className="mt-1" />
              <div className="space-y-1">
                <Label htmlFor="date-based" className="font-medium cursor-pointer">Date-based</Label>
                <p className="text-sm text-gray-500">Triggered by specific dates (e.g., Due Date, Trip Start Date)</p>
              </div>
            </div>
          </RadioGroup>
        </div>
      </CardContent>
      <CardFooter className="justify-end">
        <Button
          size="xs"
          onClick={handleNext}
          disabled={!isStepComplete(1)}
        >
          Continue <ArrowRight className="ml-2" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default WorkflowBasics;