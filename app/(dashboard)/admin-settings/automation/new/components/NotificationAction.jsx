import React from 'react';
import { Label } from "@/components/ui/label";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';

const NotificationAction = ({
  action,
  index,
  updateAction,
  choicesData,
  handleVariableInsert
}) => {
  // console.log("res", action)
  return (
    <Accordion type="single" collapsible className="w-full">
      <AccordionItem value="notification-config">
        <AccordionTrigger className="py-2">Notification Configuration</AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4 p-2">
            <div className="space-y-2">
              <Label>Notification Type</Label>
              <Select
                value={action.notification_type || 'success'}
                onValueChange={(value) => {
                  updateAction(index, {
                    ...action,
                    notification_type: value
                  });
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  {choicesData?.data?.notification_types?.map(type => (
                    <SelectItem className="text-xs" key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Recipient</Label>
              <Select
                value={action.recipient || 'manager'}
                onValueChange={(value) => {
                  updateAction(index, {
                    ...action,
                    recipient: value
                  });
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue placeholder="Select notification type" />
                </SelectTrigger>
                <SelectContent>
                  {choicesData?.data?.recipients?.map(recipient => (
                    <SelectItem className="text-xs" key={recipient.value} value={recipient.value}>
                      {recipient.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Subject</Label>
              <FloatingLabelInput
                placeholder="Enter notification subject"
                value={action.subject || ''}
                onChange={(e) => {
                  updateAction(index, {
                    ...action,
                    subject: e.target.value
                  });
                }}
              />
            </div>

            <div className="space-y-2">
              <Label>Message</Label>
              <div className="border rounded-md p-2 bg-white">
                {/* <div className="flex flex-wrap gap-2 mb-2">
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{User Name}')}
                  >
                    {'{'}User Name{'}'}
                  </Badge>
                  <Badge
                    className="bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer !rounded-button whitespace-nowrap"
                    onClick={() => handleVariableInsert('{Report ID}')}
                  >
                    {'{'}Report ID{'}'}
                  </Badge>
                </div> */}
                <Textarea
                  placeholder="Enter notification message"
                  className="min-h-[100px]"
                  value={action.message || ''}
                  onChange={(e) => {
                    updateAction(index, {
                      ...action,
                      message: e.target.value
                    });
                  }}
                />
              </div>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default NotificationAction;