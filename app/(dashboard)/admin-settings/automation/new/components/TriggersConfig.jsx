import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ArrowLeft, ArrowRight, Plus, Trash } from 'lucide-react';
import EventTrigger from './EventTrigger';
import TimeTrigger from './TimeTrigger';
import { useMutation } from '@tanstack/react-query';
import { validateTriggers } from '@/apis/admin/automation';
import { toast } from 'react-hot-toast';

const TriggersConfig = ({ 
  triggers, 
  setTriggers, 
  workflowType, 
  eventTypesData, 
  isLoadingEventTypes, 
  dateTimeTypesData, 
  isLoadingDateTimeTypes,
  choicesData,
  handleNext, 
  handleBack, 
  addTrigger,
  isStepComplete,
  applicationContext,
  subAppsData
}) => {
  const [isValidating, setIsValidating] = useState(false);

  const updateTrigger = (index, updatedTrigger) => {
    const newTriggers = [...triggers];
    newTriggers[index] = updatedTrigger;
    setTriggers(newTriggers);
  };

  // Validation mutation
  const { mutate: validateTriggersMutation } = useMutation({
    mutationFn: validateTriggers,
    onSuccess: (data) => {
      // If validation is successful, proceed to next step
      toast.success(data?.message || "Triggers validated successfully");
      handleNext();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to validate triggers");
    },
    onSettled: () => {
      setIsValidating(false);
    }
  });

  const handleValidateAndNext = () => {
    if (!isStepComplete(3)) return;
    
    setIsValidating(true);
    
    // Prepare payload for validation
    const payload = {
      automation_type: workflowType,
      triggers: triggers.map(trigger => {
        if (workflowType === 'event') {
          return {
            trigger_type: trigger.trigger_type,
            ...(trigger.trigger_value && { trigger_value: trigger.trigger_value })
          };
        } else {
          return {
            trigger_type: trigger.trigger_type,
            trigger_count: trigger.trigger_count,
            trigger_unit: trigger.trigger_unit,
            trigger_condition: trigger.trigger_condition,
            trigger_time: trigger.trigger_time,
            trigger_frequency: trigger.trigger_frequency
          };
        }
      })
    };
    
    // Call validation API
    validateTriggersMutation(payload);
  };

  return (
    <Card className="w-full .max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Configure Triggers</CardTitle>
        <CardDescription>
          Define when your workflow should run
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <ScrollArea className="h-[300px] pr-4">
          {triggers.map((trigger, index) => (
            <div key={trigger.id} className="mb-6 border rounded-lg p-4 bg-gray-50">
              <div className="flex justify-between items-center mb-3">
                <h3 className="font-medium">Trigger {index + 1}</h3>
                {triggers.length > 1 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newTriggers = [...triggers];
                      newTriggers.splice(index, 1);
                      setTriggers(newTriggers);
                    }}
                    className="h-7 text-xs"
                  >
                    <Trash className="mr-1 h-4 w-4" /> Remove
                  </Button>
                )}
              </div>
              
              {workflowType === 'event' ? (
                <EventTrigger 
                  trigger={trigger}
                  index={index}
                  eventTypesData={eventTypesData}
                  isLoadingEventTypes={isLoadingEventTypes}
                  updateTrigger={updateTrigger}
                  applicationContext={applicationContext}
                  subAppsData={subAppsData}
                />
              ) : (
                <TimeTrigger 
                  trigger={trigger}
                  index={index}
                  dateTimeTypesData={dateTimeTypesData}
                  isLoadingDateTimeTypes={isLoadingDateTimeTypes}
                  choicesData={choicesData}
                  updateTrigger={updateTrigger}
                />
              )}
            </div>
          ))}
        </ScrollArea>
        <Button
          variant="outline"
          onClick={addTrigger}
          className="w-full !rounded-button whitespace-nowrap"
        >
          <Plus className="mr-2" /> Add Another Trigger
        </Button>
      </CardContent>
      <CardFooter className="justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          size="xs"
        >
          <ArrowLeft className="mr-2" /> Back
        </Button>
        <Button
          onClick={handleValidateAndNext}
          disabled={!isStepComplete(3) || isValidating}
          size="xs"
        >
          {isValidating ? "Validating..." : "Continue"} {!isValidating && <ArrowRight className="ml-2" />}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TriggersConfig;
