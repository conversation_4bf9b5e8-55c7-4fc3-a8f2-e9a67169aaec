import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, ArrowRight } from 'lucide-react';

const ApplicationContext = ({ 
  applicationContext, 
  setApplicationContext, 
  subAppsData, 
  isLoadingSubApps, 
  handleNext, 
  handleBack, 
  isStepComplete 
}) => {
  console.log("data", subAppsData)
  return (
    <Card className="w-full .max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl">Select Application Context</CardTitle>
        <CardDescription>
          Choose the application this workflow belongs to
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="application-context">Application</Label>
          <Select
            value={applicationContext}
            onValueChange={setApplicationContext}
          >
            <SelectTrigger className="w-full text-xs">
              <SelectValue placeholder="Select an application" />
            </SelectTrigger>
            <SelectContent>
              {isLoadingSubApps ? (
                <SelectItem value="loading" disabled>Loading...</SelectItem>
              ) : (
                subAppsData?.results?.map(app => (
                  <SelectItem className="text-xs" key={app.codename} value={app.codename}>
                    {app.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          <p className="text-sm text-gray-500 mt-2">
            <i className="fas fa-info-circle mr-1"></i> This selection defines the available triggers, conditions, and data fields.
          </p>
        </div>
      </CardContent>
      <CardFooter className="justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          size="xs"
        >
          <ArrowLeft className="mr-2" /> Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!isStepComplete(2)}
          size="xs"
        >
          Continue <ArrowRight className="ml-2" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ApplicationContext;