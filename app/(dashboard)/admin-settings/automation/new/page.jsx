"use client";
import React, { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

// Import API functions (you'll need to create these)
import {
  getSubApps,
  getEventTypes,
  getDateTimeTypes,
  getActionTypes,
  getActionTypeFields,
  getChoices,
  createAutomation
} from '@/apis/admin/automation';

// Import components
import WorkflowBasics from './components/WorkflowBasics';
import ApplicationContext from './components/ApplicationContext';
import TriggersConfig from './components/TriggersConfig';
import ActionsConfig from './components/ActionsConfig';
import ReviewSummary from './components/ReviewSummary';
import { Check } from 'lucide-react';
import Navigateback from '@/utils/navigateback';

const AutomationCreator = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState(1);
  const [workflowName, setWorkflowName] = useState('');
  const [workflowType, setWorkflowType] = useState('');
  const [applicationContext, setApplicationContext] = useState('');
  const [triggers, setTriggers] = useState([
    {
      id: uuidv4(),
      trigger_type: '',
      trigger_value: '',
      trigger_value_display: '',
      trigger_count: '1',
      trigger_unit: 'days',
      trigger_condition: 'before',
      trigger_time: '09:00',
      trigger_frequency: 'once',
    }
  ]);
  const [actions, setActions] = useState([
    {
      id: uuidv4(),
      action_type: '',
      recipient: 'submitter',
      subject: '',
      message: '',
      field: '',
      value: '',
      notification_type: 'info',
      // employees: []
    }
  ]);

  const messageInputRef = useRef(null);

  // Fetch data
  const { data: subAppsData, isLoading: isLoadingSubApps } = useQuery({
    queryKey: ['subApps'],
    queryFn: getSubApps,
  });

  const { data: eventTypesData, isLoading: isLoadingEventTypes } = useQuery({
    queryKey: ['eventTypes', applicationContext],
    queryFn: () => getEventTypes({ app: applicationContext }, applicationContext),
    enabled: !!applicationContext && workflowType === 'event',
  });

  const { data: dateTimeTypesData, isLoading: isLoadingDateTimeTypes } = useQuery({
    queryKey: ['dateTimeTypes', applicationContext],
    queryFn: () => getDateTimeTypes({ app: applicationContext }, applicationContext),
    enabled: !!applicationContext && workflowType === 'time',
  });

  const { data: actionTypesData, isLoading: isLoadingActionTypes } = useQuery({
    queryKey: ['actionTypes', applicationContext],
    queryFn: () => getActionTypes(applicationContext),
    enabled: !!applicationContext,
  });

  const { data: actionTypeFieldsData, isLoading: isLoadingActionTypeFields } = useQuery({
    queryKey: ['actionTypeFields', applicationContext],
    queryFn: () => getActionTypeFields({ app: applicationContext }, applicationContext),
    enabled: !!applicationContext,
  });

  const { data: choicesData, isLoading: isLoadingChoices } = useQuery({
    queryKey: ['choices'],
    queryFn: getChoices,
  });

  // Create automation mutation
  const { mutate: createAutomationMutation, isPending: isCreating } = useMutation({
    mutationFn: createAutomation,
    onSuccess: (data) => {
      toast.success(data?.message || 'Automation workflow created successfully!');
      queryClient.invalidateQueries(["listOfAutomations2"]);
      router.push('/admin-settings/automation');
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create automation workflow');
    },
  });

  // Helper functions
  const isStepComplete = (step) => {
    switch (step) {
      case 1:
        return workflowName.trim().length > 0;
      case 2:
        return applicationContext !== '';
      case 3:
        return triggers.every(trigger => {
          if (workflowType === 'event') {
            return trigger.trigger_type !== '';
          } else {
            return trigger.trigger_type !== '' && trigger.trigger_time !== '';
          }
        });
      case 4:
        return actions.every(action => {
          if (!action.action_type) return false;

          if (action.action_type === 'email_alert') {
            return action.recipient && action.subject && action.message;
          } else if (action.action_type === 'update_fields') {
            return action.field && action.value;
          } else if (action.action_type === 'in_app_notifications') {
            return action.recipient && action.notification_type && action.message;
          }

          return true;
        });
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (isStepComplete(currentStep)) {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const addTrigger = () => {
    setTriggers(prev => [
      ...prev,
      {
        id: uuidv4(),
        trigger_type: '',
        trigger_value: '',
        trigger_value_display: '',
        trigger_count: '1',
        trigger_unit: 'days',
        trigger_condition: 'before',
        trigger_time: '09:00',
        trigger_frequency: 'once',
      }
    ]);
  };

  const addAction = () => {
    setActions(prev => [
      ...prev,
      {
        id: uuidv4(),
        action_type: '',
        recipient: "submitter",
        subject: '',
        message: '',
        field: '',
        value: '',
        notification_type: 'info',
        // employees: []
      }
    ]);
  };

  const handleVariableInsert = (variable) => {
    // Find the currently focused action with a message field
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT')) {
      const start = activeElement.selectionStart;
      const end = activeElement.selectionEnd;
      const value = activeElement.value;
      const newValue = value.substring(0, start) + variable + value.substring(end);

      // Update the appropriate action
      if (activeElement.id === 'email-message-textarea') {
        // For email actions
        const actionIndex = parseInt(activeElement.closest('[data-action-index]')?.dataset.actionIndex);
        if (!isNaN(actionIndex)) {
          const newActions = [...actions];
          newActions[actionIndex] = {
            ...newActions[actionIndex],
            message: newValue
          };
          setActions(newActions);
        }
      } else {
        // For other textarea/input fields
        activeElement.value = newValue;
        // Trigger change event
        const event = new Event('input', { bubbles: true });
        activeElement.dispatchEvent(event);
      }

      // Set cursor position after the inserted variable
      setTimeout(() => {
        activeElement.focus();
        activeElement.setSelectionRange(start + variable.length, start + variable.length);
      }, 0);
    }
  };

  const handleCreateAutomation = () => {
    const payload = {
      name: workflowName,
      automation_type: workflowType,
      app: applicationContext,
      triggers: triggers.map(trigger => {
        const baseTrigger = {
          trigger_type: trigger.trigger_type
        };

        if (workflowType === 'event') {
          return {
            ...baseTrigger,
            // trigger_type: trigger.trigger_type,
            trigger_value: trigger.trigger_value,
            trigger_value_display: trigger.trigger_value_display,
          }
        } else if (workflowType === 'time') {
          return {
            ...baseTrigger,
            trigger_count: trigger.trigger_count,
            trigger_unit: trigger.trigger_unit,
            trigger_condition: trigger.trigger_condition,
            trigger_time: trigger.trigger_time,
            trigger_frequency: trigger.trigger_frequency,
          }

        }
        return baseTrigger;
      }
      ),
      actions: actions.map(action => {
        const baseAction = {
          action_type: action.action_type
        };

        if (action.action_type === 'email_alert') {
          return {
            ...baseAction,
            recipient: action.recipient,
            subject: action.subject,
            message: action.message
          };
        } else if (action.action_type === 'update_fields') {
          return {
            ...baseAction,
            field: action.field,
            value: action.value
          };
        } else if (action.action_type === 'in_app_notifications') {
          return {
            ...baseAction,
            notification_type: action.notification_type,
            recipient: action.recipient,
            subject: action.subject,
            message: action.message
          };
        }

        return baseAction;
      })
    };

    createAutomationMutation(payload);
  };

  // Render current step
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <WorkflowBasics
            workflowName={workflowName}
            setWorkflowName={setWorkflowName}
            workflowType={workflowType}
            setWorkflowType={setWorkflowType}
            handleNext={handleNext}
            isStepComplete={isStepComplete}
          />
        );
      case 2:
        return (
          <ApplicationContext
            applicationContext={applicationContext}
            setApplicationContext={setApplicationContext}
            subAppsData={subAppsData}
            isLoadingSubApps={isLoadingSubApps}
            handleNext={handleNext}
            handleBack={handleBack}
            isStepComplete={isStepComplete}
          />
        );
      case 3:
        return (
          <TriggersConfig
            triggers={triggers}
            setTriggers={setTriggers}
            workflowType={workflowType}
            eventTypesData={eventTypesData}
            isLoadingEventTypes={isLoadingEventTypes}
            dateTimeTypesData={dateTimeTypesData}
            isLoadingDateTimeTypes={isLoadingDateTimeTypes}
            choicesData={choicesData}
            handleNext={handleNext}
            handleBack={handleBack}
            addTrigger={addTrigger}
            isStepComplete={isStepComplete}
            applicationContext={applicationContext}
            subAppsData={subAppsData}
          />
        );
      case 4:
        return (
          <ActionsConfig
            actions={actions}
            setActions={setActions}
            actionTypesData={actionTypesData}
            isLoadingActionTypes={isLoadingActionTypes}
            actionTypeFieldsData={actionTypeFieldsData}
            isLoadingActionTypeFields={isLoadingActionTypeFields}
            choicesData={choicesData}
            handleNext={handleNext}
            handleBack={handleBack}
            addAction={addAction}
            handleVariableInsert={handleVariableInsert}
            isStepComplete={isStepComplete}
            subAppsData={subAppsData}
            applicationContext={applicationContext}
          />
        );
      case 5:
        return (
          <ReviewSummary
            workflowName={workflowName}
            workflowType={workflowType}
            applicationContext={applicationContext}
            triggers={triggers}
            actions={actions}
            subAppsData={subAppsData}
            eventTypesData={eventTypesData}
            dateTimeTypesData={dateTimeTypesData}
            actionTypesData={actionTypesData}
            handleBack={handleBack}
            handleCreateAutomation={handleCreateAutomation}
            isCreating={isCreating}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6 px-4">
      <Navigateback />
      <div className="mb-8">
        <h1 className="text-3xl font-semibold mb-2">Automation Configuration</h1>
        <p className="text-gray-600">Create powerful workflows to automate your expense management processes</p>
      </div>

      <div className="flex justify-between items-center w-full max-w-2xl .mx-auto mb-8">
        {[1, 2, 3, 4, 5].map((step) => (
          <div key={step} className="flex items-center">
            <div
              className={`text-xs w-8 h-8 rounded-full flex items-center justify-center ${step === currentStep
                ? 'bg-black text-white'
                : step < currentStep
                  ? 'bg-gray-200 text-gray-700'
                  : 'bg-gray-100 text-gray-400'
                }`}
            >
              {step < currentStep ? (
                <Check className="h-5 w-5" />
              ) : (
                step
              )}
            </div>
            {step < 5 && (
              <div
                className={`h-1 w-24 ${step < currentStep ? 'bg-gray-400' : 'bg-gray-200'
                  }`}
              ></div>
            )}
          </div>
        ))}
      </div>

      {renderStep()}
    </div>
  );
};

export default AutomationCreator;
