"use client"

import { TableComponent } from "@/components/reusables/table";
import { But<PERSON> } from "@/components/ui/button";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { CheckCheck, EllipsisVertical, Plus } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox";
import { Suspense, useCallback, useEffect, useMemo, useState } from "react";
import { HeaderAndSub } from "@/components/reusables/HeaderAndSub";
import { formatDate } from "@/utils/Utils";
import { useRouter, useSearchParams } from "next/navigation";
import Navigateback from "@/utils/navigateback";
import { usersApi, exportUsersCSVApi } from "@/apis/admin/users";
import AddNewUser from "./components/AddNewUser";
import DeleteUser from "./components/DeleteUser";
import UpdateUser from "./components/UpdateUser";
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { useExportCSV } from '@/hooks/useExportCSV';
import toast from 'react-hot-toast';
import { StatusBadge } from "@/utils/status";
import { getProfileApi } from "@/apis/profile-management";

const UsersManagementContent = () => {
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';

  // Initialize states
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  // Initialize filters
  const initialFilters = useMemo(() => ({
    search: "",
    dateRange: {
      from: "",
      to: ""
    }
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for users data
  const { data: apiResponse, isLoading: isLoadingData } = useQuery({
    queryKey: ["listOfUsers", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }
      return usersApi(apiParams);
    },
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    return apiResponse.results;
  }, [apiResponse?.results]);

  // Initialize sorting
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData || []);

  // Setup CSV export
  const { handleExport } = useExportCSV(exportUsersCSVApi, {
    filename: 'users.csv'
  });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  // Handle select all
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(row => row.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters, currentTab]);

  // Filter options
  const filterOptions = useMemo(() => ({
    showDateFilter: true,
    dateFilter: {
      label: "Filter by date",
      fromLabel: "From date",
      toLabel: "To date"
    },
    inputFilters: [
      {
        key: 'search',
        label: 'Search users',
        type: 'text',
        placeholder: 'Search by name or email...'
      }
    ],
  }), []);

  const { data: profileData } = useQuery({
    queryKey: ["profile"],
    queryFn: getProfileApi
  });

  // Memoize columns
  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "email",
      header: "Email Address",
      cell: ({ row }) => row.original.email,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: ({ row }) => row.original.phone,
      sortable: true,
      unhidable: true

    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <StatusBadge status={row.original.invitation_status_display} isSubmitted={row.original.is_submitted} />
      ),
      sortable: true,
      unhidable: true

    },
    {
      accessorKey: "roles",
      header: "Role",
      cell: ({ row }) => row.original.roles?.map(item => item.name).join(', '),
      sortable: true
    },
    {
      accessorKey: "departments",
      header: "Department",
      cell: ({ row }) => row.original.departments?.map(item => item.name).join(', '),
      sortable: true
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        // Check if user email matches profile email
        const isCurrentUser = profileData?.data?.email === row.original.email;

        // Don't render dropdown for current user
        if (isCurrentUser) {
          // return null;
          return (
            <Button aria-haspopup="true" size="icon" variant="ghost">
              <CheckCheck />
            </Button>
          )
        }

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button aria-haspopup="true" size="icon" variant="ghost">
                  <EllipsisVertical />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => {
                    setIsEditModalOpen(true);
                    setSelectedRow(row.original);
                  }}
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    setIsDeleteModalOpen(true);
                    setSelectedRow(row.original);
                  }}
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
      sticky: true,
      unhidable: true
    }
  ], [handleSelectAll, selectedRows, sortedData, profileData?.data?.email]);

  return (
    <>
      <div className="min-h-screen">
        <div className=".max-w-4xl mx-auto px-4 border-b">
          {/* Header */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start mb-2">
            <HeaderAndSub
              header="Users Management"
              subheader="Manage and monitor all employees across departments."
            />

            <Button
              size="xs"
              onClick={() => setIsOpen(true)}
            >
              <Plus className="mr-1" />
              <span>Add New User</span>
            </Button>
          </div>
        </div>


        <TableComponent
          columns={columns}
          rows={sortedData}
          showImportExport={selectedRows.length > 0}
          exportToCSV={handleExportCSV}
          tableTitle={`All Users (${apiResponse?.count || 0})`}
          isLoading={isLoadingData}
          NoavailableTitle={isLoadingData ? "Loading..." : "Users Management"}
          tableDescription={
            "Manage user accounts, assign roles and departments, and control access permissions. Keep track of all system users."
          }
          onSort={handleSort}
          sortOrder={sortState.order}
          sortColumn={sortState.column}
          filterComponents={
            <FilterWrapper
              filterValues={filters}
              onFilterApply={handleFilterApply}
              onFilterClear={handleFilterClear}
              filterOptions={filterOptions}
              isLoading={isLoadingData}
            />
          }
        />
      </div>

      {/* Modals */}
      {isOpen && (
        <AddNewUser
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
      )}

      {isEditModalOpen && selectedRow && (
        <UpdateUser
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          userData={selectedRow}
        />
      )}

      {isDeleteModalOpen && selectedRow && (
        <DeleteUser
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          userId={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <UsersManagementContent />
    </Suspense>
  )
}

export default Page;