import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteUsersApi } from '@/apis/admin/users';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteUser = ({ onClose, isOpen, userId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deteleUserMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-user"],
    mutationFn: deleteUsersApi
  })

  const handleDeleteUser = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleUserMutation({ id: userId?.id })
      toast.success(response?.message || "Role Deleted Successfully")
      onClose()
      queryClient.invalidateQueries(["listOfUsers"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (

    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteUser}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{userId?.name || userId?.email}</span>?</>
      }
      description="This action will permanently delete this particular user and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteUser;
