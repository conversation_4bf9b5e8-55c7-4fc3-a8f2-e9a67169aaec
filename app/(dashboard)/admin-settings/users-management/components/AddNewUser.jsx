import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { Button } from '@/components/ui/button';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import { inviteUsersApi } from '@/apis/admin/users';
import { RolesApi } from '@/apis/admin/roles';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { SearchableSelect } from '@/components/reusables/SearchableSelect';
import { Label } from '@/components/ui/label';

const AddNewUser = ({ isOpen, onClose }) => {

  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    email: "",
    role: "",
  })

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value })
    setBackendErrors({ ...backendErrors, [key]: null });
  }

  const { data: apiResponse, isPending: fetchingRole } = useQuery({
    queryKey: ["role"],
    queryFn: RolesApi
  })

  const { mutateAsync: createUserMutation, isPending: isCreating } = useMutation({
    mutationKey: ["create-user"],
    mutationFn: inviteUsersApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfRoles"])
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    }
  })

  const handleAdd = async (e) => {
    e.preventDefault();
    try {
      await createUserMutation(formData); // Call create API
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">

          <DialogHeader>
            <DialogTitle>Add a new user</DialogTitle>
          </DialogHeader>

        <form className="py-6 pt-2" onSubmit={handleAdd}>

          <div className="space-y-2">

            <div>
              <FloatingLabelInput
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
                id="email" name="name" label="Email Address *" type={"email"}
                className={`${backendErrors.email && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="email" />
            </div>

            <div className="relative">
              <Label htmlFor="flight_departed_from" className="text-xs">
                Role
              </Label>
              <div className="relative">
                <SearchableSelect
                  value={formData.role}
                  onValueChange={(value) => handleChange("role", value)}
                  searchPlaceholder='Search for role'
                  placeholder="Select Role *"
                  items={apiResponse?.results}
                  error={backendErrors.role}
                  isLoading={fetchingRole}
                />
              </div>
              <ErrorMessage errors={backendErrors} field="role" />
            </div>

            <div className="flex items-center sm:mt-10 gap-2">
              <Button
                size="xs"
                type="submit"
                className="w-full"
                disabled={isCreating}
              >
                <span>
                  {isCreating ? 'Adding...' : 'Add New User'}
                </span>
              </Button>
            </div>

          </div>


        </form>

      </DialogContent>
    </Dialog>

  )
}

export default AddNewUser