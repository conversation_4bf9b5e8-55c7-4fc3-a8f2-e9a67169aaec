"use client"

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input';
import { Button } from '@/components/ui/button';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import { updateUsersApi } from '@/apis/admin/users';
import { RolesApi } from '@/apis/admin/roles';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Checkbox } from '@/components/ui/checkbox';
import { departmentsApi } from '@/apis/admin/departments';
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

const UpdateUser = ({ isOpen, onClose, userData }) => {

  console.log("data", userData)
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    name: userData?.name || "",
    phone: userData?.phone || "",
    email: userData?.email || "",
    roles: userData?.roles || [],
    departments: userData?.departments || [],
    is_active: userData?.is_active || true
  })

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value })
    setBackendErrors({ ...backendErrors, [key]: null });
  }

  const handleRoleSelect = (roleName) => {
    // Find the role object from API response
    const roleObj = apiResponse?.results?.find(item => item.name === roleName);
    if (!roleObj) return;
    
    // Check if this role is already selected
    const isSelected = formData.roles.some(r => r.id === roleObj.id);
    
    const updatedRoles = isSelected
      ? formData.roles.filter(r => r.id !== roleObj.id)
      : [...formData.roles, roleObj];
    
    handleChange("roles", updatedRoles);
  }

  const handleDepartmentSelect = (deptName) => {
    // Find the department object from API response
    const deptObj = deptResponse?.results?.find(item => item.name === deptName);
    if (!deptObj) return;
    
    // Check if this department is already selected
    const isSelected = formData.departments.some(d => d.id === deptObj.id);
    
    const updatedDepartments = isSelected
      ? formData.departments.filter(d => d.id !== deptObj.id)
      : [...formData.departments, deptObj];
    
    handleChange("departments", updatedDepartments);
  }

  const { data: apiResponse, isPending: fetchingRole } = useQuery({
    queryKey: ["role"],
    queryFn: RolesApi
  })

  const { data: deptResponse, isPending: fetchingDept } = useQuery({
    queryKey: ["listOfDepartments"],
    queryFn: departmentsApi
  })

  const { mutateAsync: updateUsersMutation, isPending: isUpdating } = useMutation({
    mutationKey: ["update-role"],
    mutationFn: updateUsersApi,
    onSuccess: (response) => {
      toast.success(response?.message);
      onClose();
      queryClient.invalidateQueries(["listOfRoles", userData?.id]);
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    },
  });

  // Modify the handleAdd function to transform the data before sending
  const handleAdd = async (e) => {
    e.preventDefault();
    try {
      // Extract just the IDs for roles and departments
      const dataToSend = {
        ...formData,
        roles: formData.roles.map(role => role.id),
        departments: formData.departments.map(dept => dept.id)
      };
      
      await updateUsersMutation({ id: userData?.id, ...dataToSend }); // Call update API
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">

        <form className="p-6 pt-2" onSubmit={handleAdd}>

          <DialogHeader>
            <DialogTitle>Update User</DialogTitle>
          </DialogHeader>

          <div className="mt-4 space-y-6 pt-4">

            <div>
              <FloatingLabelInput
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                id="name" name="name" label="Full Name *" type={"text"}
                className={`${backendErrors.name && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="name" />
            </div>

            <div>
              <FloatingLabelInput
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
                id="email" name="email" label="Email Address *" type={"email"}
                className={`${backendErrors.email && "validate_input"}`}
                disabled
              />
              <ErrorMessage errors={backendErrors} field="email" />
            </div>

            <div>
              <FloatingLabelInput
                value={formData.phone}
                onChange={(e) => handleChange("phone", e.target.value)}
                id="phone" name="phone" label="Phone Number *" type={"text"}
                className={`${backendErrors.phone && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="phone" />
            </div>

            <div className="relative">
              <label className="text-xs font-medium mb-1 block">Roles *</label>
              <div className="relative">
                <Select
                  onValueChange={(value) => handleRoleSelect(value)}
                >
                  <SelectTrigger className={`text-xs ${backendErrors.roles && "validate_input"} min-h-[40px] flex-wrap pr-8`}>
                    {formData.roles.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {formData.roles.map((role, index) => (
                          <Badge key={index} className="bg-transparent text-primary hover:text-secondary border border-primary px-2 flex items-center gap-1 text-[8px]">
                            {role.name}
                            <span 
                              className="cursor-pointer ml-1 border rounded-full p-[2px] inline-flex items-center justify-center"
                              onMouseDown={(e) => {
                                e.preventDefault();
                                handleChange("roles", formData.roles.filter(r => r.id !== role.id));
                              }}
                            >
                              <X size={8} />
                            </span>
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <SelectValue className='text-xs' placeholder="Select Roles" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    {apiResponse?.results?.map((item) => (
                      <SelectItem 
                        key={item?.id} 
                        value={item?.name}
                        className={formData.roles.some(r => r.id === item.id) ? "bg-gray-100 text-xs" : "text-xs"}
                      >
                        {item?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.roles.length > 0 && (
                  <button
                    type="button"
                    className={`absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-100`}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      handleChange("roles", []);
                    }}
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
              <ErrorMessage errors={backendErrors} field="roles" />
            </div>

            <div className="relative">
              <label className="text-xs font-medium mb-1 block">Departments *</label>
              <div className="relative">
                <Select
                  onValueChange={(value) => handleDepartmentSelect(value)}
                >
                  <SelectTrigger className={`text-xs ${backendErrors.departments && "validate_input"} min-h-[40px] flex-wrap pr-8`}>
                    {formData.departments.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {formData.departments.map((dept, index) => (
                          <Badge key={index} className="bg-transparent text-primary hover:text-secondary border border-primary px-2 flex items-center gap-1 text-[8px]">
                            {dept.name}
                            <span 
                              className="cursor-pointer ml-1 border rounded-full p-[2px] inline-flex items-center justify-center"
                              onMouseDown={(e) => {
                                e.preventDefault();
                                handleChange("departments", formData.departments.filter(d => d.id !== dept.id));
                              }}
                            >
                              <X size={8} />
                            </span>
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <SelectValue className='text-xs' placeholder="Select Departments" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    {deptResponse?.results?.map((item, index) => (
                      <SelectItem 
                        key={index} 
                        value={item?.name}
                        className={formData.departments.some(d => d.id === item.id) ? "bg-gray-100 text-xs" : "text-xs"}
                      >
                        {item?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.departments.length > 0 && (
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-100"
                    onMouseDown={(e) => {
                      e.preventDefault();
                      handleChange("departments", []);
                    }}
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
              <ErrorMessage errors={backendErrors} field="departments" />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="is_active" 
                checked={formData.is_active}
                onCheckedChange={(checked) => handleChange("is_active", checked)}
              />
              <label
                htmlFor="is_active"
                className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Is Active
              </label>
            </div>

            <div className="flex items-center sm:mt-10 gap-2">
              <Button
                size="xs"
                type="submit"
                className="w-full"
                disabled={isUpdating}
              >
                <span>
                  {isUpdating ? 'Updating...' : 'Update User'}
                </span>
              </Button>
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default UpdateUser;