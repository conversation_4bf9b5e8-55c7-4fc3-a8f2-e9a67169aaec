"use client"

import Link from "next/link";
import { usePathname } from "next/navigation";

const Settingstab = () => {

  const pathname = usePathname();

  const adminNavigation = [
    {
      name: "Setup",
      path: "/admin-settings"
    },
    {
      name: "General",
      path: "/admin-settings/general"
    },
    {
      name: "Currencies",
      path: "/admin-settings/currencies"
    },
    {
      name: "Role Management",
      path: "/admin-settings/roles-management"
    },
    {
      name: "Departments",
      path: "/admin-settings/departments"
    },
    {
      name: "User Management",
      path: "/admin-settings/users-management"
    },
    {
      name: "Policies",
      path: "/admin-settings/policies"
    },
    {
      name: "Automation",
      path: "/admin-settings/automation"
    },
  ]

  return (
    <>
      <div className="p-4 .px-10 min-h-screen border-r border-gray-100">
        <div className="textTitle">Admin Settings</div>

        <div className="space-y-2 sm:space-y-2 flex flex-col gap-4 whitespace-nowrap mt-6">
          {adminNavigation?.map((item, index) => (
            <Link 
              href={item?.path} 
              key={index} 
              className={`text-xs sm:text-xs font-[500] hover:font-semibold flex items-center h-8 ${
                (item.path === "/admin-settings" && pathname === item.path) || 
                (item.path !== "/admin-settings" && pathname.startsWith(item?.path))
                  ? 'font-semibold border-l-4 border-primary'
                  : ''
              }`}
            > 
              <span className="pl-2">{item?.name}</span> 
            </Link>
          ))}
        </div>

      </div>
    </>
  );
};


export default Settingstab;