"use client";
import { TableComponent } from '@/components/reusables/table';
import { Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { EllipsisVertical, Ban, Plus } from "lucide-react";
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { exportExpenseCSVApi, listOfExpenses } from '@/apis/expenses';
import { formatDate, formatExpenseDate } from '@/utils/Utils';
import { Checkbox } from '@/components/ui/checkbox';
import { listOfExpenseType } from '@/apis/expense-types';
import { useExportCSV } from '@/hooks/useExportCSV';
import ExpenseDetail from '../ExpenseDetail';
import { ExpenseModal } from './ExpenseModals/ExpenseModal';
import DeleteexpenseModal from '../DeleteexpenseModal';
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { useSearchParams } from 'next/navigation';
import TabWrapper from '@/components/reusables/TabWrapper';

const ExpenseContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();

  // Initialize useTableFilter
  const initialFilters = useMemo(() => ({
    reference: "",
    title: "",
    type: "",
    minAmount: "",
    maxAmount: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Query for expense types
  const { data: expenseTypes } = useQuery({
    queryKey: ["expense-type"],
    queryFn: listOfExpenseType,
    staleTime: Infinity
  });

  // Query for expense data
  const { data: expenseData, isPending: isLoadingData } = useQuery({
    queryKey: ["listOfExpenses", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'unsubmitted') {
        apiParams.has_report = false; // Only show expenses without reports
      }

      return listOfExpenses(apiParams);
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    keepPreviousData: true
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!expenseData?.results) return [];

    if (currentTab === 'unsubmitted') {
      return expenseData.results.filter(expense => expense.report === null);
    }

    return expenseData.results;
  }, [expenseData?.results, currentTab]);

  // Initialize useSort with filtered data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  const { handleExport } = useExportCSV(exportExpenseCSVApi, { filename: 'expenses.csv' });

  const handleExportCSV = useCallback(() => {
    handleExport(selectedRows.length ? { ids: selectedRows } : {});
  }, [handleExport, selectedRows]);

  const handleRowClick = useCallback((row) => {
    setSelectedRow(row);
    setSheetOpen(true);
  }, []);

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? filteredData.map(r => r.id) : []);
  }, [filteredData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Memoize filterOptions
  const filterOptions = useMemo(() => ({
    showAmountFilter: true,
    showDateFilter: true,
    inputFilters: [
      {
        key: 'title',
        label: 'Filter by expense name',
        type: 'text'
      },
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      }
    ],
    selectFilters: [
      {
        key: 'type',
        placeholder: 'Expense Type',
        options: expenseTypes?.results || [],
        label: "Expense Type"
      },
    ],
  }), [expenseTypes?.results]);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox checked={selectedRows.length === expenseData?.results?.length} onCheckedChange={handleSelectAll} />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true // Make select column always visible
    },
    {
      accessorKey: "report.name",
      header: "Report",
      cell: ({ row }) => row.original.report?.name || <div className='flex items-center justify-center'>--</div>,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "reference",
      header: "Reference Number",
      cell: ({ row }) => row.original.reference,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "title",
      header: "Expense Title",
      cell: ({ row }) => row.original.title,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "type.name",
      header: "Expense Type",
      cell: ({ row }) => row?.original?.expense_type?.name,
      sortable: true,
    },
    {
      accessorKey: "amount_display",
      header: "Amount",
      cell: ({ row }) => row.original.amount_display,
      sortable: true,
      unhidable: true // Make amount always visible
    },
    {
      accessorKey: "merchant.name",
      header: "Merchant",
      cell: ({ row }) => row?.original?.expense_type?.name === "Mileage" ?
        <div className='flex items-center justify-center'>--</div> :
        row?.original?.merchant?.name,
      sortable: true,
    },
    {
      accessorKey: "date",
      header: "Expense Date",
      cell: ({ row }) => formatExpenseDate(row.original.date),
      sortable: true
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const isApprovedOrRejected = row.original.report?.status === "approved" || row.original.report?.status === "rejected";
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild >
              <Button
                aria-haspopup="true"
                size="icon"
                variant="ghost"
                className={isApprovedOrRejected ? "opacity-50 cursor-not-allowed" : ""}
              >
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            {!isApprovedOrRejected && (
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => { setIsEditModalOpen(true); setSelectedRow(row.original); e.stopPropagation(); }}>
                  Edit
                </DropdownMenuItem>

                <DropdownMenuItem onClick={(e) => {
                  setSelectedRow(row?.original);
                  setSheetOpen(true);
                  e.stopPropagation();
                }}>
                  View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => { setIsDeleteModalOpen(true); setSelectedRow(row.original); e.stopPropagation(); }}>
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            )}
          </DropdownMenu>
        );
      },
      sticky: true,
      unhidable: true // Make actions column always visible
    }
  ], [handleSelectAll, selectedRows, expenseData]);

  // Get counts for tabs
  const getCounts = useMemo(() => {
    if (!expenseData?.results) return { all: 0, unsubmitted: 0 };

    const unsubmittedCount = expenseData.results.filter(expense => expense.report === null).length;
    return {
      all: expenseData.results.length,
      unsubmitted: unsubmittedCount
    };
  }, [expenseData?.results]);

  const [createExpense, setCreateExpense] = useState(false)


  return (
    <>
      <TableComponent
        columns={columns}
        rows={sortedData}
        showImportExport={selectedRows.length > 0}
        exportToCSV={handleExportCSV}
        tableTitle={
          <TabWrapper
            tabs={[
              { value: 'all', label: 'All', count: getCounts.all },
              { value: 'unsubmitted', label: 'Unsubmitted', count: getCounts.unsubmitted },
            ]}
            defaultTab="all"
          />
        }
        onRowClick={handleRowClick}
        isLoading={isLoadingData}
        NoavailableTitle={isLoadingData ? "Loading..." : expenseData?.count <= 0 ? "Expense" : ""}
        createTitle={<Button onClick={() => setCreateExpense(true)} size="xs"> <Plus /> Create Expense </Button>}
        tableDescription={
          "Track and manage all your expenses. Create, edit, and submit expenses for reimbursement."
        } 
        onSort={handleSort}
        sortOrder={sortState.order}
        sortColumn={sortState.column}
        filterComponents={
          <FilterWrapper
            filterValues={filters}
            onFilterApply={handleFilterApply}
            onFilterClear={handleFilterClear}
            filterOptions={filterOptions}
          />
        }
      />

      <ExpenseDetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedRow}
      />

      {isEditModalOpen && selectedRow && (
        <ExpenseModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          mode="edit"
          expenseData={selectedRow}
        />
      )}

      {isDeleteModalOpen && selectedRow && (
        <DeleteexpenseModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          expenseId={selectedRow}
        />
      )}
      {createExpense && (
        <ExpenseModal
          isOpen={createExpense}
          onClose={() => setCreateExpense(false)}
          mode="create"
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ExpenseContent />
    </Suspense>
  );
}

export default Page;
