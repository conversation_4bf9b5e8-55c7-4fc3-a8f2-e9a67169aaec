import React, { useState } from 'react';
import { <PERSON>, Menu, X, <PERSON>Filter, Circle<PERSON>, Trash2 } from 'lucide-react';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createMerchant, deleteMerchant } from "@/apis/merchant";
import ButtonLoader from "@/utils/spinner/ButtonLoader";

export const Merchant = ({ merchantData, onMerchantSelect }) => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMerchant, setSelectedMerchant] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [deletingMerchantId, setDeletingMerchantId] = useState(null);

  const { mutateAsync: merchantMutation, isPending: isCreatingMerchant } = useMutation({
    mutationKey: ["createMerchant"],
    mutationFn: createMerchant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['listOfMerchants'] });
    },
  });

  const { mutateAsync: deleteMerchantMutation } = useMutation({
    mutationKey: ["deletemerchant"],
    mutationFn: deleteMerchant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['listOfMerchants'] });
      setDeletingMerchantId(null);
    },
  });

  const handleCreateTag = async () => {
    if (searchQuery.trim()) {
      try {
        const response = await merchantMutation({ name: searchQuery.trim() });
        if (response) {
          if (merchantData) {
            merchantData.push(response);
          }
          setSearchQuery('');
          setSelectedTag(null);
        }
      } catch (error) {
        console.error('Error creating merchant:', error);
      }
    }
  };

  const handleDeleteTag = async (e, merchantId) => {
    e.stopPropagation(); // Prevent triggering the tag selection
    try {
      setDeletingMerchantId(merchantId);
      await deleteMerchantMutation({ id: merchantId });
    } catch (error) {
      console.error('Error deleting merchant:', error);
      setDeletingMerchantId(null);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && searchQuery.trim()) {
      handleCreateTag();
    }
  };

  const handleTagSelect = (merchant) => {
    setSelectedTag(merchant.name);
    setSearchQuery(merchant.name);
    onMerchantSelect(merchant.id); // Pass the merchant ID to parent
  };

  return (
    <div className="border rounded-md .bg-[#F5F5F5] p-4">
      <div className="max-w-md mx-auto space-y-3">
        {/* Header */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <h1 className="text-[#4A4A4A] text-sm font-semibold">Merchant</h1>
          </div>
          <div className="h-px bg-[#E5E5E5]" />
        </div>

        {selectedTag && (
          <div className="p-4 py-2">
            <div className="flex justify-between items-center">
              <h2 className="text-[#4A4A4A] text-sm">{selectedTag}</h2>
              <button
                onClick={() => {
                  setSelectedTag(null);
                  setSearchQuery('');
                }}
                className="text-black opacity-40 hover:opacity-60"
              >
                <CircleX className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}

        {/* Search Bar */}
        <div className="border-y p-2">
          <div className="flex items-center gap-3">
            <Search className="w-3 h-3 text-[#9F9F9F]" />
            <input
              type="text"
              placeholder="Search or add new merchant....."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1 bg-transparent outline-none text-[#4A4A4A] placeholder-[#9F9F9F]"
            />
            <ListFilter className="w-3 h-3 text-[#9F9F9F]" />
          </div>
        </div>

        {/* Tags UI */}
        <div>
          <div className="flex flex-wrap gap-4">
            {merchantData?.length < 1 && (
              <div className="flex items-center gap-1">
                <span className="px-3 py-3 w-full bg-[#F5F5F5] rounded-lg text-[#4A4A4A] text-xs">
                  No available merchant at the moment, please create a new one.
                </span>
              </div>
            )}
            {merchantData?.map((merchant) => (
              <div key={merchant.id} className="flex items-center gap-1">
                <span
                  onClick={() => handleTagSelect(merchant)}
                  className="px-3 py-1 bg-[#F5F5F5] rounded-lg text-[#4A4A4A] text-sm font-semibold cursor-pointer hover:bg-[#EAEAEA]"
                >
                  {merchant.name}
                </span>
                <React.Fragment>
                  {deletingMerchantId === merchant.id ? (
                    <ButtonLoader />
                  ) : (
                    <span
                      onClick={(e) => handleDeleteTag(e, merchant.id)}
                      className="cursor-pointer bg-red-500 p-1 rounded-full hover:bg-red-600"
                    >
                      <Trash2 className="w-3 h-3 text-white" />
                    </span>
                  )}
                </React.Fragment>
              </div>
            ))}
            {searchQuery && (
              <button
                onClick={handleCreateTag}
                className="px-3 py-1 bg-white rounded-md text-[#000] text-sm hover:text-[#4A4A4A]"
              >
                {isCreatingMerchant ? (
                  <span className='flex items-center gap-2'>
                    <ButtonLoader /> Creating...
                  </span>
                ) : (
                  <span>Create tag</span>
                )}

              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};