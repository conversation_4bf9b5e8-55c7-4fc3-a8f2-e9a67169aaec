import {
  Dialog,
  DialogContent,
} from "@/components/ui/dialog"
import Newexpense from "./Newexpense"
import { useState, useEffect } from "react"
import AddMultiple from "./AddMultiple"

export function ExpenseModal({ isOpen, onClose, reportId, prefillData, mode="create", expenseData=null, files }) {
  const [activeComponent, setActiveComponent] = useState(
    prefillData?.length > 1 ? 'AddMultiple' : 'AllExpense'
  );

  useEffect(() => {
    // If there are multiple receipts, switch to AddMultiple view
    if (prefillData?.length > 1) {
      setActiveComponent('AddMultiple');
    }
  }, [prefillData]);

  // Format single expense prefill data
  const formatSingleExpense = (data) => {
    if (!data) return null;
    
    console.log("Formatting single expense data:", data);
    console.log("Available files:", files);
    
    // Create a properly formatted receipt object for FileUpload
    let receiptForUpload = null;
    
    if (files && files.length > 0) {
      // If we have a file from the OCR process, use it
      receiptForUpload = files[0];
      console.log("Using file from OCR:", receiptForUpload);
    } else if (data.receipt) {
      // If data already has a receipt, use that
      receiptForUpload = data.receipt;
      console.log("Using receipt from data:", receiptForUpload);
    }
    
    return {
      date: data?.date?.toString() || '',
      amount: data.amount?.toString() || '',
      title: data.title || '',
      expense_type: data.expense_type || '',
      description: data.description || '',
      merchant: data.merchant || '',
      report: reportId || '',
      receipt: receiptForUpload
    };
  };

  console.log("prefillData", prefillData)

  const handleChange = (component) => {
    setActiveComponent(component)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
      className="w-full sm:max-w-full h-full overflow-hidden">

          <div className="flex items-center justify-center sticky">
            <div className="text-[10px] sm:text-xs text-gray-600 font-semibold rounded-lg p-1 flex items-center gap-2 sm:gap-3 bg-[#F4F4F5] dark:bg-secondary">
              <div
                className={`px-4 p-2 rounded-md dark:bg-background cursor-pointer ${activeComponent === 'AllExpense' ? 'text-gray-950 shadow-md bg-white dark:bg-primary dark:text-background' : ''}`}
                onClick={() => handleChange('AllExpense')}
                >
                Add Expense
              </div>
              <div 
                className={`px-4 p-2 rounded-md dark:bg-background cursor-pointer ${activeComponent === 'AddMileage' ? 'text-gray-950 shadow-md bg-white dark:bg-primary dark:text-background' : ''}`}
                onClick={() => handleChange('AddMileage')}
                >
                Add Mileage
              </div>
              <div
                className={`px-4 p-2 rounded-md dark:bg-background cursor-pointer ${activeComponent === 'AddMultiple' ? 'text-gray-950 shadow-md bg-white dark:bg-primary dark:text-background' : ''}`}
                onClick={() => handleChange('AddMultiple')}
              >
                Add Multiple
              </div>
            </div>
          </div>

        <div className="sm:min-h-[38rem] overflow-scroll">
          {(activeComponent === 'AllExpense' || activeComponent === 'AddMileage') && (
            <Newexpense
              key={`${activeComponent}-${mode}-${expenseData?.id || 'new'}`} 
              onClose={onClose} 
              reportId={reportId}
              activeComponent={activeComponent} 
              mode={mode}
              expenseData={expenseData}
              prefillData={formatSingleExpense(prefillData?.[0])} // Format the data properly
            />
          )}
          {activeComponent === 'AddMultiple' && (
            <AddMultiple
              reportId={reportId} 
              onClose={onClose}
              prefillData={prefillData?.map((item, index) => ({
                ...item,
                receipt: files?.[index], // Add receipt file to each row
                tempId: `placeholder-${Date.now()}-${index}`
              }))}
            />
          )}
        </div>

      </DialogContent>
    </Dialog>
  )
}
