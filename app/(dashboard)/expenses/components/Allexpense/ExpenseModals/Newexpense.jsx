import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import {
  DialogClose,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon, ChevronDown, Search, SquarePen } from "lucide-react";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { createExpenseApi, updateExpenseApi } from '@/apis/expenses';
import { listOfMerchants } from '@/apis/merchant';
import { listOfExpenseType } from '@/apis/expense-types';
import toast from 'react-hot-toast';
import { choicesApi, currencyList } from '@/apis/utilapi';
import { Input } from '@/components/ui/input';
import { listOfReports } from '@/apis/expense-report';
import FileUpload from '@/utils/FileUpload';
import { ErrorMessage } from '@/utils/validations/ErrorMessage';
import { useRouter } from 'next/navigation';
import { MerchantSelect } from '@/components/reusables/MerchantSelect';

const Newexpense = ({ onClose, reportId, activeComponent, mode, expenseData, prefillData }) => {
  const [open, setOpen] = useState(false)
  const queryClient = useQueryClient();
  const isEditing = mode === "edit";
  const [date, setDate] = useState(null);
  const [backendErrors, setBackendErrors] = useState({});
  const [existingFiles, setExistingFiles] = useState([]);
  const navigate = useRouter();

  // Queries
  const { data: expenseTypeQuery } = useQuery({
    queryKey: ["listOfExpenseType", activeComponent],
    queryFn: () => listOfExpenseType(activeComponent === "AddMileage" ? { is_mileage: true } : { is_mileage: false }),
    enabled: !!activeComponent,
  });

  const { data: currencyData, isPending: isLoading, error } = useQuery({
    queryKey: ["listOfcurrencies"],
    queryFn: currencyList,
  });

  const { data: merchantData } = useQuery({
    queryKey: ["listOfMerchants"],
    queryFn: listOfMerchants,
  });

  const { data: reportList } = useQuery({
    queryKey: ["listOfReports"],
    queryFn: () => listOfReports({ is_submitted: false }),
  });

  const { data: distance_unit_query } = useQuery({
    queryKey: ["choice"],
    queryFn: choicesApi
  });

  // Mutations
  const { mutateAsync: expenseMutation, isPending: isCreating } = useMutation({
    mutationFn: isEditing
      ? (data) => updateExpenseApi(expenseData.id, data)
      : createExpenseApi,
    onSuccess: (data) => {
      toast.success(data?.message || (isEditing ? "Expense updated successfully" : "Expense created successfully"));
      queryClient.invalidateQueries(["listOfExpenses", "retrieveReport"]);
      // navigate.push("/expenses")
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
    },
  });

  // State
  const [formData, setFormData] = useState(() => {
    if (isEditing && expenseData) {
      // Create a properly formatted receipt object for existing receipts
      let receiptObj = null;
      if (expenseData.receipt) {
        receiptObj = {
          name: "Existing Receipt",
          preview: expenseData.receipt, // Use the URL directly as preview
          isExisting: true,
          url: expenseData.receipt // Store the original URL
        };
      }

      return {
        title: expenseData?.title || "",
        expense_type: expenseData?.expense_type?.id ? String(expenseData?.expense_type.id) : "",
        amount: expenseData?.amount || "",
        amount_currency: expenseData?.amount_currency || "USD",
        description: expenseData?.description || "",
        date: expenseData?.date || "",
        merchant: expenseData?.merchant?.id ? String(expenseData?.merchant.id) : "",
        distance: expenseData?.distance || "",
        receipt: receiptObj ? [receiptObj] : [], // Store as array for consistency
        distance_unit: expenseData?.distance_unit || "",
        report: expenseData?.report?.id ? String(expenseData?.report.id) : (reportId || ""),
      };
    } else if (prefillData) {
      return {
        date: prefillData?.date || '',
        amount: prefillData?.amount || '',
        amount_currency: expenseData?.amount_currency || "USD",
        // amount_currency: prefillData?.amount_currency || 'USD',
        title: prefillData?.title || '',
        expense_type: prefillData?.expense_type || '',
        description: prefillData?.description || '',
        merchant: prefillData?.merchant || '',
        report: reportId || prefillData?.report || '',
        receipt: prefillData?.receipt ? [prefillData.receipt] : [], // Handle the receipt file
      };
    } else {
      return {
        date: '',
        amount: '',
        amount_currency: 'USD',
        title: '',
        expense_type: '',
        description: '',
        merchant: '',
        report: reportId || '',
        receipt: [],
      };
    }
  });

  // Update formData when prefillData changes
  useEffect(() => {
    if (!isEditing && prefillData) {
      setFormData((prev) => ({
        ...prev,
        ...prefillData,
      }));
    }
  }, [prefillData, isEditing]);

  // Also initialize date if available in edit mode
  useEffect(() => {
    if (isEditing && expenseData?.date) {
      try {
        const dateObj = new Date(expenseData.date); // Parse date for editing
        setDate(dateObj);
      } catch (error) {
        console.error("Error parsing date:", error);
      }
    } else if (prefillData?.date) {
      try {
        const dateObj = new Date(prefillData.date); // Parse date for prefill
        setDate(dateObj);
      } catch (error) {
        console.error("Error parsing prefill date:", error);
      }
    }
  }, [isEditing, expenseData, prefillData]);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  // Effects
  useEffect(() => {
    if (isEditing && expenseData) {
      // Parse date if available
      if (expenseData.date) {
        try {
          const dateObj = new Date(expenseData.date);
          setDate(dateObj);
        } catch (error) {
          console.error("Error parsing date:", error);
        }
      }

      // Set existing files if available
      if (expenseData.receipts && expenseData.receipts.length > 0) {
        setExistingFiles(expenseData.receipts);
      }

      // Update form data with expense data
      setFormData({
        title: expenseData?.title || "",
        expense_type: expenseData?.expense_type?.id ? String(expenseData?.expense_type.id) : "",
        amount: expenseData?.amount || "",
        amount_currency: expenseData?.amount_currency || "USD",
        description: expenseData?.description || "",
        date: expenseData?.date || "",
        merchant: expenseData?.merchant?.id ? String(expenseData?.merchant.id) : "",
        distance: expenseData?.distance || "",
        receipt: expenseData?.receipts?.[0]?.file || null, // Store the receipt URL
        distance_unit: expenseData?.distance_unit || "",
        report: expenseData?.report?.id ? String(expenseData?.report.id) : (reportId || ""),
      });
    }
  }, [isEditing, expenseData, reportId]);

  useEffect(() => {
    if (activeComponent === 'AddMileage' && expenseTypeQuery?.results?.length > 0 && !isEditing) {
      const mileageType = expenseTypeQuery.results[0];
      setFormData(prev => ({
        ...prev,
        expense_type: String(mileageType.id)
      }));
    }
  }, [activeComponent, expenseTypeQuery?.results, isEditing]);

  // Close dropdown if clicked outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handlers
  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });

    if (key === "date") {
      try {
        const dateObj = new Date(value); // Parse date string into Date object
        setDate(dateObj); // Update local date state
      } catch (error) {
        console.error("Error parsing date:", error);
      }
    }
  };

  const handleFilesUploaded = (files) => {
    console.log("Files uploaded:", files);

    const fileArray = files ? (Array.isArray(files) ? files : [files]) : [];

    // Ensure each file has the necessary properties for display
    const processedFiles = fileArray.map(file => {
      // If it's already a properly formatted file, return it as is
      if (file && file.preview) {
        return file;
      }

      // If it's a standard File object
      if (file instanceof File) {
        // Create a preview URL for images
        const isImage = file.type.startsWith('image/');
        return Object.assign(file, {
          preview: isImage ? URL.createObjectURL(file) : null
        });
      }

      // For other types of objects (like from OCR)
      return file;
    });

    setFormData((prev) => ({
      ...prev,
      receipt: processedFiles,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setBackendErrors({});

    try {
      const formattedDate = date ? format(date, "yyyy-MM-dd") : null;
      const formDataToSend = new FormData();

      // Add basic form fields
      formDataToSend.append("title", formData.title);
      formDataToSend.append("expense_type", formData.expense_type);
      formDataToSend.append("amount", formData.amount);
      formDataToSend.append("amount_currency", formData.amount_currency);
      formDataToSend.append("description", formData.description);
      formDataToSend.append("date", formattedDate);

      // Add report ID
      if (reportId) {
        formDataToSend.append("report", reportId);
      } else if (formData.report) {
        formDataToSend.append("report", formData.report);
      }

      // Add component-specific fields
      if (activeComponent === 'AddMileage') {
        formDataToSend.append("distance", formData.distance);
        formDataToSend.append("distance_unit", formData.distance_unit);
      } else {
        formDataToSend.append("merchant", formData.merchant);
      }

      // Append each receipt file
      if (formData.receipt && formData.receipt.length > 0) {
        const receiptFile = formData.receipt[0];

        // Case 1: It's an existing receipt (from API) and hasn't been changed
        if (receiptFile.isExisting && !receiptFile.changed) {
          console.log("Using existing receipt URL:", receiptFile.url);
          // Don't append anything - the backend will keep the existing receipt
        }
        // Case 2: It's a new file with a preview path (from OCR)
        else if (receiptFile.preview && typeof receiptFile.preview === 'object' && receiptFile.preview.path) {
          console.log("Appending receipt with path:", receiptFile.preview.path);
          formDataToSend.append("receipt_path", receiptFile.preview.path);
        }
        // Case 3: It's a standard File object
        else if (receiptFile instanceof File) {
          console.log("Appending new receipt file");
          formDataToSend.append("receipt", receiptFile);
        }
        // Case 4: It has a URL string as preview (existing receipt)
        else if (receiptFile.preview && typeof receiptFile.preview === 'string') {
          console.log("Using existing receipt URL:", receiptFile.preview);
          // Don't append anything - the backend will keep the existing receipt
        }
      }

      await expenseMutation(formDataToSend);
    } catch (error) {
      console.error("Error creating/updating expense:", error);
    }
  };

  const toggleDropdown = (e) => {
    e.preventDefault();
    setIsOpen((prev) => !prev);
  };

  const filteredCurrencies = (currencyData?.data || []).filter(
    (currency) =>
      currency.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      currency.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Add useEffect to handle prefilled receipt
  useEffect(() => {
    if (prefillData?.receipt) {
      handleFilesUploaded(prefillData.receipt);
    }
  }, [prefillData]);

  // Add useEffect for handling prefill data
  useEffect(() => {
    if (!isEditing && prefillData) {
      console.log("Prefill data received:", prefillData);

      // Update form data with prefill data
      setFormData(prev => ({
        ...prev,
        ...prefillData,
        report: prefillData.report || reportId || prev.report,
      }));

      // If there's a receipt in the prefill data, handle it
      if (prefillData.receipt) {
        console.log("Receipt from prefill:", prefillData.receipt);
        handleFilesUploaded(prefillData.receipt);
      }

      // If there's a date, parse it
      if (prefillData.date) {
        try {
          const dateObj = new Date(prefillData.date);
          setDate(dateObj);
        } catch (error) {
          console.error("Error parsing prefill date:", error);
        }
      }
    }
  }, [prefillData, isEditing, reportId]);

  // Update the useEffect for handling existing receipts
  useEffect(() => {
    if (isEditing && expenseData) {
      // Parse date if available
      if (expenseData.date) {
        try {
          const dateObj = new Date(expenseData.date);
          setDate(dateObj);
        } catch (error) {
          console.error("Error parsing date:", error);
        }
      }

      // Create a properly formatted receipt object for existing receipts
      if (expenseData.receipt) {
        console.log("Found existing receipt:", expenseData.receipt);
        const receiptObj = {
          name: "Existing Receipt",
          preview: expenseData.receipt, // Use the URL directly as preview
          isExisting: true,
          url: expenseData.receipt // Store the original URL
        };

        // Update form data with the receipt
        setFormData(prev => ({
          ...prev,
          receipt: [receiptObj]
        }));
      }
    }
  }, [isEditing, expenseData]);

  // Rest of the component (render) remains the same
  return (
    <div>
      <div className='sm:px-[300px]'>
        <form onSubmit={handleSubmit} className="pt-0">


          <div className="mb-5 flex !items-center my-auto justify-between sticky top-0 z-40 border-b border-1 pb-4">
            <DialogHeader className={"p-0 m-0 space-y-0"}>
              {isEditing ? (
                <DialogTitle>Edit Expense</DialogTitle>
              ) : activeComponent === 'AddMileage' ? (
                <DialogTitle>Add Mileage</DialogTitle>
              ) : (
                <DialogTitle>New Expense</DialogTitle>
              )}
            </DialogHeader>

            <div className="flex items-center mt-4 justify-end gap-2">
              <DialogClose asChild>
                <Button
                  variant="outline"
                  size="xs"
                // className="gap-1 text-xs rounded-full p-3"
                >
                  Cancel
                </Button>
              </DialogClose>

              <Button
                size="xs"
                // className="gap-1 text-xs rounded-full p-3"
                disabled={isCreating}
              >
                {isCreating
                  ? (isEditing ? "Updating..." : "Creating...")
                  : (isEditing ? "Update" : "Create")}
                <SquarePen />
              </Button>
            </div>
          </div>


          <div className="grid gap-6">
            <div>
              <label className="text-xs font-semibold mb-1 block">Report</label>
              <Select
                disabled={reportId ? true : false}
                value={formData.report}
                onValueChange={(value) => handleChange("report", value)}
              >
                <SelectTrigger className="text-gray-500 text-xs">
                  <SelectValue placeholder="Select Report" />
                </SelectTrigger>
                <SelectContent>
                  {reportList?.results?.map((item) => (
                    <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                      {item?.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-4 pt-4">
            <div className="grid grid-cols-2 gap-6">

              <div>
                <FloatingLabelInput
                  value={formData.title}
                  id="expensename"
                  label="Expense Name *"
                  type="text"
                  onChange={(e) => handleChange("title", e.target.value)}
                  className={`${backendErrors.title && "validate_input"}`}
                />
                <ErrorMessage errors={backendErrors} field="title" />
              </div>

              <div>
              <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild className={`${backendErrors.date && "validate_input"}`}>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-between text-left font-normal w-full",
                        !date && "text-muted-foreground"
                      )}
                    >
                      {date ? format(date, "PPP") : <span>Expense Date *</span>}
                      <CalendarIcon />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="flex w-auto flex-col space-y-2 p-2">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={(selectedDate) => {
                        setDate(selectedDate); // Update local date state
                        handleChange("date", format(selectedDate, "yyyy-MM-dd")); // Update formData.date state
                        setOpen(false);
                      }}
                    />
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="date" />
              </div>

            </div>

            <div className="grid grid-cols-2 gap-6">
              {activeComponent === 'AllExpense' && (
                <div>
                  <label className="text-xs font-semibold mb-1 block">Expense Type</label>
                  <Select
                    required
                    value={formData.expense_type}
                    onValueChange={(value) => handleChange("expense_type", value)}
                  >
                    <SelectTrigger className={`text-xs ${backendErrors.expense_type && "validate_input"}`}>
                      <SelectValue placeholder="Expense Type *" />
                    </SelectTrigger>
                    <SelectContent>
                      {expenseTypeQuery?.results?.map((item) => (
                        <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                          {item?.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage errors={backendErrors} field="expense_type" />
                </div>

              )}

              {activeComponent === 'AddMileage' ? (
                <div className='col-span-2'>
                  <div className="flex items-end gap-3">
                    <div className="w-1/5">
                      <label className="text-xs font-semibold mb-1 block">Distance Unit</label>
                      <Select
                        value={formData?.distance_unit}
                        onValueChange={(value) => handleChange("distance_unit", value)}
                      >
                        <SelectTrigger className={`text-xs ${backendErrors.distance_unit && "validate_input"}`}>
                          <SelectValue placeholder="Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {distance_unit_query?.data?.distance_units?.map((item) => (
                            <SelectItem className="text-xs" key={item.value} value={item.value}>
                              {item.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <ErrorMessage errors={backendErrors} field="distance_unit" />

                    </div>
                    <div className="w-4/5">
                      <FloatingLabelInput
                        id="distance"
                        name="distance"
                        label="Distance *"
                        type="number"
                        value={formData.distance}
                        onChange={(e) => handleChange("distance", e.target.value)}
                        className={`${backendErrors.distance && "validate_input"}`}
                      />
                    </div>
                  </div>
                  <ErrorMessage errors={backendErrors} field="distance" />
                </div>

              ) : (

                <div>
                  <label className="text-xs font-semibold mb-1 block">Merchant</label>
                  <MerchantSelect
                    value={formData.merchant}
                    onChange={(value) => handleChange("merchant", value)}
                    merchants={merchantData?.results}
                    error={backendErrors.merchant}
                    className="w-full"
                  />
                  <ErrorMessage errors={backendErrors} field="merchant" />
                </div>
              )}
            </div>

            <div className="flex items-end gap-3">
              <div className="relative rounded-[20px] w-64">
                <label className="text-xs font-semibold mb-1 block">Currency</label>
                {/* Dropdown Trigger */}
                <button
                  ref={buttonRef}
                  onClick={toggleDropdown}
                  role='button'
                  className="w-full rounded-md border border-gray-30 px-4 py-2 text-left h-8 bg-background text-xs flex items-center justify-between"
                >
                  <span>{formData.amount_currency || "Select a currency"}</span>
                  <ChevronDown
                    size={18} className={`text-gray-500 transform ${isOpen ? 'rotate-180' : ''}`}
                  />
                </button>

                {/* Dropdown Content */}
                {isOpen && (
                  <div
                    ref={dropdownRef}
                    className="absolute z-10 mt-2 p-2 w-96 bg-background border shadow-lg text-xs rounded-md"
                  >
                    {/* Search Input */}
                    <div className="flex items-center gap-2 w-full border rounded-md px-2">
                      <Search className="w-3 h-3 text-[#9F9F9F]" />
                      <Input
                        type="search"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        placeholder="Search currency"
                        className="border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 placeholder:text-xs"
                      />
                    </div>

                    {/* Loading/Fetching State */}
                    {isLoading && (
                      <p className="text-center py-2 text-gray-500">Loading...</p>
                    )}

                    {/* Error Handling */}
                    {error && (
                      <p className="text-center py-2 text-red-500">
                        Failed to fetch currencies
                      </p>
                    )}

                    {/* Filtered Options */}
                    {!isLoading && !error && (
                      <ul className="max-h-40 overflow-y-auto">
                        {filteredCurrencies.length > 0 ? (
                          filteredCurrencies.map((currency, index) => (
                            <li
                              key={`${currency.code}-${index}`} // Ensure uniqueness by appending the index
                              onClick={() => {
                                handleChange("amount_currency", currency.code); // Set selected currency code
                                setIsOpen(false); // Close the dropdown when a currency is selected
                              }}
                              className="px-4 py-2 text-xs cursor-pointer hover:bg-secondary"
                            >
                              {currency.code} - {currency.name}
                            </li>
                          ))
                        ) : (
                          <li className="px-4 py-2 text-center text-gray-500">
                            No results found
                          </li>
                        )}
                      </ul>
                    )}
                  </div>
                )}
              </div>

              <div className="w-4/5">
                <FloatingLabelInput
                  id="amount"
                  label="Amount *"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => handleChange("amount", e.target.value)}
                  className={`${backendErrors.amount && "validate_input"}`}
                // required
                />
                <ErrorMessage errors={backendErrors} field="amount" />
              </div>
            </div>

            <div className="flex flex-col .gap-3">
              <label className="text-xs font-semibold mb-1 block">Description</label>
              <Textarea
                // required
                id="description"
                placeholder="Description *"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
                className={`${backendErrors.description && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="description" />

            </div>
          </div>


          <div>
            <div className="my-4">
              <FileUpload
                onFilesUploaded={handleFilesUploaded}
                uploadMode="single"
                initialFiles={formData.receipt ?
                  (Array.isArray(formData.receipt) ? formData.receipt : [formData.receipt])
                  : []}
                key={`file-upload-${JSON.stringify(formData.receipt)}`}
              />
            </div>
          </div>

        </form>
      </div>
    </div>
  );

}

export default Newexpense;
