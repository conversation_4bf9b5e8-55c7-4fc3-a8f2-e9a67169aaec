import React, { useEffect, useState } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { TableComponent } from "@/components/reusables/table";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CircleCheck, CircleX, Download, EllipsisVertical, Plus, Trash2, Upload } from "lucide-react";
import { Input } from "@/components/ui/input";
import { listOfReports } from "@/apis/expense-report";
import { listOfExpenseType } from "@/apis/expense-types";
import { listOfMerchants } from "@/apis/merchant";
import { currencyList } from "@/apis/utilapi";
import { createExpenseApi } from "@/apis/expenses";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";

const AddMultiple = ({ reportId, onClose }) => {
  const queryClient = useQueryClient();

  // Template for new rows
  const [newRowTemplate, setNewRowTemplate] = useState({
    title: "",
    type: "",
    amount: "",
    amount_currency: "",
    description: "description",
    date: "",
    merchant: "",
    report: reportId || "",
  });

  const [newRows, setNewRows] = useState([
    {
      ...newRowTemplate,
      tempId: `placeholder-${Date.now()}-0`,
    },
  ]);

  // Fetch data for dropdowns
  const { data: reportList } = useQuery({ queryKey: ["listOfReports"], queryFn: listOfReports });
  const { data: expenseTypeList } = useQuery({ queryKey: ["expenseTypeList"], queryFn: listOfExpenseType });
  const { data: merchantList } = useQuery({ queryKey: ["merchantList"], queryFn: listOfMerchants });
  const { data: listOfCurrency } = useQuery({ queryKey: ["listOfCurrency"], queryFn: currencyList });

  // API Mutation for creating multiple expenses
  const { mutateAsync: createMultipleExpenses, isPending: isAdding } = useMutation({
    mutationFn: createExpenseApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Expenses created successfully!");
      queryClient.invalidateQueries(["listOfExpenses", "retrieveReport"]);
      onClose();
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to create expenses.");
    },
  });

  // Update a specific row by `tempId` and append a new row if it's the last row
  const updateNewRow = (tempId, updates) => {
    setNewRows((prev) => {
      const updatedRows = prev.map((row) =>
        row.tempId === tempId ? { ...row, ...updates } : row
      );

      // Check if the updated row is the last row and has some data
      const lastRow = updatedRows[updatedRows.length - 1];
      if (
        lastRow.tempId === tempId &&
        (lastRow.date || lastRow.title || lastRow.merchant || lastRow.type || lastRow.amount)
      ) {
        // Append a new row
        return [
          ...updatedRows,
          {
            ...newRowTemplate,
            tempId: `placeholder-${Date.now()}-${updatedRows.length}`,
          },
        ];
      }

      return updatedRows;
    });
  };

  // Delete a specific row by `tempId`
  const handleDeleteRow = (tempId) => {
    setNewRows((prev) => prev.filter((row) => row.tempId !== tempId));
  };

  const handleAddMultiple = async () => {
    // Filter out rows that are incomplete
    const validRows = newRows.filter(
      (row) =>
        row.date &&
        row.title &&
        row.merchant &&
        row.type &&
        row.amount &&
        row.amount_currency
    );

    if (validRows.length === 0) {
      toast.error("Please fill in all required fields before submitting.");
      return;
    }

    // Transform data to exclude `tempId` and send as an array of objects
    const payload = validRows.map(({ tempId, ...rest }) => rest);

    try {
      await createMultipleExpenses(payload); // Send the array of objects to the backend
    } catch (error) {
      console.error("Failed to create multiple expenses:", error);
    }
  };

  useEffect(() => {
    if (reportId) {
      setNewRowTemplate((prev) => ({ ...prev, title: String(reportId) }));
    }
  }, [reportId]);

  const columns = [
    {
      title: "Expense Date",
      accessor: (row) => (
        <input
          type="date"
          className="border-none px-2 py-1 rounded focus:outline-none"
          value={row.date}
          onChange={(e) => updateNewRow(row.tempId, { date: e.target.value })}
          placeholder="Add Expense Date"
        />
      ),
    },
    {
      title: "Title",
      accessor: (row) => (
        <input
          type="text"
          className="border-none px-2 py-1 rounded focus:outline-none"
          value={row.title}
          onChange={(e) => updateNewRow(row.tempId, { title: e.target.value })}
          placeholder="Add Title"
        />
      ),
    },
    {
      title: "Merchant",
      accessor: (row) => (
        <Select
          className="border-none border-0 focus:outline-none focus:border-none focus:border-0"
          onValueChange={(value) => updateNewRow(row.tempId, { merchant: value })}
        >
          <SelectTrigger className="border-none border-0 focus:ring-0 focus:outline-none focus:border-none focus:border-0">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {merchantList?.results?.map((item) => (
              <SelectItem key={item?.id} value={item?.id}>
                {item?.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ),
    },
    {
      title: "Expense Type",
      accessor: (row) => (
        <Select
          className="border-none border-0 focus:outline-none focus:border-none focus:border-0 text-xs bg-transparent hover:bg-transparent"
          onValueChange={(value) => updateNewRow(row.tempId, { type: value })}
        >
          <SelectTrigger className="border-none border-0 focus:ring-0 focus:outline-none focus:border-none focus:border-0">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {expenseTypeList?.results?.map((item) => (
              <SelectItem key={item?.id} value={item?.id}>
                {item?.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ),
    },
    {
      title: "Amount",
      accessor: (row) => (
        <div className="flex items-center gap-2">
          <Select
            className="border-none border-0 focus:outline-none focus:border-none focus:border-0 text-xs"
            onValueChange={(value) => updateNewRow(row.tempId, { amount_currency: value })}
          >
            <SelectTrigger className="border-none border-0 focus:ring-0 focus:outline-none focus:border-none focus:border-0">
              <SelectValue placeholder="USD" />
            </SelectTrigger>
            <SelectContent>
              {listOfCurrency?.data?.map((item) => (
                <SelectItem key={item?.id} value={item?.code}>
                  {item?.code}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <input
            type="number"
            className="border-none px-2 py-1 rounded focus:outline-none"
            value={row.amount}
            onChange={(e) => updateNewRow(row.tempId, { amount: e.target.value })}
            placeholder="Add Amount"
          />
        </div>
      ),
    },
    {
      accessor: (row) => (
        <Trash2
          className="text-red-400 cursor-pointer"
          size={15}
          onClick={() => handleDeleteRow(row.tempId)}
        />
      ),
    },
  ];

  return (
    <div className="sm:px-20">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-2 items-start justify-between mb-3">
        {/* Report Selector */}
        <div className="flex flex-col gap-3">
          <h1 className="text-lg font-semibold md:text-lg">Expenses</h1>
        </div>


        {/* Buttons */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="h-7 gap-1 text-xs rounded-full">
            <Download />
            Download Template
          </Button>
          <Button variant="outline" size="sm" className="h-7 gap-1 text-xs rounded-full">
            <Upload />
            Upload CSV
          </Button>
        </div>
      </div>

      <Select
        value={newRowTemplate.title}
        onValueChange={(value) =>
          setNewRowTemplate((prev) => ({ ...prev, title: value, report: value }))
        }
        disabled={!!reportId}
      >
        <SelectTrigger className="text-xs w-full sm:w-[350px]">
          <SelectValue placeholder="Select a report" />
        </SelectTrigger>
        <SelectContent className="text-xs">
          {reportList?.results?.length > 0 ? (
            reportList?.results?.map((item) => (
              <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                {item?.name}
              </SelectItem>
            ))
          ) : (
            <div className="p-2 text-sm text-gray-500">No reports available</div>
          )}
        </SelectContent>
      </Select>

      {/* Table */}
      <div className="overflow-scroll">
        <TableComponent rows={newRows} columns={columns} tableTitle="New Multiple" />
      </div>

      {/* Submit Button */}
      <div className="relative flex flex-col gap-2 mt-4">
        <div className="absolute right-0">
          <Button size="sm" onClick={handleAddMultiple} disabled={isAdding}>
            {isAdding ? "Adding..." : "Add Expenses"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddMultiple;