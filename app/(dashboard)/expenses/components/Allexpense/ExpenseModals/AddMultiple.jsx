import React, { useEffect, useState } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { TableComponent } from "@/components/reusables/table";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CalendarIcon, CircleCheck, CircleX, Download, Plus, Trash2, Upload } from "lucide-react";
import { Input } from "@/components/ui/input";
import { listOfReports } from "@/apis/expense-report";
import { listOfExpenseType } from "@/apis/expense-types";
import { listOfMerchants } from "@/apis/merchant";
import { currencyList } from "@/apis/utilapi";
import { createMultipleExpenseApi } from "@/apis/expenses";
import { Button } from "@/components/ui/button";
import toast from "react-hot-toast";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { ImagePlus } from "lucide-react";
import Image from "next/image";
import { MerchantSelect } from "@/components/reusables/MerchantSelect";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";

const AddMultiple = ({ reportId, onClose, prefillData }) => {
  const queryClient = useQueryClient();
  const [open, setOpen] = useState(false)


  // Template for new rows
  const [newRowTemplate, setNewRowTemplate] = useState({
    title: "",
    expense_type: "",
    amount: "",
    amount_currency: "USD",
    description: "", // Add description field
    date: "",
    merchant: "",
    report: reportId || "",
    receipt: null, // Add receipt field
  });

  console.log("newRowTemplate", newRowTemplate)

  // Initialize rows with prefillData if available
  const [newRows, setNewRows] = useState(prefillData || [
    {
      ...newRowTemplate,
      tempId: `placeholder-${Date.now()}-0`,
    },
  ]);

  // Fetch data for dropdowns
  const { data: reportList } = useQuery({ queryKey: ["listOfReports"], queryFn: listOfReports });
  const { data: expenseTypeList } = useQuery({ queryKey: ["expenseTypeList"], queryFn: listOfExpenseType });
  const { data: merchantList } = useQuery({ queryKey: ["merchantList"], queryFn: listOfMerchants });
  const { data: listOfCurrency } = useQuery({ queryKey: ["listOfCurrency"], queryFn: currencyList });

  // Add state for backend errors
  const [backendErrors, setBackendErrors] = useState({});

  // API Mutation for creating multiple expenses
  const { mutateAsync: createMultipleExpenses, isPending: isAdding } = useMutation({
    mutationFn: createMultipleExpenseApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Expenses created successfully!");
      queryClient.invalidateQueries(["listOfExpenses", "retrieveReport"]);
      onClose();
    },
    onError: (error) => {
      // Handle array of errors from backend
      if (error?.details && Array.isArray(error.details)) {
        const newErrors = {};

        // Loop through each error object in the array (one per expense)
        error.details.forEach((expenseErrors, index) => {
          // For each field in the error object, add it to newErrors with the row index
          Object.entries(expenseErrors).forEach(([field, messages]) => {
            newErrors[`expenses.${index}.${field}`] = Array.isArray(messages) ? messages[0] : messages;
          });
        });

        setBackendErrors(newErrors);
        console.log("Backend errors:", newErrors);
      } else {
        // Handle non-array error details (global errors)
        setBackendErrors(error?.details || {});
      }

      toast.error(error?.message || "Failed to create expenses.");
    },
  });

  // Update a specific row by `tempId` and append a new row if it's the last row
  const updateNewRow = (tempId, updates) => {
    setNewRows((prev) => {
      const updatedRows = prev.map((row) =>
        row.tempId === tempId ? { ...row, ...updates } : row
      );

      // Check if this is the last row and has required fields filled
      const lastRow = updatedRows[updatedRows.length - 1];
      const isLastRowFilled = lastRow.tempId === tempId &&
        lastRow.date &&
        lastRow.title &&
        lastRow.merchant &&
        lastRow.expense_type &&
        lastRow.amount &&
        lastRow.amount_currency;

      // Only append new row if the last row is completely filled
      if (isLastRowFilled) {
        return [
          ...updatedRows,
          {
            ...newRowTemplate,
            tempId: `placeholder-${Date.now()}-${updatedRows.length}`,
          },
        ];
      }

      return updatedRows;
    });
  };

  // Delete a specific row by `tempId`
  const handleDeleteRow = (tempId) => {
    // Only allow deletion if there's more than one row
    if (newRows.length > 1) {
      setNewRows(newRows.filter(row => row.tempId !== tempId));
    } else {
      toast.error("Cannot delete the last row");
    }
  };

  const handleAddMultiple = async () => {
    // Clear previous errors
    setBackendErrors({});

    const validRows = newRows.filter(
      (row) =>
        row.date &&
        row.title &&
        row.merchant &&
        row.expense_type &&
        row.amount &&
        row.amount_currency
    );

    if (validRows.length === 0) {
      toast.error("Please fill in all required fields before submitting.");
      return;
    }

    try {
      const formData = new FormData();

      validRows.forEach((row, index) => {
        const { tempId, receipt, ...rest } = row;

        // Append each field individually
        if (reportId) {
          formData.append(`expenses[${index}][report]`, parseInt(rest.report) || reportId);
        }
        formData.append(`expenses[${index}][title]`, rest.title);
        formData.append(`expenses[${index}][expense_type]`, parseInt(rest.expense_type));
        formData.append(`expenses[${index}][amount]`, parseFloat(rest.amount));
        formData.append(`expenses[${index}][amount_currency]`, rest.amount_currency);
        formData.append(`expenses[${index}][description]`, rest.description || "Expense Description");
        formData.append(`expenses[${index}][date]`, rest.date);
        formData.append(`expenses[${index}][merchant]`, parseInt(rest.merchant));

        // If there's a receipt file, append it
        if (receipt instanceof File) {
          formData.append(`expenses[${index}][receipt]`, receipt);
        }
      });

      await createMultipleExpenses(formData);
      onClose();
    } catch (error) {
      console.error("Failed to create multiple expenses:", error);
      // Error handling is now done in the mutation's onError callback
    }
  };

  useEffect(() => {
    if (reportId) {
      setNewRowTemplate((prev) => ({ ...prev, title: String(reportId) }));
    }
  }, [reportId]);

  useEffect(() => {
    if (prefillData) {
      // Update rows with prefilled data including receipts
      const updatedRows = prefillData.map((item, index) => ({
        ...item,
        tempId: `temp-${index}`,
        receipt: item.receipt
      }));
      setNewRows(updatedRows);
    }
  }, [prefillData]);

  const columns = [
    {
      title: "Receipt",
      accessor: (row) => (
        <div className="flex items-center gap-2">
          <Input
            type="file"
            accept="image/*,.pdf"
            className="hidden"
            id={`receipt-${row.tempId}`}
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                updateNewRow(row.tempId, { receipt: file });
              }
            }}
          />
          <label
            htmlFor={`receipt-${row.tempId}`}
            className="cursor-pointer flex items-center gap-2"
          >
            {row.receipt ? (
              <Image
                src={URL.createObjectURL(row.receipt)}
                alt="Receipt"
                width={30}
                height={10}
                className="object-cover rounded"
              />
            ) : (
              <div className="h-8 w-8 rounded border border-dashed border-gray-300 flex items-center justify-center">
                <ImagePlus size={15} className="text-gray-400" />
              </div>
            )}
          </label>
        </div>
      ),
    },
    {
      title: "Expense Date",
      accessor: (row) => (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={`w-full justify-start text-left font-normal ${!row.date && "text-muted-foreground"
                }`}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {row.date ? format(new Date(row.date), "PPP") : "Pick a date"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <CalendarComponent
              mode="single"
              selected={row.date ? new Date(row.date) : undefined}
              onSelect={(date) => {
                updateNewRow(row.tempId, {
                  date: date ? format(date, "yyyy-MM-dd") : ""
                });
                setOpen(false);

              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      ),
    },
    {
      title: "Title",
      accessor: (row, rowIndex) => (
        <div>
          <input
            type="text"
            className={`px-2 py-1 rounded focus:outline-none text-xs placeholder:text-[10px] ${backendErrors[`expenses.${rowIndex}.title`] ? "validate_input" : ""
              }`}
            value={row.title}
            onChange={(e) => updateNewRow(row.tempId, { title: e.target.value })}
            placeholder="Add Title"
          />
          {backendErrors[`expenses.${rowIndex}.title`] && (
            <div className="text-xs text-red-500 mt-1">
              {backendErrors[`expenses.${rowIndex}.title`]}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Merchant",
      accessor: (row, rowIndex) => (
        <div>
          <MerchantSelect
            value={row.merchant}
            merchantPlaceholder="Merchant"
            onChange={(value) => updateNewRow(row.tempId, { merchant: value })}
            merchants={merchantList?.results}
            error={backendErrors[`expenses.${rowIndex}.merchant`]}
          />
          {backendErrors[`expenses.${rowIndex}.merchant`] && (
            <div className="text-xs text-red-500 mt-1">
              {backendErrors[`expenses.${rowIndex}.merchant`]}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Expense Type",
      accessor: (row) => (
        <Select
          // className="border-none border-0 focus:outline-none focus:border-none focus:border-0 text-xs bg-transparent hover:bg-transparent"
          onValueChange={(value) => updateNewRow(row.tempId, { expense_type: value })}
        >
          <SelectTrigger className="text-xs w-18 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {expenseTypeList?.results?.map((item) => (
              <SelectItem className="text-xs" key={item?.id} value={item?.id}>
                {item?.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ),
    },
    {
      title: "Amount",
      accessor: (row, rowIndex) => (
        <div className="flex items-center gap-2">
          <Select
            onValueChange={(value) => updateNewRow(row.tempId, { amount_currency: value || "USD" })}
          >
            <SelectTrigger className={`text-xs w-18 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate ${backendErrors[`expenses.${rowIndex}.amount_currency`] ? 'border-red-500' : ''
              }`}>
              <SelectValue placeholder="Select Currency" />
            </SelectTrigger>
            <SelectContent>
              {listOfCurrency?.data?.map((item) => (
                <SelectItem className="text-xs" key={item?.id} value={item?.code}>
                  {item?.code}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <input
            type="number"
            className={`px-2 py-1 rounded focus:outline-none text-xs placeholder:text-[10px] ${backendErrors[`expenses.${rowIndex}.amount`]
              ? 'border border-red-500'
              : 'border-none'
              }`}
            value={row.amount}
            onChange={(e) => updateNewRow(row.tempId, { amount: e.target.value })}
            placeholder="Add Amount"
          />
          {(backendErrors[`expenses.${rowIndex}.amount`] || backendErrors[`expenses.${rowIndex}.amount_currency`]) && (
            <div className="text-xs text-red-500 mt-1 absolute -bottom-5">
              {backendErrors[`expenses.${rowIndex}.amount`] || backendErrors[`expenses.${rowIndex}.amount_currency`]}
            </div>
          )}
        </div>
      ),
    },
    {
      title: "Description",
      accessor: (row, rowIndex) => (
        <div>
          <input
            type="text"
            className={`px-2 py-1 rounded focus:outline-none text-xs placeholder:text-[10px] ${backendErrors[`expenses.${rowIndex}.description`]
              ? 'border border-red-500'
              : 'border-none'
              }`}
            value={row.description}
            onChange={(e) => updateNewRow(row.tempId, { description: e.target.value })}
            placeholder="Add Short Description"
          />
          {backendErrors[`expenses.${rowIndex}.description`] && (
            <div className="text-xs text-red-500 mt-1">
              {backendErrors[`expenses.${rowIndex}.description`]}
            </div>
          )}
        </div>
      ),
    },
    {
      accessor: (row) => (
        <Trash2
          className="text-red-400 cursor-pointer"
          size={15}
          onClick={() => handleDeleteRow(row.tempId)}
        />
      ),
    },
  ];

  // Add a debug display for errors during development (optional, can be removed later)
  useEffect(() => {
    if (Object.keys(backendErrors).length > 0) {
      console.log("Current backend errors:", backendErrors);
    }
  }, [backendErrors]);

  return (
    <div className="sm:px-20">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-2 items-start justify-between mb-3">
        {/* Report Selector */}
        <div className="flex flex-col gap-3">
          <h1 className="text-lg font-semibold md:text-lg">Expenses</h1>
        </div>


        {/* Buttons */}
        {/* <div className="flex gap-2">
          <Button variant="outline" size="xs">
            <Download />
            Download Template
          </Button>
          <Button variant="outline" size="xs">
            <Upload />
            Upload CSV
          </Button>
        </div> */}
      </div>

      <Select
        value={newRowTemplate.title}
        onValueChange={(value) =>
          setNewRowTemplate((prev) => ({ ...prev, title: value, report: value }))
        }
        disabled={!!reportId}
      >
        <SelectTrigger className="text-xs w-full sm:w-[350px]">
          <SelectValue placeholder="Select a report" />
        </SelectTrigger>
        <SelectContent className="text-xs">
          {reportList?.results?.length > 0 ? (
            reportList?.results?.map((item) => (
              <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                {item?.name}
              </SelectItem>
            ))
          ) : (
            <div className="p-2 text-sm text-gray-500">No reports available</div>
          )}
        </SelectContent>
      </Select>

      {/* Table */}
      <div className="overflow-scroll">
        <TableComponent
          rows={newRows}
          columns={columns}
          tableTitle="New Multiple"
          showImportExport={false}
          showFilter={false}
          showColumnFilter={false}
        />
        {/* Display any global errors */}
        {backendErrors.global && (
          <div className="text-red-500 text-sm mt-2">
            {backendErrors.global}
          </div>
        )}
      </div>

      {/* Submit Button */}
      <div className="relative flex flex-col gap-2 mt-4">
        <div className="absolute right-0">
          <Button size="xs" onClick={handleAddMultiple} disabled={isAdding}>
            {isAdding ? "Adding..." : "Add Expenses"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AddMultiple;
