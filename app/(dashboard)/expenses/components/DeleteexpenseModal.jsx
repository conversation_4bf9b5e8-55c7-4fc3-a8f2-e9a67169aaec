import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteExpense } from '@/apis/expenses';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteexpenseModal = ({ onClose, isOpen, expenseId }) => {
  
  const queryClient = useQueryClient()

  const { mutateAsync: deleteExpenseMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-expense"],
    mutationFn: deleteExpense
  })

  const handleDeleteExpense = async (e) => {
    e.preventDefault()
    try {
      const response = await deleteExpenseMutation({ id: expenseId?.id })
      toast.success(response?.message || "Expense deleted sucessfully")
      onClose()
      queryClient.invalidateQueries(["listOfAdvances"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteExpense}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{expenseId?.title}</span>?</>
      }
        description="This action will permanently delete the expense and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteexpenseModal;