"use client"

import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { ExpenseModal } from "./components/Allexpense/ExpenseModals/ExpenseModal";

export default function ExpenseLayout({ children }) {


  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>

      <div className="overflow-x-hidden grid auto-rows-max">
        {/* {showHeader && ( */}
          <div className=".mb-4 px-4 lg:p-4 border-b">
            <div className="flex flex-row items-center justify-between w-full gap-4">

              <div>
                <h3 className="text-sm font-semibold">Expenses</h3>
              </div>

              <div className="flex items-center gap-3">

                {/* <div><ExpenseModal /></div> */}
                <div>
                  <Button
                    size="xs"
                    // className={`gap-1 rounded-full p-3`}
                    onClick={openModal}
                  >
                    <Plus />
                    <span>New Expense</span>
                  </Button>
                </div>

              </div>
            </div>

          </div>
        {/* )} */}

        <ExpenseModal
          isOpen={isModalOpen}
          onClose={closeModal}
        />

        <main className="overflow-x-scroll">{children}</main>
      </div>
    </>
  )
}
