import React from 'react'
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteAdvancesApi } from '@/apis/advances';
import toast from 'react-hot-toast';


const DeleteadvanceModal = ({ onClose, isOpen, advanceId }) => {
  
  const queryClient = useQueryClient()

  const { mutateAsync: deteleAdvanceMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-advance"],
    mutationFn: deleteAdvancesApi
  })

  const handleDeleteAdvance = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleAdvanceMutation({ id: advanceId?.id })
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfAdvances", reportData?.id]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">

        <DialogHeader className={"flex items-center justify-center max-w-[70%] text-center mx-auto"}>
          <DialogTitle className="text-xs font-normal">Are you sure you want to delete {advanceId?.title} ?</DialogTitle>
        </DialogHeader>

        <div className="flex items-center justify-center sm:mt-10 gap-2">
          <DialogClose asChild>
            <Button variant="outline"
              className="h-7 gap-1 text-xs rounded-full p-3"
            >Cancel</Button>
          </DialogClose>

          <Button
            onClick={handleDeleteAdvance}
            className="h-7 gap-1 text-xs rounded-full p-3"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <span>Deleting...</span>
            ) : (
              <>
                <Trash2 size={"16"}/> Delete
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteadvanceModal;
