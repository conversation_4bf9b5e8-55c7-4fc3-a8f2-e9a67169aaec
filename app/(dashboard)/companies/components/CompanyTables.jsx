"use client";

import React, { Suspense, useCallback, useEffect, useMemo, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { EllipsisVertical } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { formatDate } from '@/utils/Utils'
import { Checkbox } from '@/components/ui/checkbox'
import DeleteadvanceModal from './DeleteadvanceModal'
import { useExportCSV } from '@/hooks/useExportCSV'
import TableFilters from '@/components/reusables/TableFilters'
import { choicesApi } from '@/apis/utilapi'
import useSort from '@/hooks/useSort'
import useTableFilter from '@/hooks/useTableFilter'
import FilterWrapper from '@/components/reusables/FilterWrapper'
import { useSearchParams } from 'next/navigation'
import Tabaccross from '@/components/reusables/MyTabs';
import TabWrapper from '@/components/reusables/TabWrapper';
import { CompanyApi, exportCompanyCSVApi } from '@/apis/system-admin/company';

const CompanyContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    title: "",
    has_report: "",
    paid_through: "",
    minAmount: "",
    maxAmount: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices data
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch advances data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfCompany", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max",
            dateRange: "date"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'pending') {
        apiParams.has_report = false; // Only show advances without reports
      }

      return CompanyApi(apiParams);
    },
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true
  });

  // Initialize useSort with memoized initial data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(useMemo(() => apiResponse?.results || [], [apiResponse?.results]));

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Export CSV functionality
  const { handleExport } = useExportCSV(exportCompanyCSVApi, { filename: 'compnay.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Filter options configuration
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'title',
        label: 'Filter by title',
        type: 'text'
      },
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'paid_through',
        placeholder: 'Paid Through',
        options: choicesList?.data?.payment_types || [],
        label: "Paid Through",
      }
    ],
    showAmountFilter: true,
    showDateFilter: true
  }), [choicesList?.data?.payment_types]);

  // Get counts for tabs
  const getCounts = useMemo(() => {
    if (!apiResponse?.results) return { all: 0, pending: 0 };

    return {
      all: apiResponse.count || 0,
      pending: apiResponse.results.filter(advance => !advance.report).length
    };
  }, [apiResponse?.results, apiResponse?.count]);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData?.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "organization",
      header: "Organization",
      cell: ({ row }) => (
        <div className="capitalize">
          {row.original.name}
        </div>
      ),
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "organization_id",
      header: "Organization ID",
      cell: ({ row }) => row.original.organization_id,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "short_name",
      header: "Short Name",
      cell: ({ row }) => row.original.short_name,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "size",
      header: "Size",
      cell: ({ row }) => row.original.size_display || "--",
      sortable: true,
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }) => (
        <div className="capitalize">
          {row.original.country || "--"}
        </div>
      ),
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "industry",
      header: "Industry",
      cell: ({ row }) => row.original.industry_display || "--",
      sortable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true,
    },
    // {
    //   id: "view",
    //   header: "View",
    //   cell: ({ row }) => (
    //     <div onClick={(e) => {e.stopPropagation(); setSelectedRow(row.original); setSheetOpen(true);}}>
    //       <Eye strokeWidth={1.8} size={18} />
    //     </div>
    //   ),
    // },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsEditModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsDeleteModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      sticky: true,
      unhidable: true,
    }
  ], [selectedRows, sortedData, handleSelectAll]);

  return (
    <>
      <div className="overflow-x-scroll w-full">
        <div className="no-scrollbar">
          <TableComponent
            rows={sortedData}
            columns={columns}
            tableTitle={
              <TabWrapper
                tabs={[
                  { value: 'all', label: 'All', count: getCounts.all },
                  { value: 'active', label: 'Active', count: getCounts.pending },
                ]}
                defaultTab="all"
              />
            }
            // onRowClick={(row) => {
            //   setSelectedRow(row);
            //   setSheetOpen(true);
            // }}
            isLoading={isLoading}
            onSort={handleSort}
            sortState={sortState}
            showImportExport={selectedRows.length > 0}
            exportToCSV={handleExportCSV}
            filterComponents={
              <FilterWrapper
                filterValues={filters}
                onFilterApply={handleFilterApply}
                onFilterClear={handleFilterClear}
                filterOptions={filterOptions}
              />
            }
          />
        </div>
      </div>

      {/* <Advancesdetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedRow}
      /> */}

      {isDeleteModalOpen && selectedRow && (
        <DeleteadvanceModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          advanceId={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <CompanyContent />
    </Suspense>
  );
}

export default Page;