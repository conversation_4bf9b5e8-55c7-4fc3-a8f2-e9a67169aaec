import React from 'react'
import {
  Sheet,
  SheetClose,
  SheetContent,
} from "@/components/ui/sheet"
import { CircleArrowLeft } from 'lucide-react'
import { formatDate } from '@/utils/Utils'

const Advancesdetail = ({ sheetOpen, setSheetOpen, selectedRow }) => {
  return (
    <>
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        {selectedRow && (
          <SheetContent side="right" className=".w-1/3 rounded-s-xl flex flex-col gap-4">
            <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-gray-950 text-xs font-medium leading-8'>
                <div className='border-b'>
                  <span>Advances Details</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Date:</span> {selectedRow.date}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Title:</span> {selectedRow.title}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Reference Number:</span> {selectedRow.reference}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Paid Through:</span> {selectedRow.paid_through_display}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Currency:</span> {selectedRow.amount_currency}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Created:</span> {formatDate(selectedRow.created_at)}
                </div>
                <div className='flex justify-between items-center font-semibold text-sm text-gray-950'>
                  <span>Amount:</span> {selectedRow.amount_display}
                </div>
              </div>
            </div>

            <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-table_gray_text text-xs font-medium leading-8'>
                <div className='border-b'>
                  <span className='font-semibold text-sm'>Description</span>
                </div>

                <div className='flex justify-between items-center'>
                  <span>{selectedRow.notes}</span>
                </div>
              </div>
            </div>
          </SheetContent>
        )}
      </Sheet>
    </>
  )
}

export default Advancesdetail
