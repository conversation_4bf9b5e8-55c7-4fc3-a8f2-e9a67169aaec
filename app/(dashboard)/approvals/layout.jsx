"use client"

import <PERSON><PERSON><PERSON><PERSON> from "@/components/reusables/MyTabs";
import Createreport from "../reports/components/createreport";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import { usePathname } from "next/navigation";

export default function ApprovalLayout({ children }) {

  const [isNewReportModalOpen, setisNewReportModalOpen] = useState(false);

  const pathname = usePathname();

  // Do not render this layout if on a nested `[id]` route
  if (pathname.startsWith("/approvals/") && pathname !== "/approvals" && pathname !== "/approvals/pending-approvals" && pathname !== "/approvals/approved" && pathname !== "/approvals/rejected-approvals") {
    return <>{children}</>;
    
  }

  return (
    <>

      <div className="overflow-x-hidden grid auto-rows-max">
        <div className="p-4 border-b">
          <div className="flex flex-row items-center justify-between w-full gap-4">

            <div>
              <h3 className="text-sm font-semibold">Approvals</h3>
            </div>

            {/* <div className="flex items-center gap-3">

              <div>
                <Button
                  size="xs"
                  className={`gap-1 rounded-full p-3`}
                  onClick={() => setisNewReportModalOpen(true)}
                >
                  <Plus />
                  <span>New Report</span>
                </Button>
              </div>

            </div> */}

          </div>
        </div>

        {/* {isNewReportModalOpen && (
          <Createreport
            isOpen={isNewReportModalOpen}
            onClose={() => setisNewReportModalOpen(false)} />
        )} */}

        <main className="overflow-x-scroll">{children}</main>
      </div>
    </>
  )
}