"use client"

import { exportApproval<PERSON><PERSON><PERSON>, listOfApprovals } from '@/apis/approvals';
import { TableComponent } from '@/components/reusables/table';
import { Checkbox } from '@/components/ui/checkbox';
import { formatDate } from '@/utils/Utils';
import { useQuery } from '@tanstack/react-query';
import { EllipsisVertical } from 'lucide-react';
import React, { useEffect, useState, useMemo, useCallback, Suspense } from 'react';
import Approvalsdetail from './components/Approvalsdetail';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { StatusBadge } from '@/utils/status';
import { Role } from '@/utils/roles/Role';
import useSort from '@/hooks/useSort';
import useTableFilter from '@/hooks/useTableFilter';
import FilterWrapper from '@/components/reusables/FilterWrapper';
import { choicesApi } from '@/apis/utilapi';
import { useExportCSV } from '@/hooks/useExportCSV';
import TabWrapper from '@/components/reusables/TabWrapper';

const ApprovalsContent = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const { isAdministrator } = Role();

  // State management
  const [selectedRows, setSelectedRows] = useState([]);
  const [sheetOpen, setSheetOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    status: "",
    name: "",
    report_types: "",
    dateRange: null,
    submittedBy: ""
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices for dropdown options
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch approvals data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfApprovals", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          apiParams[key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab !== 'all') {
        apiParams.status = currentTab;
      }

      return listOfApprovals(apiParams);
    }
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    if (currentTab === 'all') return apiResponse.results;

    return apiResponse.results.filter(approval => approval.status === currentTab);
  }, [apiResponse?.results, currentTab]);

  // Sort functionality
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  // Handle row selection
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Handle CSV export
  const { handleExport } = useExportCSV(exportApprovalCSVApi, { filename: 'approvals.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  // Get counts for tabs
  const getCounts = useMemo(() => {
    if (!apiResponse?.results) return { all: 0, approved: 0, pending: 0, rejected: 0 };

    return {
      all: apiResponse.results.length,
      approved: apiResponse.results.filter(item => item.status === 'approved').length,
      pending: apiResponse.results.filter(item => item.status === 'pending').length,
      rejected: apiResponse.results.filter(item => item.status === 'rejected').length
    };
  }, [apiResponse?.results]);

  // Filter options configuration
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
      {
        key: 'name',
        label: 'Filter by report name',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'type',
        placeholder: 'Report Type',
        options: choicesList?.data?.report_types || [],
        label: "Report Type",
      },
      {
        key: 'status',
        placeholder: 'Status',
        options: choicesList?.data?.approval_status || [],
        label: "Status"
      }
    ],
    showAmountFilter: true,
  }), [choicesList?.data]);

  // Table columns configuration
  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      unhidable: true,
    },
    {
      accessorKey: "date",
      header: "Report Date",
      cell: ({ row }) => formatDate(row.original.date),
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "name",
      header: "Report Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "type",
      header: "Report Type",
      cell: ({ row }) => row.original.report_type_display,
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "reference",
      header: "Reference Number",
      cell: ({ row }) => row.original.reference,
      sortable: true,
      unhidable: true
    },
    ...(isAdministrator ? [{
      accessorKey: "approver.submits_to.name",
      header: "Submitted To",
      cell: ({ row }) => row.original?.approver?.submits_to?.name,
      sortable: true,
      unhidable: true
    }] : []),
    {
      accessorKey: "employee.name",
      header: "Submitted By",
      cell: ({ row }) => row.original?.employee?.name,
      sortable: true
    },
    {
      accessorKey: "submitted_at",
      header: "Submitted At",
      cell: ({ row }) => formatDate(row.original.submitted_at),
      sortable: true
    },
    {
      accessorKey: "created_at",
      header: "Created At",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => <StatusBadge status={row.original.status_display} />,
      sortable: true,
      unhidable: true
    },
    {
      header: "Actions",
      id: "actions",
      id: "actions",
      sticky: true,
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button aria-haspopup="true" size="icon" variant="ghost">
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setSelectedReport(row.original);
              setSheetOpen(true);
            }}>View Details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      unhidable: true,
    }
  ], [isAdministrator, selectedRows, sortedData, handleSelectAll]);

  return (
    <div className='overflow-x-scroll no-scrollbar w-full'>
      <TableComponent
        columns={columns}
        rows={sortedData}
        tableTitle={
          <TabWrapper
            tabs={[
              { value: 'all', label: 'All', count: getCounts.all },
              { value: 'approved', label: 'Approved', count: getCounts.approved },
              { value: 'pending', label: 'Pending', count: getCounts.pending },
              { value: 'rejected', label: 'Rejected', count: getCounts.rejected },
            ]}
            defaultTab="all"
          />
        }
        onRowClick={(row) => router.push(`/approvals/${row.id}`)}
        onSort={handleSort}
        sortState={sortState}
        exportToCSV={handleExportCSV}
        NoavailableTitle={isLoading ? "Loading..." : apiResponse?.count <= 0 ? "Approvals" : ""}
        tableDescription={
          "Review and manage pending approvals for expenses, advances, and trips. Take action on requests and maintain approval history."
        }
        filterComponents={
          <FilterWrapper
            filterValues={filters}
            onFilterApply={handleFilterApply}
            onFilterClear={handleFilterClear}
            filterOptions={filterOptions}
          />
        }
        showImportExport={selectedRows.length > 0}
        isLoading={isLoading}
      />

      {sheetOpen && selectedReport && (
        <Approvalsdetail
          sheetOpen={sheetOpen}
          setSheetOpen={setSheetOpen}
          selectedRow={selectedReport}
        />
      )}
    </div>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ApprovalsContent />
    </Suspense>
  );
}

export default Page;