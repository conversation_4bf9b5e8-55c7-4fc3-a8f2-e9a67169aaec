import React from 'react'
import {
  Sheet,
  SheetContent,
} from "@/components/ui/sheet"
import { formatDate } from '@/utils/Utils'

const Approvalsdetail = ({ sheetOpen, setSheetOpen, selectedRow }) => {
  return (
    <>
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        {selectedRow && (
          <SheetContent side="right" className=".w-1/3 rounded-s-xl flex flex-col gap-4">
            <div id="detail" className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-gray-950 text-xs font-medium leading-8'>
                <div className='border-b'>
                  <span className='font-semibold text-sm'>Approval Details</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Report Name:</span> {selectedRow.name}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Reference Number:</span> {selectedRow.reference}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Submitted By:</span> {selectedRow.approver?.name}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Status:</span> {selectedRow.status_display}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Submitted To:</span> {selectedRow.approver?.submits_to?.name}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Report Date:</span> {formatDate(selectedRow.date)}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Submitted At:</span> {formatDate(selectedRow.submitted_at)}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Created:</span> {formatDate(selectedRow.created_at)}
                </div>
                {/* <div className='flex justify-between items-center font-semibold text-sm text-gray-950'>
                  <span>Amount:</span> {selectedRow.amount_display}
                </div> */}
              </div>
            </div>

            <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
              <div className='text-table_gray_text text-xs font-medium leading-8'>
                <div className='border-b'>
                  <span className='font-semibold text-sm'>Description</span>
                </div>

                <div className='flex justify-between items-center'>
                  <span>{selectedRow.purpose}</span>
                </div>
              </div>
            </div>
          </SheetContent>
        )}
      </Sheet>
    </>
  )
}

export default Approvalsdetail
