import { But<PERSON> } from "@/components/ui/button"
import { Bell, GitPullRequestDraft } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

export const Notification = () => {
  return (
    <div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>

          <Button
            variant="outline"
            size="icon"
            className="ml-auto h-8 w-8 rounded-xl"
          >
            <Bell className="h-4 w-4" />
          </Button>

          {/* </Button> */}
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-60 h-80">
          <DropdownMenuLabel className="flex items-center justify-between">
            <span className="text-xs">Notifications</span>
            <span className="cursor-pointer"><GitPullRequestDraft className="rotate-90" size={"12px"}/></span>
          </DropdownMenuLabel>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}