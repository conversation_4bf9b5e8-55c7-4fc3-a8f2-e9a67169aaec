"use client";

import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { getOnboardingApi, OnboardingApi } from '@/apis/auth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { currencyList, onboardingChoicesApi } from '@/apis/utilapi';
import toast from 'react-hot-toast';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { SearchableSelect } from '@/components/reusables/SearchableSelect';

export function OnboardingModal({ isOpen, onClose }) {
  const queryClient = useQueryClient();

  const [payload, setPayload] = useState({
    organization_size: "",
    organization_country: "",
    organization_industry: "",
    organization_default_currency: ""
  });

  const { data: onboardingData, isPending: isLoadingOnboarding } = useQuery({
    queryKey: ["getOnboarding"],
    queryFn: getOnboardingApi
  });

  useEffect(() => {
    if (onboardingData?.data) {
      setPayload({
        organization_size: onboardingData.data.organization_size || "",
        organization_country: onboardingData.data.organization_country || "",
        organization_industry: onboardingData.data.organization_industry || "",
        organization_default_currency: onboardingData.data.organization_default_currency || ""
      });
    }
  }, [onboardingData]);

  const { data: organizationResponse, isPending } = useQuery({
    queryKey: ["choice"],
    queryFn: onboardingChoicesApi
  });

  const { data: currencyResponse, isLoading: isLoadingCurrencies } = useQuery({
    queryKey: ["currency-choice"],
    queryFn: currencyList
  });

  const { mutateAsync, isPending: isLoadingOnboardingSubmit } = useMutation({
    mutationKey: ["onboarding"],
    mutationFn: OnboardingApi,
    onSuccess: (response) => {
      toast.success(response?.message);
      queryClient.invalidateQueries("profile-settings");
      onClose();
    },
    onError: (error) => {
      console.log(error);
      toast.error(error?.message);
    }
  });

  const handleChange = (key, value) => {
    setPayload({ ...payload, [key]: value });
  };

  const handleSubmitOrganizationDetail = async (e) => {
    e.preventDefault();
    try {
      if (!payload.organization_country || !payload.organization_industry || !payload.organization_size || !payload.organization_default_currency) {
        toast.error("Please fill in all required fields");
        return;
      }

      await mutateAsync(payload);
    } catch (error) {
      console.log(error);
      toast.error(error?.message);
    }
  };

  const isSubmitDisabled = !payload?.organization_country ||
    !payload?.organization_industry ||
    !payload?.organization_size ||
    isLoadingOnboarding;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] p-6">
        {isLoadingOnboarding ? (
          <ButtonLoader />
        ) : (
          <div className="flex flex-col">
            <h2 className="text-xl font-semibold mb-4">Complete Your Organization Details</h2>
            <p className="text-sm text-gray-600 mb-6">
              Please provide additional information about your organization to complete the onboarding process.
            </p>

            <form onSubmit={handleSubmitOrganizationDetail}>

              <div className="p-6 pt-0">
                <div className="space-y-4">

                  <div>
                    <label className="text-xs font-medium mb-1 block">Company Size</label>
                    <div>

                      <SearchableSelect
                        value={payload?.organization_size}
                        onValueChange={(value) => handleChange("organization_size", value)}
                        placeholder="Company Size"
                        items={organizationResponse?.data?.organization_sizes}
                        isLoading={isPending}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium mb-1 block">Industry</label>

                    <div>

                      <SearchableSelect
                        value={payload?.organization_industry}
                        onValueChange={(value) => handleChange("organization_industry", value)}
                        placeholder="Industry"
                        items={organizationResponse?.data?.industries}
                        isLoading={isPending}

                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium mb-1 block">Country</label>

                    <div>

                      <SearchableSelect
                        value={payload?.organization_country}
                        onValueChange={(value) => handleChange("organization_country", value)}
                        placeholder="Country"
                        items={organizationResponse?.data?.countries}
                        isLoading={isPending}

                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-xs font-medium mb-1 block">Default Currency</label>
                    <div>
                      <SearchableSelect
                        value={payload?.organization_default_currency}
                        onValueChange={(value) => handleChange("organization_default_currency", value)}
                        items={currencyResponse?.data}
                        isLoading={isLoadingCurrencies}
                      />
                    </div>
                  </div>


                  <div className='flex items-center justify-end pt-4'>
                    <Button disabled={isSubmitDisabled} size="xs">
                      {isLoadingOnboardingSubmit ? (
                        <span><ButtonLoader color={"FFFFFF"} /></span>
                      ) : (
                        <span>Submit</span>
                      )}
                    </Button>
                  </div>

                </div>

              </div>

            </form>

          </div>
        )}

      </DialogContent>
    </Dialog>
  );
}