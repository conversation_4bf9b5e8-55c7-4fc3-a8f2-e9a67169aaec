import React from 'react'
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

const DashboardCards = ({apiResponse, apiResponseforAdvances}) => {
  return (
    <div className='grid grid-cols-2 my-4 gap-4'>
      <Card x-chunk="dashboard-05-chunk-1">
        <CardHeader className="pb-2">
          <CardDescription>Total number of Expenses</CardDescription>
          <CardTitle className="text-base md:text-2xl">{apiResponse?.count}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs text-muted-foreground">
            +25% Lifetime
          </div>
        </CardContent>
      </Card>

      <Card x-chunk="dashboard-05-chunk-1">
        <CardHeader className="pb-2">
          <CardDescription>Total number of Advances</CardDescription>
          <CardTitle className="text-base md:text-2xl">{apiResponseforAdvances?.count}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-xs text-muted-foreground">
            3 unpaid document (s)
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default DashboardCards;