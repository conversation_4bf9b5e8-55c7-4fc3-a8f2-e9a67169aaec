"use client";

import { ChartContainer } from "@/components/ui/chart";
import Pageloader from "@/utils/spinner/Pageloader";
import {
  Bar,
  Bar<PERSON>hart,
  Line,
  LineChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  AreaChart,
  Area,
} from "recharts";

export function DashboardChart({ type = "bar", data = [], isLoading = false, chartType }) {
  if (isLoading) {
    return <div className="flex items-center justify-center h-full text-xs">
      <Pageloader />
    </div>;
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-2 border rounded-lg shadow-lg">
          <p className="text-[10px] font-medium">{`Date: ${label}`}</p>
          {chartType === "reportedUnreported" ? (
            <>
              <p className="text-[10px] text-primary">{`Reported: ${payload[0]?.value}`}</p>
              <p className="text-[10px] text-muted">{`Unreported: ${payload[1]?.value}`}</p>
            </>
          ) : (
            <p className="text-[10px] text-primary">{`Total Reports: ${payload[0]?.value}`}</p>
          )}
        </div>
      );
    }
    return null;
  };

  console.log("data", data)

  return (
    <ResponsiveContainer width="100%" height="100%">
      {type === "bar" ? (
        <BarChart data={data} margin={{}}>
          <XAxis
            dataKey="date"
            stroke="#888888"
            fontSize={10}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            stroke="#888888"
            fontSize={10}
            tickLine={false}
            axisLine={false}
          />
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <Bar
            dataKey="total_reports"
            fill="currentColor"
            radius={[8, 8, 0, 0]}
            className="fill-primary"
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
          />
        </BarChart>
      ) : (
        <AreaChart data={data} margin={{}}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="date"
            stroke="#888888"
            fontSize={10}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            stroke="#888888"
            fontSize={10}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ fill: 'rgba(0, 0, 0, 0.1)' }}
          />
          <Area
            type="monotone"
            dataKey="reported_expenses"
            stroke="currentColor"
            fill="currentColor"
            fillOpacity={0.4}
            className="fill-primary stroke-primary"
            stackId="1"
          />
          <Area
            type="monotone"
            dataKey="unreported_expenses"
            stroke="currentColor"
            fill="currentColor"
            fillOpacity={0.4}
            className="fill-muted stroke-muted"
            stackId="1"
          />
        </AreaChart>
      )}
    </ResponsiveContainer>
  );
}
