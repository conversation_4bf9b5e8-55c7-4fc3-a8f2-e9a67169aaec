"use client"

import React, { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { listOfReports } from '@/apis/expense-report'
import { MessageCircle, EllipsisVertical } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import Pageloader from '@/utils/spinner/Pageloader'
import { formatDate, formatExpenseDate } from '@/utils/Utils'
import { useRouter } from 'next/navigation'
import { Checkbox } from '@/components/ui/checkbox'
import { TooltipMessage } from '@/components/reusables/tooltip-message'
import { StatusBadge } from '@/utils/status'

const Recentreport = () => {
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfReports"],
    queryFn: listOfReports,
  });

  const columns = [
    {
      title: (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      ),
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
          />
        </div>
      ),
    },
    {
      title: "Report Name",
      accessor: (row) => row.name,
    },
    {
      title: "Report Type",
      accessor: (row) => row.report_type_display,
    },
    {
      title: "Reference Number",
      accessor: (row) => row.reference,
    },
    {
      title: "Status",
      accessor: (row) => (
        <StatusBadge status={row.status_display} />
      )
    },
    {
      title: "Total Amount",
      accessor: (row) => row.total_expenses_display,
    },
    {
      title: "Reimbursable Amount",
      accessor: (row) => row.reimbursable_amount_display,
    },
    {
      title: "Date",
      accessor: (row) => (formatExpenseDate(row.date)),
    },
    {
      title: "Created",
      accessor: (row) => (formatDate(row.created_at)),
    },
    {
      accessor: (row) => (
        <TooltipMessage message={row?.purpose} />
      ),
    },
    {
      accessor: (row) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem>Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
      sticky: true,
    },
  ];

  // Map the API response to rows
  const rows = apiResponse ? apiResponse.results : [];

  const router = useRouter()

  return (
    <div className='overflow-x-scroll no-scrollbar w-full'>
        <div className='no-scrollbar'>
          <TableComponent
            rows={rows}
            columns={columns}
            tableTitle={`Recent Reports (${rows?.length || 0})`}
            onRowClick={(row) => router.push(`/reports/${row?.id}`)}
            NoavailableTitle="Recent Report"
          />
        </div>
    </div>
  );
};

export default Recentreport;