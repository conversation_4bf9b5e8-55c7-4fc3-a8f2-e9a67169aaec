"use client"
import React, { useEffect, useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, TrendingUp, CalendarIcon } from 'lucide-react'
import { DashboardChart } from './components/chart/chart'
import AllreportsTable from '../reports/components/Allreports/AllreportsTable';
import { useQuery } from '@tanstack/react-query'
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { chartApi } from '@/apis/graph'
import Createreport from '../reports/components/createreport'


const Dashboard = () => {

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const [timeRange, setTimeRange] = useState("this-year");

  const { data: chartResponse, isLoading } = useQuery({
    queryKey: ["chartData", timeRange],
    queryFn: () => chartApi({ range: timeRange }),
    keepPreviousData: true, // Retains previous data while fetching new data
  });

  // Extract and transform the data for the charts
  const totalReportsData = chartResponse?.data?.charts?.total_reports_data?.map((item) => ({
    date: item?.date,
    total_reports: item?.total_reports || 0
  })) || [];

  const reportedUnreportedData = chartResponse?.data?.charts?.reported_unreported_expenses_data?.map((item) => ({
    date: item?.date,
    reported_expenses: item?.reported_expenses || 0,
    unreported_expenses: item?.unreported_expenses || 0
  })) || [];

  const totalExpenses = chartResponse?.data?.expenses?.total_amount_display || 0;

  console.log("bar", totalReportsData)

  return (
    <>
      <div className="overflow-x-hidden grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2">
        <div className="flex flex-col gap-6">
          <div className="flex items-center justify-between border-b py-3 px-4">
            <h3 className='font-semibold text-base'>Report Overview</h3>
            <div className='flex gap-2 items-center '>
              <div>

                <Select value={timeRange} onValueChange={setTimeRange}>

                  <SelectTrigger className="w-[140px] text-xs rounded-[10px] h-[36px] leading-[1.5em]" aria-label="Select a value">
                    <CalendarIcon size={12} />
                    <SelectValue placeholder="Select a range" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl text-xs">
                    <SelectItem value="this-year" className="rounded-lg text-xs">
                      This Year
                    </SelectItem>
                    <SelectItem value="this-quarter" className="rounded-lg text-xs">
                      This Quarter
                    </SelectItem>
                    <SelectItem value="last-30-days" className="rounded-lg text-xs">
                      Last 30 Days
                    </SelectItem>
                    <SelectItem value="last-7-days" className="rounded-lg text-xs">
                      Last 7 Days
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={() => setIsEditModalOpen(true)} size="xs">
                <Plus />
                New Report
              </Button>
            </div>

          </div>

          {/* Stats Cards */}
          <div className="p-4 grid grid-cols-1 md:grid-cols-4 gap-3">
            <Card>
              <CardHeader className="p-3">
                <CardDescription>Total of Expenses</CardDescription>
                <CardTitle className="text-xl font-bold">{totalExpenses.toLocaleString()}</CardTitle>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader className="p-3">
                <CardDescription>Report Submitted</CardDescription>
                <CardTitle className="text-xl font-bold">{chartResponse?.data?.reports?.submitted || 0}</CardTitle>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader className="p-3">
                <CardDescription>Pending Approvals</CardDescription>
                <CardTitle className="text-xl font-bold">{chartResponse?.data?.reports?.pending_approval || 0}</CardTitle>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader className="p-3">
                <CardDescription>Unsubmitted Expenses</CardDescription>
                <CardTitle className="text-xl font-bold">{chartResponse?.data?.expenses?.unreported || 0}</CardTitle>
              </CardHeader>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="p-4 grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card className="space-0 p-0 md:col-span-3">
              <CardHeader className="py-2 px-2">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-sm">Reports Status</CardTitle>
                    <CardTitle className="text-xs flex items-center">
                      Total Reports Per Day <span className='ml-2'><TrendingUp size={16} /></span>
                    </CardTitle>
                    <CardDescription className="text-xs font-medium">
                      Number of reports submitted per day
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-0 p-0">
                <div className="h-[300px]">
                  <DashboardChart
                    type="bar"
                    data={totalReportsData}
                    isLoading={isLoading}
                    chartType="totalReports"
                  />
                </div>
              </CardContent>
            </Card>

            <Card className="space-0 p-0 md:col-span-2">
              <CardHeader className="py-2 px-2">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-sm">Unreported Status</CardTitle>
                    <CardTitle className="text-xs flex items-center">
                      Reported vs Unreported <span className='ml-2'><TrendingUp size={16} /></span>
                    </CardTitle>
                    <CardDescription className="text-xs font-medium">
                      Comparison of reported and unreported expenses
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-0 p-0">
                <div className="h-[300px]">
                  <DashboardChart
                    type="area"
                    data={reportedUnreportedData}
                    isLoading={isLoading}
                    chartType="reportedUnreported"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* <Recentreport /> */}
      <div className="w-full overflow-x-scroll">
        <AllreportsTable />
      </div>
      </div>

      {isEditModalOpen && (
        <Createreport
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
        />
      )}

    </>
  )
}

export default Dashboard
