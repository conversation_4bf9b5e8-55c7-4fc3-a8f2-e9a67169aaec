"use client";

import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getListOfNotification, notificationMarkAllAsReadApi, notificationMarkAsReadApi } from "@/apis/utilapi";
import { formatDistanceToNow } from "date-fns";
import { Search, ArrowUpDown, Bell, AlertTriangle, CheckCircle, Info, EllipsisVertical, Plus, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import Pageloader from "@/utils/spinner/Pageloader";
import { getProfile<PERSON><PERSON>, updateNotificationPre<PERSON><PERSON><PERSON> } from "@/apis/profile-management";
import toast from "react-hot-toast";
import ButtonLoader from "@/utils/spinner/ButtonLoader";

const NotificationsPage = () => {
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState("all");

  // Fetch notifications and profile data
  const { data: notificationsData, isLoading } = useQuery({
    queryKey: ["notifications"],
    queryFn: getListOfNotification,
  });

  const { data: profileData, isPending: isLoadingProfile } = useQuery({
    queryKey: ["profile"],
    queryFn: getProfileApi,
  });

  // Mutation for updating notification preferences
  const { mutateAsync: updatePreferences, isPending: isUpdating } = useMutation({
    mutationFn: updateNotificationPreferenceApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Notification preferences updated");
      queryClient.invalidateQueries(["profile"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to update preferences");
    },
  });

  // Add mutation for mark all as read
  const { mutateAsync: markAllAsRead, isPending: isMarkingAllRead } = useMutation({
    mutationFn: notificationMarkAllAsReadApi,
    onSuccess: (data) => {
      toast.success(data?.message || "All notifications marked as read");
      queryClient.invalidateQueries(["notifications"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to mark notifications as read");
    },
  });

  // Add mutation for mark as read
  const { mutateAsync: markAsRead } = useMutation({
    mutationFn: notificationMarkAsReadApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Notification mark as read")
      queryClient.invalidateQueries(["notifications"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to mark notification as read");
    },
  });

  // Get notification preferences from profile data
  const notificationPreferences = profileData?.data?.profile?.notification_preferences || {};

  // Create dynamic preferences mapping from API data
  const getPreferencesMapping = () => {
    const preferences = notificationPreferences || {};
    return Object.keys(preferences).map(key => {
      // Convert snake_case to readable text
      const label = key
        .replace('notify_', '')
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');

      return {
        id: key,
        label,
        tooltip: `Get notified ${key.includes('when') ? 'when' : 'about'} ${label.toLowerCase()}`
      };
    });
  };

  // Helper function to get icon based on notification level
  const getNotificationIcon = (level) => {
    switch (level) {
      case "error":
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "info":
      default:
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  // Helper function to get background color based on notification level
  const getNotificationBg = (level) => {
    switch (level) {
      case "error":
        return "bg-red-50";
      case "success":
        return "bg-green-50";
      case "info":
      default:
        return "bg-blue-50";
    }
  };

  const handlePreferenceChange = async (key, value) => {
    try {
      const payload = {
        [key]: value
      };
      await updatePreferences(payload);
    } catch (error) {
      console.error(error);
    }
  };

  // Add handler for mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error(error);
    }
  };

  // Handler function for marking a single notification as read
  const handleNotificationClick = async (notification) => {
    if (notification.unread) {
      try {
        await markAsRead(notification.id);
      } catch (error) {
        console.error(error);
      }
    }
  };

  return (
    <div className="max-w-5xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Notifications</h1>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="hover:bg-gray-100 p-2 rounded-md cursor-pointer">
              <EllipsisVertical size={16} />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-[350px] p-6" align="end">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-sm font-semibold">Notification Preferences</h2>
              <Button variant="outline" size="sm">
                <span>Save</span>
              </Button>
            </div>

            <div className="space-y-6">
              {isLoadingProfile ? (
                <div className="text-center py-4">
                  <Pageloader />
                </div>
              ) : (
                getPreferencesMapping().map((item) => (
                  <div key={item.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium">{item.label}</span>
                      <HelpCircle className="h-3 w-3 text-gray-400" />
                    </div>
                    <Switch
                      size="md"
                      className="text-xs"
                      checked={notificationPreferences[item.id] || false}
                      onCheckedChange={(checked) => {
                        handlePreferenceChange(item.id, checked);
                      }}
                      disabled={isUpdating}
                    />
                  </div>
                ))
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Search, filter and sort */}

      <div className="flex items-center justify-between mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
          <Input
            type="text"
            placeholder="Search notifications..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex items-center gap-4">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[100px] text-xs">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent className="text-xs">
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="unread">Expense</SelectItem>
              <SelectItem value="read">Approval</SelectItem>
              <SelectItem value="read">System</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" className="flex items-center gap-2">
            <ArrowUpDown className="h-4 w-4" />
            <span className="text-xs">Sort</span>
          </Button>

          <Button 
            variant="default" 
            size="xs"
            onClick={handleMarkAllAsRead}
            disabled={isMarkingAllRead || !notificationsData?.results?.some(n => n.unread)}
            className="flex items-center gap-2"
          >
            {isMarkingAllRead ? (
              <ButtonLoader color="#FFFFFF" />
            ) : (
              <>
                <Plus className="h-4 w-4" />
                <span className="text-xs">Mark all as read</span>
              </>
            )}
          </Button>
        </div>

      </div>

      {/* Notifications List */}
      <div className=".space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <Pageloader />
          </div>
        ) : notificationsData?.results.map((notification) => (
          <div
            key={notification.id}
            onClick={() => handleNotificationClick(notification)}
            className={`flex items-start gap-4 p-4 border cursor-pointer hover:bg-gray-50 transition-colors
              ${notification.unread ? 'bg-gray-50' : 'bg-white'}`}
          >
            {notification.unread && (
              <div className="h-2 w-2 bg-blue-500 rounded-full" />
            )}
            <div className={`w-10 h-10 rounded-full ${getNotificationBg(notification.level)} flex items-center justify-center`}>
              {getNotificationIcon(notification.level)}
            </div>

            <div className="flex-1">
              <h3 className="font-medium text-sm mb-1">{notification.verb}</h3>
              <p className="text-gray-600 text-xs">{notification.description}</p>
              <div className="flex items-center gap-3 mt-3">
                {notification.level === "error" && (
                  <Button
                    variant="default"
                    size="xs"
                  >
                    View Details
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="xs"
                >
                  Dismiss
                </Button>
              </div>
            </div>

            <span className="text-xs text-gray-500">
              {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
            </span>
          </div>
        ))}

        {notificationsData?.results.length === 0 && (
          <div className="text-center py-8 text-gray-500 text-xs">
            No notifications found
          </div>
        )}
      </div>

      {/* Pagination if needed */}
      {notificationsData?.count > notificationsData?.page_size && (
        <div className="mt-6 flex justify-center">
          {/* Add pagination component here */}
        </div>
      )}
    </div>
  );
};

export default NotificationsPage;
