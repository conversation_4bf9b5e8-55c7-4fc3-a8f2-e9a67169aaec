"use client"

import { But<PERSON> } from "@/components/ui/button";
import { SquarePen } from "lucide-react";
import Createreport from "./components/createreport";
import { useState } from "react";
import { usePathname } from "next/navigation";

export default function ReportLayout({ children }) {

  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  const pathname = usePathname();

  // Do not render this layout if on a nested `[id]` route
  if (pathname.startsWith("/reports/") && pathname !== "/reports" && pathname !== "/reports/pendingreports" && pathname !== "/reports/rejectedreports" && pathname !== "/reports/approvedreports" && pathname !== "/reports/draftreports") {
    return <>{children}</>;
  }

  return (
    <>

      <div className="overflow-x-hidden grid auto-rows-max">
        <div className=".mb-4 p-4 lg:p-4 border-b">
          <div className="flex flex-row items-center justify-between w-full gap-4">

            <div>
              <h3 className="text-sm font-semibold">Reports</h3>
            </div>

            <div className="flex items-center gap-3">

              <div>
                <Button
                  size="xs"
                  // className={`gap-1 rounded-full p-3`}
                  onClick={openModal}
                >
                  <SquarePen />
                  <span>Create Report</span>
                </Button>
              </div>

            </div>
          </div>

        </div>

        <Createreport
          isOpen={isModalOpen}
          onClose={closeModal} />

        <main className="overflow-x-hidden">{children}</main>
      </div>
    </>
  )
}