import React, { useState } from "react";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { createReportApi, updateReportApi } from "@/apis/expense-report";
import { choicesApi } from "@/apis/utilapi";
import { listOfPolicies } from "@/apis/expense-policies";
import { useRouter } from "next/navigation";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { Label } from "@/components/ui/label";

const Createreport = ({ onClose, isOpen, reportData = null, mode = "create" }) => {
  const [date, setDate] = useState(reportData?.date ? new Date(reportData.date) : null);
  const [open, setOpen] = useState(false)

  const navigate = useRouter();

  const queryClient = useQueryClient()
  const [backendErrors, setBackendErrors] = useState({});



  const [formData, setFormData] = useState({
    name: reportData?.name || "",
    report_type: reportData?.report_type || "",
    purpose: reportData?.purpose || "",
    // date: reportData?.date || "",
    date: reportData?.date ? format(new Date(reportData.date), "yyyy-MM-dd") : "",
    policy: reportData?.policy?.id || "",
  });

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { data: reportTypeQuery, isPending: isFetchingReportType } = useQuery({ queryKey: ["choice"], queryFn: choicesApi });
  const { data: policyQuery, isPending: isFetchingPolicy } = useQuery({ queryKey: ["policy"], queryFn: listOfPolicies });

  // Create mutation
  const { mutateAsync: createReportMutation, isPending: isCreating } = useMutation({
    mutationFn: createReportApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Report created successfully");
      queryClient.invalidateQueries(["retrieveReport", reportData?.id]);
      onClose()
      navigate.push(`/reports/${data?.data?.id}`);
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      // toast.error(error?.message || "Failed to create report");
    },
  });

  // Update mutation
  const { mutateAsync: updateReportMutation, isPending: isUpdating } = useMutation({
    mutationFn: updateReportApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Report updated successfully");
      onClose();
      queryClient.invalidateQueries(["retrieveReport", reportData?.id]);
      // navigate.push(`/reports/${reportData?.id}`);
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      // toast.error(error?.message || "Failed to update report");
    },
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setBackendErrors({}); // Clear any existing errors

    try {
      const formattedDate = date ? format(date, "yyyy-MM-dd") : null;
      const payload = { ...formData, date: formattedDate };
      // console.log("load", payload)

      if (mode === "create") {
        await createReportMutation(payload); // Call create API
      } else {
        await updateReportMutation({ id: reportData?.id, ...payload }); // Call update API
      }

      // onClose(); // Close the modal
    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} report:`, error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[40rem] overflow-hidden rounded-md">
        <DialogHeader>
          <DialogTitle>{mode === "create" ? "Create Report" : "Edit Report"}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="sm:py-6">

          <div className="space-y-4 pt-4">
            <div>
              <FloatingLabelInput
                id="report_name"
                className={`${backendErrors.name && "validate_input"}`}
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                label="Report Name"
              />
              <ErrorMessage errors={backendErrors} field="name" />
            </div>

            <div>

              <Popover open={open} onOpenChange={setOpen}>

                <PopoverTrigger asChild className={`${backendErrors.date && "validate_input"}`}>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-between text-left",
                      !date && "text-muted-foreground"
                    )}
                  >
                    {date ? format(date, "PPP") : "Select Date"}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent align="start" side="bottom">
                  <Calendar
                    mode="single"
                    selected={date}
                    defaultMonth={date}
                    onSelect={(selectedDate) => {
                      if (selectedDate) {
                        setDate(selectedDate);
                        handleChange("date", format(selectedDate, "yyyy-MM-dd"));
                      }
                      setOpen(false)

                    }}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <ErrorMessage errors={backendErrors} field="date" />
            </div>

            <div className="relative">
              <Label htmlFor="report_type" className="text-xs">
                Report Type
              </Label>
              <div>
                <Select
                  value={formData.report_type}
                  onValueChange={(value) => handleChange("report_type", value)}
                >
                  <SelectTrigger className={`text-xs ${backendErrors.report_type && "validate_input"}`}>
                    <SelectValue placeholder="Report Type" />
                  </SelectTrigger>
                  <SelectContent className="text-xs">
                    {reportTypeQuery?.data?.report_types?.map((item) => (
                      <SelectItem className="text-xs" key={item.value} value={item.value}>
                        {item.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage errors={backendErrors} field="report_type" />
              </div>
            </div>


            <div className="relative">
              <Label htmlFor="policy" className="text-xs">
                Select Policy
              </Label>
              <div>
                <Select
                  value={String(formData.policy)}
                  onValueChange={(value) => handleChange("policy", value)}
                  className="text-xs"
                >
                  <SelectTrigger className={`text-xs ${backendErrors.policy && "validate_input"}`}>
                    <SelectValue placeholder="Select Policy" />
                  </SelectTrigger>
                  <SelectContent>
                    {policyQuery?.results?.map((content) => (
                      <SelectItem className="text-xs" key={String(content.id)} value={String(content.id)}>
                        {content.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage errors={backendErrors} field="policy" />
              </div>
            </div>


            <div>

              <Textarea
                className={`${backendErrors.purpose && "validate_input"}`}
                value={formData.purpose}
                onChange={(e) => handleChange("purpose", e.target.value)}
                placeholder="Business Purpose"
              />
              <ErrorMessage errors={backendErrors} field="purpose" />
            </div>

          </div>

          <div className="flex items-center mt-4 justify-end gap-2">
            <DialogClose asChild>
              <Button size="xs" variant="outline"
              // className="h-7 gap-1 text-xs rounded-full p-3"
              >Cancel</Button>
            </DialogClose>
            <Button
              size="xs"
              type="submit"
              // className="h-7 gap-1 text-xs rounded-full p-3"
              disabled={isCreating || isUpdating || isFetchingReportType || isFetchingPolicy}
            >
              {isCreating || isUpdating
                ? `${mode === "create" ? "Creating..." : "Updating..."}`
                : `${mode === "create" ? "Create" : "Update"}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default Createreport;