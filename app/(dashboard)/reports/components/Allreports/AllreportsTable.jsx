"use client"

import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { Suspense } from 'react'
import { useQuery } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { listOfReports, exportReportCSVApi } from '@/apis/expense-report'
import { EllipsisVertical, Eye, SquarePen } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { formatDate, formatExpenseDate } from '@/utils/Utils'
import { useRouter, useSearchParams } from 'next/navigation'
import { Checkbox } from '@/components/ui/checkbox'
import { useExportCSV } from '@/hooks/useExportCSV'
import { StatusBadge } from '@/utils/status'
import FilterWrapper from '@/components/reusables/FilterWrapper'
import { choicesApi } from '@/apis/utilapi'
import useSort from '@/hooks/useSort'
import useTableFilter from '@/hooks/useTableFilter'
import { FloatingLabelInput } from '@/components/ui/flaoting-label-input'
import TabWrapper from '@/components/reusables/TabWrapper'
import Reportdetail from '../../[id]/components/Reportdetail'
import DeletereportModal from '../DeletereportModal'
import Createreport from '../createreport'

const ReportsContent = ({tableTitle}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';

  // State management
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedReport, setSelectedReport] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    type: "",
    status: "",
    name: "",
    date: null,
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices for dropdown options
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch report data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfReports", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            date: "date"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'draft') {
        apiParams.is_submitted = false;
      } else if (currentTab !== 'all') {
        apiParams.status = currentTab;
        if (currentTab === 'pending') {
          apiParams.is_submitted = true;
        }
      }

      return listOfReports(apiParams);
    },
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true
  });

  // Filter data based on current tab
  const filteredData = useMemo(() => {
    if (!apiResponse?.results) return [];

    switch (currentTab) {
      case 'draft':
        return apiResponse.results.filter(report => !report.is_submitted);
      case 'pending':
        return apiResponse.results.filter(report => report.is_submitted && report.status === 'pending');
      case 'approved':
        return apiResponse.results.filter(report => report.status === 'approved');
      case 'rejected':
        return apiResponse.results.filter(report => report.status === 'rejected');
      default:
        return apiResponse.results;
    }
  }, [apiResponse?.results, currentTab]);

  // Sort functionality
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(filteredData);

  // Handle row selection
  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Handle CSV export
  const { handleExport } = useExportCSV(exportReportCSVApi, { filename: 'reports.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  // Get counts for tabs
  const getCounts = useMemo(() => {
    if (!apiResponse?.results) return { all: 0, draft: 0, pending: 0, approved: 0, rejected: 0 };

    return {
      all: apiResponse.results.length,
      draft: apiResponse.results.filter(item => !item.is_submitted).length,
      pending: apiResponse.results.filter(item => item.is_submitted && item.status === 'pending').length,
      approved: apiResponse.results.filter(item => item.status === 'approved').length,
      rejected: apiResponse.results.filter(item => item.status === 'rejected').length
    };
  }, [apiResponse?.results]);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData?.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true,
    },
    {
      accessorKey: "name",
      header: "Report Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "reference",
      header: "Reference Number",
      cell: ({ row }) => row.original.reference,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "type_display",
      header: "Report Type",
      cell: ({ row }) => row.original.report_type_display,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "status_display",
      header: "Status",
      cell: ({ row }) => (
        <StatusBadge status={row.original.status_display} isSubmitted={row.original.is_submitted} />
      ),
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "total_expenses_display",
      header: "Total Amount",
      cell: ({ row }) => row.original.total_expenses_display,
      sortable: true,
    },
    {
      accessorKey: "reimbursable_amount_display",
      header: "Reimbursable Amount",
      cell: ({ row }) => row.original.reimbursable_amount_display,
      sortable: true,
    },
    {
      accessorKey: "date",
      header: "Date",
      cell: ({ row }) => formatExpenseDate(row.original.date),
      sortable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true,
    },
    // {
    //   id: "view",
    //   header: "View",
    //   cell: ({ row }) => (
    //     <div onClick={(e) => { e.stopPropagation(); setSelectedReport(row.original); setSheetOpen(true); }}>
    //       <Eye strokeWidth={1.8} size={18} />
    //     </div>
    //     // setSelectedReport(row.original);
    //     // setSheetOpen(true)
    //   ),
    // },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        // Check if status is approved or rejected
        const isApprovedOrRejected = row.original.status === "approved" || row.original.status === "rejected";

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild >
              <Button aria-haspopup="true" size="icon" variant="ghost"
                className={isApprovedOrRejected ? "opacity-50 cursor-not-allowed" : ""}
              >
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            {!isApprovedOrRejected && (
              <DropdownMenuContent align="end">
                {row.original.status === "pending" && (
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    setIsEditModalOpen(true);
                    setSelectedReport(row.original);
                  }}>
                    Edit
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={(e) => { e.stopPropagation(); setSelectedReport(row.original); setSheetOpen(true); }}
                >
                  View
                </DropdownMenuItem>

                <DropdownMenuItem onClick={(e) => {
                  e.stopPropagation();
                  setIsDeleteModalOpen(true);
                  setSelectedReport(row.original);
                }}>
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            )}

          </DropdownMenu>
        );
      },
      sticky: true,
      unhidable: true
    }
  ], [selectedRows, sortedData, handleSelectAll]);

  // Memoize filterOptions
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
      {
        key: 'name',
        label: 'Filter by report name',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'type',
        placeholder: 'Report Type',
        options: choicesList?.data?.report_types || [],
        label: "Report Type",
      },
      {
        key: 'status',
        placeholder: 'Status',
        options: choicesList?.data?.approval_status || [],
        label: "Status"
      }
    ],
    showAmountFilter: true,
  }), [choicesList?.data]);

  // Custom filters specific to reports
  const reportCustomFilters = useMemo(() => (
    <>
      <div className="relative">
        <FloatingLabelInput
          id="name"
          name="name"
          label="Filter by report name"
          type="text"
          value={filters.name || ""}
          onChange={(e) => handleFilterApply({ ...filters, name: e.target.value })}
        />
      </div>
    </>
  ), [filters, handleFilterApply]);

  const [createReport, setCreateReport] = useState(false)

  return (
    <>
      <div className="w-full">
        <div className="no-scrollbar">
          <TableComponent
            rows={sortedData || []}
            columns={columns}
            tableTitle={tableTitle || 
              <TabWrapper
                tabs={[
                  { value: 'all', label: 'All', count: getCounts.all },
                  { value: 'draft', label: 'Draft', count: getCounts.draft },
                  { value: 'pending', label: 'Pending', count: getCounts.pending },
                  { value: 'approved', label: 'Approved', count: getCounts.approved },
                  { value: 'rejected', label: 'Rejected', count: getCounts.rejected },
                ]}
                defaultTab="all"
              />
            }
            onRowClick={(row) => router.push(`/reports/${row?.id}`)}
            NoavailableTitle={isLoading ? "Loading..." : apiResponse?.count <= 0 ? "Report" : ""}
            createTitle={<Button onClick={() => setCreateReport(true)} size="xs"> <SquarePen /> Create Report </Button>}
            tableDescription={
              "Record and organize your business expenses, track receipts, and manage reimbursements efficiently. Submit expenses individually or create detailed reports for approval."
            }
            filterComponents={
              <FilterWrapper
                filterValues={filters}
                onFilterApply={handleFilterApply}
                onFilterClear={handleFilterClear}
                filterOptions={filterOptions}
              />
            }
            exportToCSV={handleExportCSV}
            showImportExport={selectedRows.length > 0}
            onSort={handleSort}
            sortOrder={sortState.order}
            sortColumn={sortState.column}
          />
        </div>
      </div>

      <Reportdetail sheetOpen={sheetOpen} setSheetOpen={setSheetOpen} reportData={selectedReport} />
      {isEditModalOpen && <Createreport isOpen onClose={() => setIsEditModalOpen(false)} mode="edit" reportData={selectedReport} />}
      {isDeleteModalOpen && <DeletereportModal isOpen onClose={() => setIsDeleteModalOpen(false)} reportId={selectedReport} />}
      {createReport && <Createreport isOpen={createReport} onClose={() => setCreateReport(false)} mode="create" />}

    </>
  );
}

const AllreportsTable = ({tableTitle}) => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ReportsContent tableTitle={tableTitle}/>
    </Suspense>
  );
}

export default AllreportsTable;
