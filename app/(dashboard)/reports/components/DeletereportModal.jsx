import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteReportApi } from '@/apis/expense-report';
import toast from 'react-hot-toast';
import DeleteModal from '@/components/reusables/DeleteModal';

const DeletereportModal = ({ onClose, isOpen, reportId }) => {
  const queryClient = useQueryClient();

  const { mutateAsync: deteleReportMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-report"],
    mutationFn: deleteReportApi
  });

  const handleDeleteReport = async (e) => {
    e?.preventDefault?.();
    try {
      const response = await deteleReportMutation({ id: reportId?.id });
      toast.success(response?.message || "Report Deleted");
      onClose();
      queryClient.invalidateQueries(["listOfReports"]);
    } catch (error) {
      toast.error(error?.message);
      console.log(error);
    }
  };

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteReport}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{reportId?.name}</span>?</>
      }
        description="This action will permanently delete the report and all its associated data. This cannot be undone."
    />
  );
};

export default DeletereportModal;