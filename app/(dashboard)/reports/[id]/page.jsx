"use client"

import React from 'react'
import { useQuery } from '@tanstack/react-query';
import { listOfExpenses } from '@/apis/expenses';
import { Noexpense } from './components/noexpense-and-advances/noexpenses';
import Expensetable from './components/tablesunderreport/Expensetable';

const Page = () => {

  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfExpenses"],
    queryFn: listOfExpenses,
  });

  return (
    <div>
      {/* Lorem. */}
      {/* {!apiResponse?.count < 1 ? (
        <Noexpense />
      ) : (
        <Expensetable />
      )} */}
    </div>
  )
}

export default Page
