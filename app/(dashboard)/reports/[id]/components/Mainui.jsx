"use client"

import AllCards from '@/components/reusables/Allcards'
import { Button } from '@/components/ui/button'
import React, { useState } from 'react'
import { CircleArrowRight, CircleCheckBig } from 'lucide-react'
import { RetrieveReports } from '@/apis/expense-report'
import { listOfApprovalsExpenses, retrieveApprovals } from '@/apis/approvals'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import Pageloader from '@/utils/spinner/Pageloader'
import Createreport from '../../components/createreport'
import Reportdetail from './Reportdetail'
import { listOfExpenses } from '@/apis/expenses'
import Expensetable from './tablesunderreport/Expensetable'
import toast from 'react-hot-toast'
import Rejectreport from './rejectreport/Rejectreport'
import Navigateback from '@/utils/navigateback'
import { StatusBadge } from '@/utils/status'
import { Role } from '@/utils/roles/Role'
import { ApprovedModal } from './approved/ApprovedModal'
import TabWrapper from '@/components/reusables/TabWrapper'
import AdvancesRefunds from '../advances-refunds/page'; // Import the advances component
import { useSearchParams } from 'next/navigation';
import { SubmitReportModal } from './approved/SubmitReportModal'

const Mainui = ({ id, pathname, type = 'report' }) => {

  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'expenses';

  // const { id } = useParams()
  const navigate = useRouter()
  // const { pathname } = usePathname()

  const { data: reportData, isPending, refetch: RefetchReport } = useQuery({
    queryKey: [type === 'report' ? "retrieveReport" : "retrieveApproval", id],
    queryFn: () => type === 'report' ? RetrieveReports(id) : retrieveApprovals(id),
    enabled: !!id,
    onError: (error) => {
      toast.error(error?.message || `Failed to retrieve ${type}`)
    }
  })

  const { data: apiResponse, isPending: expenseLoading } = useQuery({
    queryKey: [type === 'report' ? "listOfExpenses" : "listOfApprovalExpenses", id],
    queryFn: () => type === 'report' ? listOfExpenses(id && { report: id }) : listOfApprovalsExpenses(id && { report: id }),
    enabled: !!id,
    onError: (error) => {
      toast.error(error?.message || `Failed to load expenses ${type}`)
    }
  });

  // State management
  const [sheetOpen, setSheetOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isRejectModalOpen, setIsRejectModalOpen] = useState(false);
  const [isApprovedModalOpen, setIsApprovedModalOpen] = useState(false);
  const [isSubmittedModalOpen, setIsSubmittedModalOpen] = useState(false);

  const handleRowClick = () => {
    setSheetOpen(true);
  };

  const { isAdministrator, isApproverManager } = Role()

  const isReportRejected = reportData?.data?.status === 'rejected';
  const isReportApproved = reportData?.data?.status === 'approved';

  const isAdvancesRoute = pathname?.includes('advances-refunds')


  return (
    <>
      {isPending ? (
        <div>
          <Pageloader className={"w-full min-h-screen flex items-center justify-center"} />
        </div>
      ) : (
        <div className='overflow-x-scroll'>

          <div>
            <div className="mb-4 px-4 lg:px-4">
              <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between mb-3 .sticky .top-10">
                <div className="flex flex-col mt-4">
                  <Navigateback />
                  <div className="flex items-center gap-3">
                    <h1 className="text-lg font-semibold md:text-xl whitespace-nowrap">{reportData?.data?.name}</h1>
                    {reportData?.data?.is_submitted ? (
                      <StatusBadge status={reportData?.data?.status_display} />
                    ) : (
                      <div className="text-[10px] border border-[#D7E5FF] rounded-[8px] py-[2px] px-[6px] font-[600] text-[#183C82] bg-[#E6EEFF]">Draft</div>
                    )}
                  </div>
                  <div className='text-sm flex items-center gap-3'>{reportData?.data?.reference}
                    <span onClick={handleRowClick} className='font-medium cursor-pointer flex items-center text-sm gap-1'>more <CircleArrowRight strokeWidth={1.5} size={15} /></span>
                  </div>
                </div>

                <div className='flex items-center gap-2'>
                  <Button
                    disabled={reportData?.data?.is_submitted}
                    variant="outline"
                    size="xs"
                    // className={`gap-1 text-xs rounded-full p-3`}
                    onClick={() => setIsEditModalOpen(true)}
                  >
                    Edit
                  </Button>

                  {(reportData?.data?.is_submitted === false) && (
                    <Button
                      size="xs"
                      // onClick={handleSubmit}
                      onClick={() => setIsSubmittedModalOpen(true)}
                      disabled={apiResponse?.count < 1 || isReportRejected}
                    // className={`gap-1 text-xs rounded-full p-3`}
                    >
                      <>
                        <span>Submit</span>
                      </>
                    </Button>

                  )}

                  {(reportData?.data?.is_submitted && isAdministrator || isApproverManager) && (
                    <>
                      {/* Reject Button */}
                      <Button
                        variant="outline"
                        size="xs"
                        className={`${isReportApproved && "hidden"}`}
                        // className={`gap-1 text-xs rounded-full p-3 ${isReportApproved && "hidden"}`}
                        onClick={() => setIsRejectModalOpen(true)}
                        disabled={isReportRejected}
                      >
                        {isReportRejected ? (
                          <span>Rejected</span>
                        ) : (
                          <span>Reject</span>
                        )}
                      </Button>

                      {!isReportRejected && (
                        <Button size="xs"
                          // className="gap-1 text-xs rounded-full p-3"
                          disabled={apiResponse?.count < 1 || reportData?.data?.status === "approved"}
                          onClick={() => setIsApprovedModalOpen(true)}>
                          <>
                            <CircleCheckBig />
                            <span>Approve</span>
                          </>
                        </Button>
                      )}
                    </>
                  )}


                </div>

              </div>
            </div>

            <div className="flex items-center border rounded-lg mx-4 lg:mx-4">
              <AllCards
                cardData={reportData?.data}
              />
            </div>

            <div className="flex flex-col sm:flex-row .items-center gap-4 my-4">
              <div className='px-4 p-2 lg:px-4 flex items-center justify-between w-full'>
                <TabWrapper
                  tabs={[
                    { label: 'Expenses', value: 'expenses', },
                    { label: 'Advances and Refunds', value: 'advances-refunds', },
                    // { value: 'advances', label: 'Advance and Refunds', route: type === 'report' ? `/reports/${id}/advances-refunds` : `/approvals/${id}/advances-refunds` }
                  ]}
                  defaultTab="expenses"
                />
                <div className='flex items-center gap-2'>
                  {/* <div className="border rounded-lg">
                    <ArrowDownNarrowWide className='p-1' strokeWidth={1.5} size={20} />
                  </div>
                  <div className="border rounded-lg">
                    <AlignJustify className='p-1' strokeWidth={1.5} size={20} />
                  </div> */}
                </div>


              </div>
            </div>
          </div>


          {/* {!isAdvancesRoute && (
            <Expensetable
              apiResponse={apiResponse}
              id={id}
              isLoading={expenseLoading}
            />
          )} */}

          {currentTab === 'expenses' && (
            <Expensetable
              apiResponse={apiResponse}
              id={id}
              isLoading={expenseLoading}
              reportData={reportData?.data}

            />
          )}

          {currentTab === 'advances-refunds' && (
            <AdvancesRefunds />
          )}

        </div>

      )}
      {isEditModalOpen && (
        <Createreport
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          reportData={reportData?.data}
          mode="edit"
        />
      )}

      {isRejectModalOpen && (
        <Rejectreport
          isOpen={isRejectModalOpen}
          onClose={() => setIsRejectModalOpen(false)}
          reportData={reportData?.data}
          RefetchReport={RefetchReport}
        />
      )}

      {isApprovedModalOpen && (
        <ApprovedModal
          isOpen={isApprovedModalOpen}
          onClose={() => setIsApprovedModalOpen(false)}
          id={id}
          reportData={reportData?.data}
          RefetchReport={RefetchReport}
        />
      )}

      {isSubmittedModalOpen && (
        <SubmitReportModal
          isOpen={isSubmittedModalOpen}
          onClose={() => setIsSubmittedModalOpen(false)}
          reportData={reportData?.data}
          isAdministrator={isAdministrator}
          isApproverManager={isApproverManager}
          RefetchReport={RefetchReport}
          id={id}
        />
      )}

      <Reportdetail
        sheetOpen={sheetOpen} reportData={reportData?.data} setSheetOpen={setSheetOpen}
      />

    </>

  )
}

export default Mainui;