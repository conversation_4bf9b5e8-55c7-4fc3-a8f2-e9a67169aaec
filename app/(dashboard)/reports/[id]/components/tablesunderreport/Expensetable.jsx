"use client"

import React, { useCallback, useEffect, useState } from 'react';
import { TableComponent } from '@/components/reusables/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Ban, EllipsisVertical, PlusIcon } from "lucide-react";
import { formatDate, formatExpenseDate } from '@/utils/Utils';
import { Checkbox } from '@/components/ui/checkbox';
import { Noexpense } from '../noexpense-and-advances/noexpenses';
import DeleteexpenseModal from '@/app/(dashboard)/expenses/components/DeleteexpenseModal';
import { ExpenseModal } from '@/app/(dashboard)/expenses/components/Allexpense/ExpenseModals/ExpenseModal';
import TableFilters from '@/components/reusables/TableFilters';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { exportExpenseCSVApi } from '@/apis/expenses';
import { useExportCSV } from '@/hooks/useExportCSV';

const Expensetable = ({ id, apiResponse, isLoading, reportData }) => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);

  // Sorting state (client-side)
  const [sortOrder, setSortOrder] = useState("asc");
  const [sortColumn, setSortColumn] = useState("");
  const [sortedRows, setSortedRows] = useState([]);

  // Initialize filters from URL params
  const [filters, setFilters] = useState(() => {
    const initialFilters = {
      reference: searchParams.get('reference') || "",
      title: searchParams.get('title') || "",
      minAmount: searchParams.get('minAmount') || "",
      maxAmount: searchParams.get('maxAmount') || "",
      type: searchParams.get('type') || "",
      dateRange: searchParams.get('date_from') && searchParams.get('date_to') ? {
        from: searchParams.get('date_from'),
        to: searchParams.get('date_to')
      } : null
    };

    return initialFilters;
  });
  // CSV Export functionality
  const { handleExport } = useExportCSV(exportExpenseCSVApi, { filename: 'expenses.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : { report: id });

  // Handle select all checkbox
  const handleSelectAll = (checked) => {
    setSelectedRows(checked ? apiResponse?.results.map(r => r.id) : []);
  };

  // Client-side sorting implementation
  const handleSort = (column) => {
    const newSortOrder = sortColumn === column.title && sortOrder === "asc" ? "desc" : "asc";
    setSortOrder(newSortOrder);
    setSortColumn(column.title);

    const sorted = [...(apiResponse?.results || [])].sort((a, b) => {
      let aValue, bValue;

      // Extract values based on column accessor
      if (typeof column.accessor === 'function') {
        // For complex accessors, we need to extract the raw value
        // This is a simplified approach - you might need to adjust based on your data structure
        const aContent = column.accessor(a);
        const bContent = column.accessor(b);

        // Try to extract text content if it's a React element
        if (React.isValidElement(aContent)) {
          aValue = aContent.props.children;
        } else {
          aValue = aContent;
        }

        if (React.isValidElement(bContent)) {
          bValue = bContent.props.children;
        } else {
          bValue = bContent;
        }
      } else {
        // For simple string accessors
        aValue = a[column.accessor];
        bValue = b[column.accessor];
      }

      // Handle numeric values
      if (!isNaN(aValue) && !isNaN(bValue)) {
        aValue = Number(aValue);
        bValue = Number(bValue);
      }

      // Compare values
      if (aValue < bValue) return newSortOrder === "asc" ? -1 : 1;
      if (aValue > bValue) return newSortOrder === "asc" ? 1 : -1;
      return 0;
    });

    setSortedRows(sorted);
  };

  // Update sorted rows when API response changes
  useEffect(() => {
    if (apiResponse?.results) {
      setSortedRows(apiResponse.results);
    }
  }, [apiResponse]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Server-side filtering implementation
  const handleFilterApply = useCallback((newFilters) => {
    // Create a new URLSearchParams object
    const params = new URLSearchParams(searchParams);

    // Update or remove parameters based on filter values
    if (newFilters.reference) params.set('reference', newFilters.reference);
    else params.delete('reference');

    if (newFilters.title) params.set('title', newFilters.title);
    else params.delete('title');

    if (newFilters.minAmount) params.set('minAmount', newFilters.minAmount);
    else params.delete('minAmount');

    if (newFilters.maxAmount) params.set('maxAmount', newFilters.maxAmount);
    else params.delete('maxAmount');

    if (newFilters.type) params.set('type', newFilters.type);
    else params.delete('type');

    if (newFilters.dateRange?.from && newFilters.dateRange?.to) {
      params.set('date_from', newFilters.dateRange.from);
      params.set('date_to', newFilters.dateRange.to);
    } else {
      params.delete('date_from');
      params.delete('date_to');
    }

    // Update the URL with the new search parameters
    router.push(`${pathname}?${params.toString()}`);

    // Update local state
    setFilters(newFilters);
    // Reset sorting when filters change
    setSortColumn("");
    setSortOrder("asc");
    setSortedRows([]);
  }, [searchParams, pathname, router]);

  // Handle filter clearing
  const handleFilterClear = useCallback(() => {
    const clearedFilters = {
      reference: "",
      title: "",
      minAmount: "",
      maxAmount: "",
      dateRange: null,
      type: ""
    };

    // Update URL by removing all filter parameters
    router.push(pathname);

    // Update local state
    setFilters(clearedFilters);
    // Reset sorting when filters are cleared
    setSortColumn("");
    setSortOrder("asc");
    setSortedRows([]);
  }, [pathname, router]);

  const columns = [
    {
      title: (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === (apiResponse?.results?.length || 0) && apiResponse?.results?.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.includes(row.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.id]
                : prev.filter(id => id !== row.id)
              )
            }
          />
        </div>
      ),
    },
    { title: "Report Name", accessor: (row) => (row.report.name), sortable: true },
    { title: "Reference Number", accessor: (row) => row.reference, sortable: true },
    { title: "Expense Title", accessor: (row) => row.title, sortable: true },
    { title: "Expense Type", accessor: (row) => row.expense_type.name, sortable: true },
    { title: "Amount", accessor: (row) => row.amount_display, sortable: true },
    { title: "Merchant", accessor: (row) => (row.expense_type.name === "Mileage" ? <div className='flex items-center justify-center'>--</div> : row.merchant.name), sortable: true },
    { title: "Expense Date", accessor: (row) => formatExpenseDate(row.date), sortable: true },
    { title: "Created", accessor: (row) => formatDate(row?.created_at), sortable: true },
    {
      title: "Actions",
      accessor: (row) => (
        <div>
          { reportData?.is_submitted ? (
            <div className="flex justify-center">
              <Ban size={18} strokeWidth={1.8} />
            </div>
          ) : (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button aria-haspopup="true" size="icon" variant="ghost">
                  <EllipsisVertical />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={(e) => {
                    setIsEditModalOpen(true);
                    setSelectedRow(row);
                    e.stopPropagation();
                  }}>
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsDeleteModalOpen(true);
                    setSelectedRow(row);
                  }}
                >Delete</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      ),
      sticky: true,
    },
  ];

  const rows = sortedRows.length > 0 && sortColumn ? sortedRows : (apiResponse ? apiResponse.results : []);

  return (
    <>
      <div className='overflow-x-scroll w-full'>
        {apiResponse?.count === 0 || apiResponse?.count < 1 ? (
          <Noexpense reportId={id} />
        ) : (
          <div className="no-scrollbar">
            <TableComponent
              rows={rows}
              columns={columns}
              tableTitle="All Expenses"
              filterComponents={
                <TableFilters
                  filterValues={filters}
                  setFilterValues={setFilters}
                  onFilterApply={handleFilterApply}
                  onFilterClear={handleFilterClear}
                  syncWithUrl={true}
                  filterOptions={{
                    showReference: true,
                    showTypeFilter: true,
                    showAmountFilter: true,
                    referencePlaceholder: "Filter by reference",
                    clearButtonText: "Clear Filters",
                    applyButtonText: "Apply Filters"
                  }}
                />
              }
              // tableDescription={
              //   "Track and manage all your expenses. Create, edit, and submit expenses for reimbursement."
              // }
              // NoavailableTitle={"Expenses"}

              exportToCSV={handleExportCSV}
              showImportExport={selectedRows.length > 0}
              onSort={handleSort}
              sortOrder={sortOrder}
              sortColumn={sortColumn}
            />

            {/* Only show the Add New Expense button if report is not rejected or approved */}
            { !reportData?.is_submitted && (
              <div className="flex items-center justify-center">
                <Button size="xs"
                  // className="rounded-md p-3"
                  onClick={() => setIsModalOpen(true)}>
                  <PlusIcon /> Add New Expense
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {isDeleteModalOpen && selectedRow && (
        <DeleteexpenseModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          expenseId={selectedRow}
        />
      )}

      {(isModalOpen || isEditModalOpen) && (
        <ExpenseModal
          isOpen={isModalOpen || isEditModalOpen}
          onClose={() => {
            if (isModalOpen) setIsModalOpen(false);
            if (isEditModalOpen) setIsEditModalOpen(false);
          }}
          reportId={id}
          mode={isEditModalOpen ? "edit" : "create"}
          expenseData={isEditModalOpen ? selectedRow : null}
        />
      )}
    </>
  );
};

export default Expensetable;