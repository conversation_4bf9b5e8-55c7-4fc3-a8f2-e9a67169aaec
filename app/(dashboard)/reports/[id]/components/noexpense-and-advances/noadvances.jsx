"use client"

import { RecordAdvanceModal } from '@/app/(dashboard)/advances/components/RecordAdvanceModal'
import { Button } from '@/components/ui/button'
import { ChevronDown, PlusIcon, Upload } from 'lucide-react'
import React, { useState } from 'react'

export const Noadvances = ({reportId}) => {

  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <div className=".w-full border-4 border-dashed rounded-xl flex items-center justify-center h-96 m-4">
        <div className='flex flex-col gap-3 items-center justify-center text-center'>
          <div className='flex items-center justify-center flex-col gap-y-2'>
            <Upload size={"40px"} strokeWidth={"1"} />
            <div className='font-semibold text-xs'>Apply and Record advance for this report</div>
            {/* <div className='font-normal text-xs'>or click here to upload</div> */}
          </div>
          <div className='flex items-center gap-4'>
            <Button className="rounded-xl bg-gray-300 text-primary hover:text-white" > Apply Advance (2) <ChevronDown /> </Button>
            <Button className="rounded-xl" variant="outline" onClick={openModal}> <PlusIcon /> Record Advance</Button>
          </div>
        </div>
      </div>

      <RecordAdvanceModal
        isOpen={isModalOpen}
        onClose={closeModal}
        reportId={reportId}  // Pass reportId to the modal

      />
    </>
  )
}