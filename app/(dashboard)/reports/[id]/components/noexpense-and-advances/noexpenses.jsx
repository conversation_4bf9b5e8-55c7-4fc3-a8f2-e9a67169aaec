"use client"

import { useState, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { readReceipt<PERSON><PERSON> } from '@/apis/expense-report'
import { ExpenseModal } from '@/app/(dashboard)/expenses/components/Allexpense/ExpenseModals/ExpenseModal'
import { Button } from '@/components/ui/button'
import { PlusIcon, Upload } from 'lucide-react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import toast from 'react-hot-toast'

export const Noexpense = ({ reportId }) => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [prefillData, setPrefillData] = useState(null)
  const [files, setFiles] = useState([])
  const [uploadType, setUploadType] = useState("");
  const fileInputRef = useRef(null);

  const handleOCRResponse = useCallback((result, uploadedFiles) => {
    if (!result?.data) return;

    const expenseData = result.data;
    setFiles(uploadedFiles);
    
    console.log("OCR result data:", expenseData);
    console.log("Uploaded files:", uploadedFiles);

    if (Array.isArray(expenseData) && expenseData.length > 0) {
      // For multiple expenses
      setPrefillData(expenseData.map((item, index) => {
        const receipt = uploadedFiles[index] || null;
        console.log(`Preparing receipt for expense ${index}:`, receipt);
        
        return {
          ...item,
          receipt: receipt,
          tempId: `placeholder-${Date.now()}-${index}`
        };
      }));
    } else {
      // For single expense
      const receipt = uploadedFiles[0] || null;
      console.log("Preparing receipt for single expense:", receipt);
      
      setPrefillData([{
        ...expenseData,
        receipt: receipt,
        tempId: `placeholder-${Date.now()}-0`
      }]);
    }

    setIsModalOpen(true);
  }, []);

  const processReceipts = useCallback(async (uploadedFiles) => {
    try {
      setIsProcessing(true);
      const formData = new FormData();

      // Process files to ensure they have preview URLs
      const processedFiles = uploadedFiles.map(file => {
        // Create preview URLs for images
        if (file.type.startsWith('image/')) {
          return Object.assign(file, {
            preview: URL.createObjectURL(file)
          });
        }
        return file;
      });

      // Append processed files to FormData
      processedFiles.forEach((file) => {
        formData.append('images', file);
      });

      const result = await readReceiptApi(formData);
      toast.success(result?.message || 'Receipts processed successfully');

      handleOCRResponse(result, processedFiles);
    } catch (error) {
      toast.error(error.message || 'Error processing receipts');
      console.error('OCR Error:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [handleOCRResponse]);

  const onDrop = useCallback(async (acceptedFiles) => {
    if (acceptedFiles?.length > 0) {
      await processReceipts(acceptedFiles);
    }
  }, [processReceipts]);

  const handleUploadTypeChange = (value) => {
    setUploadType(value);
    if (value === "computer") {
      fileInputRef.current?.click();
    } else if (value === "drive") {
      handleDriveUpload();
    }
  };

  const handleComputerUpload = async (event) => {
    const files = event.target.files;
    if (files?.length > 0) {
      await processReceipts(Array.from(files));
    }
  };

  const handleDriveUpload = async () => {
    try {
      // Initialize Google Drive API
      const gapi = window.gapi;
      await gapi.client.init({
        apiKey: process.env.NEXT_PUBLIC_GOOGLE_API_KEY,
        clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
        discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
        scope: 'https://www.googleapis.com/auth/drive.readonly'
      });

      // Show Google Drive picker
      const picker = new google.picker.PickerBuilder()
        .addView(google.picker.ViewId.DOCS)
        .setOAuthToken(gapi.auth.getToken().access_token)
        .setCallback(handleDrivePickerCallback)
        .build();
      picker.setVisible(true);
    } catch (error) {
      toast.error('Failed to initialize Google Drive');
      console.error('Drive Error:', error);
    }
  };

  const handleDrivePickerCallback = async (data) => {
    if (data.action === google.picker.Action.PICKED) {
      try {
        setIsProcessing(true);
        const files = await Promise.all(
          data.docs.map(async (doc) => {
            const response = await fetch(
              `https://www.googleapis.com/drive/v3/files/${doc.id}?alt=media`,
              {
                headers: {
                  Authorization: `Bearer ${gapi.auth.getToken().access_token}`,
                },
              }
            );
            const blob = await response.blob();
            return new File([blob], doc.name, { type: blob.type });
          })
        );
        await processReceipts(files);
      } catch (error) {
        toast.error('Failed to process files from Drive');
        console.error('Drive Processing Error:', error);
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
      // 'application/pdf': ['.pdf']
    },
    multiple: true // Enable multiple file upload
  });

  return (
    <>
      <div
        {...getRootProps()}
        className={`w-full border-4 border-dashed rounded-xl flex items-center justify-center h-96 m-4 transition-colors
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-200'}`}
      >
        <input {...getInputProps()} />
        <div className='flex flex-col gap-3 items-center justify-center text-center'>
          <div className='flex items-center justify-center flex-col gap-y-2'>
            <Upload
              size={"40px"}
              strokeWidth={"1"}
              className={isProcessing ? 'animate-pulse' : ''}
            />
            <div className='font-semibold text-xs'>
              {isProcessing ? 'Processing Receipts...' : 'Drag and Drop Multiple Receipts'}
            </div>
            <div className='font-normal text-xs'>or click here to upload</div>
          </div>
          <div className='flex items-center gap-4'>
            <div>
              <Select onValueChange={handleUploadTypeChange} value={uploadType}>
                <SelectTrigger
                  className="text-xs rounded-[10px] h-[36px]"
                  disabled={isProcessing}
                >
                  <SelectValue placeholder="Autoscan Receipts" />
                </SelectTrigger>
                <SelectContent className="rounded-[10px] text-xs">
                  <SelectItem value="computer" className="rounded-lg text-xs">
                    Upload from computer
                  </SelectItem>
                  <SelectItem value="drive" className="rounded-lg text-xs">
                    Upload from drive
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              className="rounded-xl"
              variant="outline"
              onClick={(e) => {
                setIsModalOpen(true);
                e.stopPropagation();
              }}
              disabled={isProcessing}
            >
              <PlusIcon /> New Expense
            </Button>
          </div>
        </div>
      </div>

      {/* Add hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        multiple
        accept="image/*"
        onChange={handleComputerUpload}
      />

      {isModalOpen && (
        <ExpenseModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setPrefillData(null);
            setFiles([]); // Clear files when closing modal
          }}
          reportId={reportId}
          prefillData={prefillData}
          files={files}
        />
      )}
    </>
  );
};
