import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useState } from 'react'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import AllCards from '@/components/reusables/Allcards';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { CircleX } from 'lucide-react';
import toast from 'react-hot-toast';
import { rejectReportApi } from '@/apis/expense-report';
import { useRouter } from 'next/navigation';
import { StatusBadge } from '@/utils/status';

const Rejectreport = ({ onClose, isOpen, reportData, RefetchReport }) => {

  const navigate = useRouter()

  const { mutateAsync: rejectReportMutation, isPending: isRejectingReport } = useMutation({
    mutationKey: ["reject-report"],
    mutationFn: rejectReportApi
  })

  const [reason, setReason] = useState("")

  const handleRejectApproval = async (e) => {
    e.preventDefault();
    try {
      if (!reason.trim()) {
        toast.error('Please provide a reason for rejection');
        return;
      }
      const response = await rejectReportMutation({ id: reportData?.id, reason })
      onClose();
      toast.success(response?.message)
      RefetchReport();
      navigate.push("/approvals")
    } catch (error) {
      console.log("error", error)
      toast.error(error?.message)
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[60rem] overflow-hidden">

        <div className='mx-4 lg:mx-4'>

          <div className="flex flex-col mb-4">
            <div className="flex items-center gap-3">
              <h1 className="text-lg font-semibold md:text-xl whitespace-nowrap">{reportData?.name}</h1>
              {reportData?.is_submitted ? (
                <StatusBadge status={reportData?.status_display} />
              ) : (
                <div className="text-xs border rounded-full p-[2px] px-4 font-semibold text-black bg-gray-300">Unreported</div>
              )}
            </div>
          </div>

          <div className="flex items-center border rounded-lg text-[10px]">
            <AllCards
              cardData={reportData}
              className={"text-xs"}
              iconSize={"16"}
              title={"Submitted by"}
            />
          </div>

          <form action="" onSubmit={handleRejectApproval}>
            <div className='mb-2 mt-5 text-lg font-semibold md:text-lg'>Reject Report</div>
            <Textarea
              placeholder={"Reason for rejection"}
              rows="8"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              required
            />

            <div className="flex items-center justify-center sm:mt-10 gap-2">
              <DialogClose asChild>
                <Button variant="outline" size="xs"
                  // className="h-7 gap-1 text-xs rounded-full p-3"
                >Cancel</Button>
              </DialogClose>

              <Button
                disabled={isRejectingReport || !reason}
                // className="h-7 gap-1 text-xs rounded-full p-3"
                size="xs"
              > <CircleX />{isRejectingReport ? 'Rejecting...' : 'Reject'}</Button>
            </div>

          </form>
        </div>

      </DialogContent>
    </Dialog>
  )
}

export default Rejectreport;
