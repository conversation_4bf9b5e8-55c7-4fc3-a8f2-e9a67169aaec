import React from 'react'
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet"

const Reportdetail = ({ reportData, sheetOpen, setSheetOpen }) => {
  return (
    <>
      <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
        <SheetContent side="right" className="w-[95%] sm:w-full rounded-s-xl flex flex-col gap-4">
          <div className="flex flex-col gap-4 py-4">
            <SheetHeader className='font-semibold text-xl'>
              <SheetTitle>Description</SheetTitle>
            </SheetHeader>
            <div className='text-[12px]'>
              {reportData?.purpose}
            </div>
            <div className='font-semibold text-lg'>Policy</div>
            <div className='text-gray-700 text-sm'>{reportData?.policy?.name}</div>

            <div>
              <div className='font-semibold text-lg'>Amount</div>
              <hr />
              <div className='text-table_gray_text text-[13px] font-normal leading-8'>
                <div className='flex justify-between items-center'>
                  <span>Total Expense Amonut:</span> {reportData?.total_expenses_display}
                </div>

                <div className='flex justify-between items-center'>
                  <span>Non-reimbursable Amount:</span> {reportData?.non_reimbursable_amount_display}
                </div>
                <div className='flex justify-between items-center'>

                  <span>Applied Advance Amount:</span> {reportData?.total_advances_display}
                </div>
              </div>
              <div className="flex items-center justify-between font-semibold text-xs">
                <span >Amount to be Reimbursed:</span> {reportData?.reimbursable_amount_display}
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  )
}

export default Reportdetail
