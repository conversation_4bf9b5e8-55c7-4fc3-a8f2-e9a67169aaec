import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { submitReportApi } from "@/apis/expense-report";
import { useRouter } from "next/navigation";
import DeleteModal from "@/components/reusables/DeleteModal";

export const SubmitReportModal = ({ isOpen, onClose, reportData, id, RefetchReport, isAdministrator, isApproverManager }) => {
  const navigate = useRouter()

  const { mutateAsync: submitMutation, isPending: isSubmitingReport } = useMutation({
    mutationKey: ["submit-report"],
    mutationFn: submitReportApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose()
      RefetchReport();
      // If user is not an administrator AND not an approver manager, redirect to reports
      if (!isAdministrator && !isApproverManager) {
        navigate.push("/reports")
      }
    },
    onError: (error) => {
      toast.error(error?.message || 'Failed to submit report')
    }
  })


  const handleReport = async (e) => {
    e.preventDefault();
    try {
      await submitMutation({ id })
    } catch (error) {
      // Error handling is done in mutation config
    }
  };

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleReport}
      isDeleting={isSubmitingReport}
      isDeletingText={"Submitting..."}
      confirmText={"Submit"}
      title={
        <>Are you sure you want to submit <span className='font-semibold text-sm capitalize'>{reportData?.name}</span>?</>
      }
      description="Review and submit your report for approval. Make sure all details are correct before proceeding, This cannot be undone."
    />
  )
}