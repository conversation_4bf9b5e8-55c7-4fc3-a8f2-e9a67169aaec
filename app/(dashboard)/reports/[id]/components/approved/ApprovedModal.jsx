import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { approveReportApi } from "@/apis/expense-report";
import { useRouter } from "next/navigation";
import DeleteModal from "@/components/reusables/DeleteModal";

export const ApprovedModal = ({ isOpen, onClose, id, reportData, RefetchReport }) => {
  const navigate = useRouter()

  const { mutateAsync: approveReportMutation, isPending: isApprovingReport } = useMutation({
    mutationKey: ["approve-report"],
    mutationFn: approveReportApi,
    onSuccess: (response) => {
      toast.success(response?.message)
      onClose();
      RefetchReport();
      navigate.push("/approvals")
    },
    onError: (error) => {
      toast.error(error?.message || 'Failed to approve report')
    }
  })


  const handleApproval = async (e) => {
    e.preventDefault();
    try {
      await approveReportMutation({ id })
    } catch (error) {
      // Error handling is done in mutation config
    }
  };

  return (
    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleApproval}
      isDeleting={isApprovingReport}
      isDeletingText={"Approving..."}
      confirmText={"Approve"}
      title={
        <>Are you sure you want to submit <span className='font-semibold text-sm capitalize'>{reportData?.name}</span>?</>
      }
      description="By approving this expense, you confirm that all details have been verified and meet company policy requirements. This action cannot be undone."
    />
  )
}