"use client"

import { Noadvances } from '@/app/(dashboard)/reports/[id]/components/noexpense-and-advances/noadvances'
import { TableComponent } from '@/components/reusables/table';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { EllipsisVertical, PlusIcon } from 'lucide-react';
import React, { useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { listOfAdvances } from '@/apis/advances';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'next/navigation';
import { RecordAdvanceModal } from '@/app/(dashboard)/advances/components/RecordAdvanceModal';

const AdvancesRefunds = () => {

  const { id } = useParams()
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: advancesResponse, isPending: advancesLoading } = useQuery({
    queryKey: ["listOfAdvances", id],
    queryFn: () => listOfAdvances(id ? { report: id } : null),
    enabled: !!id,
    onError: (error) => {
      toast.error(error?.message || `Failed to load advances ${type}`)
    }
  });

  console.log("advres", advancesResponse)
  console.log("advid", id)

  const reportStatus = advancesResponse?.results?.[0]?.report?.status || 'pending';
  const isReportRejectedOrApproved = reportStatus === 'rejected' || reportStatus === 'approved';

  const columns = [
    {
      accessor: (row) => (
        <div className='flex items-center justify-center'>
          <Checkbox />
        </div>
      )
    },
    {
      title: "Title",
      accessor: (row) => row.title,
    },
    {
      title: "Reference Number",
      accessor: (row) => row.reference,
    },
    {
      title: "Amount",
      accessor: (row) => row.amount_display,
    },
    {
      title: "Currency",
      accessor: (row) => row.amount_currency,
    },
    {
      title: "Paid Through",
      accessor: (row) => row.paid_through_display,
    },
    {
      title: "Report Name",
      accessor: (row) => row.reference,
    },
    {
      title: "Expense Date",
      accessor: (row) => row.date,
    },
    {
      title: "Created",
      accessor: (row) => row.created_at, // Removed formatDate since it wasn't defined
    },
    {
      accessor: (row) => (
        <div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button aria-haspopup="true" size="icon" variant="ghost">
                <EllipsisVertical />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem>Edit</DropdownMenuItem>
              <DropdownMenuItem>Delete</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className='sm:overflow-x-scroll w-full'>
        {advancesResponse?.count < 1 ? (
          <Noadvances reportId={id} />
        ) : (
          <div className="no-scrollbar">
            <TableComponent
              rows={advancesResponse?.results}
              columns={columns}
              tableTitle="All Advances"
            />

            {!isReportRejectedOrApproved && (
              <div className="flex items-center justify-center">
                <Button size="xs" onClick={() => setIsModalOpen(true)} > <PlusIcon /> Add New Advance</Button>
              </div>
            )}
          </div>
        )}
      </div>

      {isModalOpen && (
        <RecordAdvanceModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          reportId={id}  // Pass reportId to the modal
        />
      )}
    </>

  );
};

export default AdvancesRefunds;