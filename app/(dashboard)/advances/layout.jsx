"use client"

import { But<PERSON> } from "@/components/ui/button";
import { RecordAdvanceModal } from "./components/RecordAdvanceModal";
import { useState } from "react";
import { SquarePen } from "lucide-react";

export default function AdvancesLayout({ children }) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="overflow-x-hidden grid auto-rows-max">

      <div className="p-4 px-4 border-b">
        <div className="flex flex-row items-center justify-between w-full gap-4">

          <div>
            <h3 className="text-sm font-semibold">Advances</h3>
          </div>

          <div className="flex items-center gap-3">
            <div>
              <Button
                size="xs"
                // className={`gap-1 rounded-full p-3`}
                onClick={() => setIsModalOpen(true)}
              >
                <SquarePen />
                <span>Record Advances</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <RecordAdvanceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />

      <main className="overflow-x-scroll">{children}</main>
    </div>
  );
}
