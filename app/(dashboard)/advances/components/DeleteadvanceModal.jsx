import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteAdvancesApi } from '@/apis/advances';
import toast from 'react-hot-toast';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteadvanceModal = ({ onClose, isOpen, advanceId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deteleAdvanceMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-advance"],
    mutationFn: deleteAdvancesApi
  })

  const handleDeleteAdvance = async (e) => {
    e.preventDefault()
    try {
      const response = await deteleAdvanceMutation({ id: advanceId?.id })
      toast.success(response?.message)
      onClose()
      queryClient.invalidateQueries(["listOfAdvances", reportData?.id]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (

    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteAdvance}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{advanceId?.title}</span>?</>
      }
      description="This action will permanently delete the advance and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteadvanceModal;
