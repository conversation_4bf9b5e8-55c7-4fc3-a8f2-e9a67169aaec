"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Calendar as CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { choicesApi, currencyList } from "@/apis/utilapi";
import { recordAdvance, updateAdvancesApi } from "@/apis/advances";
import toast from "react-hot-toast";
import { listOfReports } from "@/apis/expense-report";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { Label } from "@/components/ui/label";

export const RecordAdvanceModal = ({ reportId, isOpen, onClose, advancesData = null, mode = "create" }) => {
  const queryClient = useQueryClient();
  const [date, setDate] = useState(advancesData?.date ? new Date(advancesData.date) : null);
  const [backendErrors, setBackendErrors] = useState({});
  const [open, setOpen] = useState(false)


  const { data: listPaymentMethodQuery } = useQuery({
    queryKey: ["choice"],
    queryFn: choicesApi
  });

  const { data: currencyData } = useQuery({
    queryKey: ["listOfcurrencies"],
    queryFn: currencyList,
  });

  const { data: reportList } = useQuery({
    queryKey: ["listOfReports"],
    queryFn: () => listOfReports({ is_submitted: false }),
  });

  // create advances
  const { mutateAsync: createAdvance, isPending: isCreating } = useMutation({
    mutationFn: recordAdvance,
    onSuccess: (data) => {
      toast.success(data?.message || "Advance created successfully");
      queryClient.invalidateQueries(["listOfAdvances"]);
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      // toast.error(error?.message || "Failed to create advance");
    },
  });

  // Update mutation
  const { mutateAsync: updateAdvanceMutation, isPending: isUpdating } = useMutation({
    mutationFn: updateAdvancesApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Advance updated successfully");
      queryClient.invalidateQueries(["listOfAdvances"]);
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      // toast.error(error?.message || "Failed to update advance");
    },
  });

  const [formData, setFormData] = useState({
    title: advancesData?.title || "",
    amount: advancesData?.amount || "",
    amount_currency: advancesData?.amount_currency || "USD",
    notes: advancesData?.notes || "",
    date: advancesData?.date ? format(new Date(advancesData.date), "yyyy-MM-dd") : "",
    paid_through: advancesData?.paid_through || "",
    report: reportId || "",
  });

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    // Clear the specific error when user starts typing
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setBackendErrors({}); // Clear any existing errors

    try {
      const formattedDate = date ? format(date, "yyyy-MM-dd") : null;
      const payload = {
        ...formData,
        date: formattedDate,
      };

      if (mode === "create") {
        await createAdvance(payload);
      } else {
        await updateAdvanceMutation({ id: advancesData?.id, ...payload });
      }
    } catch (error) {
      console.error(`Error ${mode === "create" ? "creating" : "updating"} advance:`, error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[40rem] overflow-hidden rounded-md">
        <form onSubmit={handleSubmit} className="sm:p-6">
          <DialogHeader>
            <DialogTitle>{mode === "create" ? "Record Advance" : "Edit Advance"}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 pt-4">
            <div className="grid gap-6">
              <div className="relative">
                <Label htmlFor="report" className="text-xs">
                  Select Report
                </Label>

                <div>
                  <Select
                    disabled={reportId ? true : false}
                    value={formData.report}
                    onValueChange={(value) => handleChange("report", value)}
                  >
                    <SelectTrigger className="text-xs .text-gray-500">
                      <SelectValue placeholder="Select Reports" />
                    </SelectTrigger>
                    <SelectContent>
                      {reportList?.results?.map((item) => (
                        <SelectItem className="text-xs" key={item?.id} value={String(item?.id)}>
                          {item?.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <ErrorMessage errors={backendErrors} field="report" />
                </div>
              </div>

            </div>

            <div>
              <FloatingLabelInput
                id="title"
                name="title"
                label="Advance Title"
                type="text"
                value={formData.title}
                onChange={(e) => handleChange("title", e.target.value)}
                className={`${backendErrors.title && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="title" />
            </div>

            <div className="flex gap-3">
              <div className="w-1/5">
                <Select
                  value={formData?.amount_currency}
                  onValueChange={(value) => handleChange("amount_currency", value)}
                >
                  <SelectTrigger className={`${backendErrors.amount_currency && "validate_input"}`}>
                    <SelectValue placeholder="Currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencyData?.data?.map((item, index) => (
                      <SelectItem key={`${item?.code}-${index}`} value={item?.code}>
                        {item.code} - {item.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage errors={backendErrors} field="amount_currency" />
              </div>

              <div className="w-4/5">
                <FloatingLabelInput
                  id="amount"
                  name="amount"
                  label="Amount"
                  type="number"
                  value={formData.amount}
                  onChange={(e) => handleChange("amount", e.target.value)}
                  className={`${backendErrors.amount && "validate_input"}`}
                />
                <ErrorMessage errors={backendErrors} field="amount" />
              </div>
            </div>

            <div className="relative">
              <Label htmlFor="date" className="text-xs">
                Select Date
              </Label>
              <div>
                <Popover open={open} onOpenChange={setOpen}>
                  <PopoverTrigger asChild className={`${backendErrors.date && "validate_input"}`}>
                    <Button
                      variant="outline"
                      className={cn(
                        "justify-between text-left font-normal w-full text-xs",
                        !date && "text-muted-foreground"
                      )}
                    >
                      {date ? format(date, "PPP") : <span>Select Date</span>}
                      <CalendarIcon />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="flex w-auto flex-col space-y-2 p-2">
                    <div>
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={(selectedDate) => {
                          if (selectedDate) {
                            setDate(selectedDate);
                            handleChange("date", format(selectedDate, "yyyy-MM-dd"));
                          }
                          setOpen(false)
                        }}
                      />
                    </div>
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="date" />
              </div>
            </div>

            <div className="relative">
              <Label htmlFor="paid_through" className="text-xs">
                Paid Through
              </Label>
              <div>
                <Select
                  value={formData?.paid_through}
                  onValueChange={(value) => handleChange("paid_through", value)}
                  className="text-xs"
                >
                  <SelectTrigger className={`text-xs ${backendErrors.paid_through && "validate_input"}`}>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    {listPaymentMethodQuery?.data?.payment_types?.map((item, index) => (
                      <SelectItem className="text-xs" key={index} value={item?.value}>{item?.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <ErrorMessage errors={backendErrors} field="paid_through" />

              </div>
            </div>

            <div>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Write notes here"
                value={formData.notes}
                onChange={(e) => handleChange("notes", e.target.value)}
                className={`${backendErrors.notes && "validate_input"}`}
              />
              <ErrorMessage errors={backendErrors} field="notes" />
            </div>
          </div>

          <div className="flex items-center mt-4 justify-end gap-2">
            <Button
              variant="outline"
              size="xs"
              // className="h-7 gap-1 text-xs rounded-full p-3"
              onClick={onClose}
              disabled={isCreating}
            >
              Cancel
            </Button>
            <Button
              size="xs"
              type="submit"
              // className="h-7 gap-1 text-xs rounded-full p-3"
              disabled={isCreating || isUpdating}
            >
              {isCreating || isUpdating
                ? `${mode === "create" ? "Creating..." : "Updating..."}`
                : `${mode === "create" ? "Create" : "Update"}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};