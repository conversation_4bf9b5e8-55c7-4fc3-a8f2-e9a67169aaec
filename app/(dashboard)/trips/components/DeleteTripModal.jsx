import React from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import { deleteTrip } from '@/apis/trips';
import DeleteModal from '@/components/reusables/DeleteModal';


const DeleteTripModal = ({ onClose, isOpen, tripId }) => {

  const queryClient = useQueryClient()

  const { mutateAsync: deleteTripMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-trip"],
    mutationFn: deleteTrip
  })

  const handleDeleteTrip = async (e) => {
    e.preventDefault()
    try {
      const response = await deleteTripMutation({ id: tripId?.id })
      toast.success(response?.message || "Expense deleted sucessfully")
      onClose()
      queryClient.invalidateQueries(["listOfTrips"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (

    <DeleteModal
      open={isOpen}
      onClose={onClose}
      onDelete={handleDeleteTrip}
      isDeleting={isDeleting}
      title={
        <>Are you sure you want to delete <span className='font-semibold text-sm capitalize'>{tripId?.name}</span>?</>
      }
      description="This action will permanently delete the report and all its associated data. This cannot be undone."
    />
  )
}

export default DeleteTripModal;