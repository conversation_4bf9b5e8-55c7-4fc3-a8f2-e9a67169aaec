import React from 'react'
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
} from "@/components/ui/sheet"
import { formatDate, formatExpenseDate } from '@/utils/Utils'
import { StatusBadge } from '@/utils/status'

const TripDetail = ({ sheetOpen, setSheetOpen, selectedRow }) => {
  if (!selectedRow) return null;
  
  return (
    <Sheet open={sheetOpen} onOpenChange={setSheetOpen}>
      <SheetContent side="right" className="w-[95%] sm:w-full rounded-s-xl flex flex-col gap-4 overflow-scroll">
        <div className="grid gap-4 py-4 bg-bg_gray p-4 mt-2 rounded-xl">
          <SheetHeader className="text-left">
            <SheetTitle>Trip Details</SheetTitle>
          </SheetHeader>
          
          <div className='text-gray-950 text-xs font-medium leading-8'>
            <div className='border-b pb-2'>
              <span className='font-semibold text-sm'>{selectedRow.name}</span>
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Reference Number:</span> {selectedRow.reference}
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Status:</span> <StatusBadge className="!p-0" status={selectedRow.status_display} />
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Created:</span> {formatDate(selectedRow.created_at)}
            </div>
            
            {selectedRow.date && (
              <div className='flex justify-between items-center'>
                <span>Date:</span> {formatExpenseDate(selectedRow.date)}
              </div>
            )}
            
            <div className='border-t border-b my-2 py-2'>
              <span className='font-semibold'>Travel Information</span>
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Travel Type:</span> {selectedRow.travel_type_display}
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Purpose:</span> {selectedRow.purpose || "--"}
            </div>
            
            <div className='border-t border-b my-2 py-2'>
              <span className='font-semibold'>Flight Details</span>
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Ticket Type:</span> {selectedRow.flight_ticket_type_display}
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Departed From:</span> {selectedRow.flight_departed_from || "--"}
            </div>
            
            <div className='flex justify-between items-center'>
              <span>Arrived At:</span> {selectedRow.flight_arrived_at || "--"}
            </div>
            
            {selectedRow.flight_departure_time && (
              <div className='flex justify-between items-center'>
                <span>Departure Time:</span> {formatDate(selectedRow.flight_departure_time)}
              </div>
            )}
            
            {selectedRow.flight_return_time && (
              <div className='flex justify-between items-center'>
                <span>Return Time:</span> {formatDate(selectedRow.flight_return_time)}
              </div>
            )}
            
            <div className='flex justify-between items-center'>
              <span>Duration:</span> {selectedRow.flight_duration || "--"}
            </div>
            
            {selectedRow.flight_description && (
              <div className='flex justify-between items-center'>
                <span>Description:</span> {selectedRow.flight_description}
              </div>
            )}
            
            <div className='border-t border-b my-2 py-2'>
              <span className='font-semibold'>Hotel Details</span>
            </div>
            
            <div className='flex justify-between items-center'>
              <span>City:</span> {selectedRow.hotel_city || "--"}
            </div>
            
            {selectedRow.hotel_check_in && (
              <div className='flex justify-between items-center'>
                <span>Check-in:</span> {formatDate(selectedRow.hotel_check_in)}
              </div>
            )}
            
            {selectedRow.hotel_check_out && (
              <div className='flex justify-between items-center'>
                <span>Check-out:</span> {formatDate(selectedRow.hotel_check_out)}
              </div>
            )}
            
            {selectedRow.hotel_description && (
              <div className='flex justify-between items-center'>
                <span>Description:</span> {selectedRow.hotel_description}
              </div>
            )}
            
            {selectedRow.report && (
              <>
                <div className='border-t border-b my-2 py-2'>
                  <span className='font-semibold'>Report Information</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Report Name:</span> {selectedRow.report.name}
                </div>
              </>
            )}
            
            {selectedRow.approver && (
              <>
                <div className='border-t border-b my-2 py-2'>
                  <span className='font-semibold'>Approver Information</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Approver:</span> {selectedRow.approver.name}
                </div>
                <div className='flex justify-between items-center'>
                  <span>Email:</span> {selectedRow.approver.email}
                </div>
              </>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default TripDetail