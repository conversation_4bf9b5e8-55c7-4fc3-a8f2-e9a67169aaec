import React, { useState } from "react";
import {
  Dialog,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, PlaneTakeoff, Building2, Calendar } from "lucide-react";

const DraftTrip = ({ onClose, isOpen, tripData }) => {
  if (!tripData) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full sm:max-w-[80%] h-full sm:h-[90%] overflow-hidden overflow-y-auto">
        <div className="p-4">
          {/* Header with Trip ID and Status */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-bold">{tripData.id}</h2>
              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                Draft
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="xs">
                Edit
              </Button>
              <Button variant="outline" size="xs">
                Save
              </Button>
              <Button variant="outline" size="xs" className="text-red-500">
                Delete
              </Button>
              <Button variant="default" size="xs">
                Submit for Approval
              </Button>
            </div>
          </div>

          {/* Traveler Info */}
          <div className="mb-6">
            <h3 className="text-xl font-bold mb-1">{tripData.traveler}</h3>
            <p className="text-sm text-gray-600">Duration: {tripData.duration}</p>
          </div>

          {/* Itinerary Section */}
          <div className="grid grid-cols-2 gap-6">
            {/* Flight Itinerary */}
            <div className="border rounded-lg p-4">
              <h4 className="uppercase text-sm font-bold mb-4">FLIGHT ITINERARY</h4>
              
              <div className="flex items-start gap-3 mb-4">
                <div className="bg-gray-100 p-2 rounded-full mt-1">
                  <PlaneTakeoff size={16} />
                </div>
                <div>
                  <div className="text-sm font-medium">{tripData.flight.date}</div>
                  <div className="text-xs text-gray-500">Preferred time: {tripData.flight.time}</div>
                  <div className="text-xs text-gray-500">Ticket Type - {tripData.flight.type}</div>
                </div>
              </div>

              <div className="ml-9 space-y-4">
                <div>
                  <p className="text-xs text-gray-500">Departing From:</p>
                  <p className="text-sm font-medium">{tripData.flight.departFrom}</p>
                  <p className="text-xs text-gray-500">{tripData.flight.departFromDetails}</p>
                </div>
                
                <div>
                  <p className="text-xs text-gray-500">Arriving At:</p>
                  <p className="text-sm font-medium">{tripData.flight.arriveTo}</p>
                  <p className="text-xs text-gray-500">{tripData.flight.arriveToDetails}</p>
                </div>
              </div>
            </div>

            {/* Hotel Itinerary */}
            <div className="border rounded-lg p-4">
              <h4 className="uppercase text-sm font-bold mb-4">HOTEL ITINERARY</h4>
              
              <div className="flex items-start gap-3 mb-4">
                <div className="bg-gray-100 p-2 rounded-full mt-1">
                  <Building2 size={16} />
                </div>
                <div>
                  <div className="text-sm font-medium">{tripData.hotel.date}</div>
                  <div className="text-xs text-gray-500">Preferred time: {tripData.hotel.time}</div>
                  <div className="text-xs text-gray-500">Ticket Type - {tripData.hotel.type}</div>
                </div>
              </div>

              <div className="ml-9">
                <div>
                  <p className="text-xs text-gray-500">Arrival At:</p>
                  <p className="text-sm font-medium">{tripData.hotel.location}</p>
                  <p className="text-xs text-gray-500">{tripData.hotel.locationDetails}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DraftTrip;