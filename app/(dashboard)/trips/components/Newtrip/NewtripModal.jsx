import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CalendarIcon, Clock, HotelIcon, Info, PlaneTakeoff } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import DraftTrip from "../DraftTrip/DraftTrip";
import { choices<PERSON><PERSON>, onboarding<PERSON>hoices<PERSON><PERSON> } from "@/apis/utilapi";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import toast from "react-hot-toast";
import { createTripApi, updateTripApi } from "@/apis/trips";
import { useRouter } from "next/navigation";
import { SearchableSelect } from "@/components/reusables/SearchableSelect";

const NewtripModal = ({ onClose, isOpen, mode = "create", tripData }) => {
  const navigate = useRouter()
  const queryClient = useQueryClient();
  const isEditing = mode === "edit";

  // Create a single state object to manage all popover states
  const [openPopovers, setOpenPopovers] = useState({
    flight_departure_time: false,
    flight_return_time: false,
    hotel_check_in: false,
    hotel_check_out: false
  });

  // Helper function to toggle a specific popover
  const togglePopover = (name, isOpen) => {
    setOpenPopovers(prev => ({
      ...prev,
      [name]: isOpen
    }));
  };

  console.log("tripData", tripData)

  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    // staleTime: Infinity
  });

  const { data: countryList, isPending: isLoadingCountries } = useQuery({
    queryKey: ["country-list"],
    queryFn: onboardingChoicesApi,
  });

  const [tripPayload, setTripPayload] = useState(() => {
    if (isEditing && tripData) {
      return {
        name: tripData.name || "",
        travel_type: tripData.travel_type || "",
        purpose: tripData.purpose || "",
        flight_ticket_type: tripData.flight_ticket_type || "",
        flight_departed_from: tripData.flight_departed_from || "",
        flight_arrived_at: tripData.flight_arrived_at || "",
        flight_departure_time: tripData.flight_departure_time || "",
        flight_return_time: tripData.flight_return_time || "",
        flight_description: tripData.flight_description || "",
        hotel_city: tripData.hotel_city || "",
        hotel_check_in: tripData.hotel_check_in || "",
        hotel_check_out: tripData.hotel_check_out || "",
        hotel_description: tripData.hotel_description || "",
        report: tripData.report?.id || "",
        status: tripData.status || "pending",
      };
    }
    return {
      name: "",
      travel_type: "",
      purpose: "",
      flight_ticket_type: "",
      flight_departed_from: "",
      flight_arrived_at: "",
      flight_departure_time: "",
      flight_return_time: "",
      flight_description: "",
      hotel_city: "",
      hotel_check_in: "",
      hotel_check_out: "",
      hotel_description: "",
      report: "",
      status: "pending",
    };
  });

  const [backendErrors, setBackendErrors] = useState({})

  const handleChange = (key, value) => {
    setTripPayload({ ...tripPayload, [key]: value });
    // Clear the specific error when user starts typing
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["create-trip"],
    mutationFn: createTripApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Trip Created successfully");
      queryClient.invalidateQueries(["listOfTrips"]);
      onClose();
      navigate.push("/trips")
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "An Error Occured while creating trip");
    }
  });

  // Add mutation for updating
  const { mutate: updateTrip, isPending: isUpdating } = useMutation({
    mutationFn: (data) => updateTripApi(tripData?.id, data),
    onSuccess: (data) => {
      toast.success(data?.message || "Trip updated successfully");
      queryClient.invalidateQueries(["listOfTrips"]);
      onClose();
      navigate.push("/trips")
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to update trip");
    },
  });

  const handleSubmit = async (e) => {
    e.preventDefault();
    setBackendErrors({});

    const formattedPayload = {
      ...tripPayload,
      flight_departure_time: tripPayload.flight_departure_time 
        ? format(new Date(tripPayload.flight_departure_time), "yyyy-MM-dd")
        : null,
      flight_return_time: tripPayload.flight_return_time
        ? format(new Date(tripPayload.flight_return_time), "yyyy-MM-dd")
        : null,
      hotel_check_in: tripPayload.hotel_check_in
        ? format(new Date(tripPayload.hotel_check_in), "yyyy-MM-dd")
        : null,
      hotel_check_out: tripPayload.hotel_check_out
        ? format(new Date(tripPayload.hotel_check_out), "yyyy-MM-dd")
        : null,
    };

    try {
      if (isEditing) {
        await updateTrip(formattedPayload);
      } else {
        await mutateAsync(formattedPayload);
      }
    } catch (error) {
      console.error("An error occurred", error);
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="w-full min-w-[100%] px-40 .sm:max-w-[80%] h-full .sm:h-[90%] overflow-hidden overflow-y-auto">
          <form className="sm:p-6" onSubmit={handleSubmit}>
            <DialogHeader>
              <DialogTitle>{isEditing ? "Edit Trip" : "New Trip"}</DialogTitle>
            </DialogHeader>

            <div className="space-y-6 mt-4">
              {/* Trip Name */}
              <div>

                <FloatingLabelInput
                  id="tripName"
                  label="Trip Name *"
                  name="name"
                  value={tripPayload?.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  className={`mt-1 ${backendErrors.name && "validate_input"}`}

                />
                <ErrorMessage errors={backendErrors} field="name" />

              </div>

              {/* Travel Type */}
              <div>
                <Label className="text-sm font-medium">
                  Travel Type<span className="text-red-500">*</span>
                </Label>
                <RadioGroup
                  value={tripPayload.travel_type}
                  onValueChange={(value) => handleChange("travel_type", value)}
                  className="flex gap-4 mt-2"
                >
                  {choicesList?.data?.trip_travel_types?.map((item) => (
                    <Label
                      key={item.value}
                      htmlFor={item.value}
                      className={cn(
                        "cursor-pointer flex items-center gap-4 border rounded-md p-3 w-1/2 h-16",
                        tripPayload.travel_type === item.value ? "bg-gray-100 dark:bg-secondary" : "bg-secondary"
                      )}
                    >
                      <RadioGroupItem value={item.value} id={item.value} />
                      <div>
                        <div className="font-medium">{item.label}</div>
                      </div>
                    </Label>
                  ))}
                </RadioGroup>
              </div>

              {/* Business Purpose */}
              <div>
                <Label htmlFor="businessPurpose" className="text-sm font-medium">
                  Business Purpose
                </Label>
                <Textarea
                  id="purpose"
                  name="purpose"
                  value={tripPayload?.purpose}
                  onChange={(e) => handleChange("purpose", e.target.value)}
                  className={`!max-h-[34px] !min-h-[10px] w-1/2 ${backendErrors.purpose && "validate_input"}`}
                  rows="3"
                  // placeholder="Business Purpose"
                />
                <ErrorMessage errors={backendErrors} field="purpose" />

              </div>

              {/* Trip Itinerary */}
              <div>
                <h3 className="text-sm font-bold uppercase border-b pb-2">TRIP ITINERARY</h3>

                {/* Flight Section */}
                <div className="py-4">
                  <h4 className="font-medium">FLIGHT</h4>

                  <div className="w-full mt-4">

                      <div className="pb-4 border-b flex gap-2 items-center">
                        <div className="flex items-center gap-2">
                          <div className="bg-secondary p-2 rounded-full">
                            <PlaneTakeoff size={20} />
                          </div>
                        </div>

                        <RadioGroup
                          value={tripPayload.flight_ticket_type}
                          onValueChange={(value) => handleChange("flight_ticket_type", value)}
                          className="flex gap-4"
                        >
                          {choicesList?.data?.flight_ticket_types?.map((item) => (
                            <Label
                              key={item.value}
                              htmlFor={item.value}
                              className="flex items-center gap-2"
                            >
                              <RadioGroupItem value={item.value} id={item.value} />
                              <div>{item.label}</div>
                            </Label>
                          ))}
                        </RadioGroup>
                      </div>

                      <div className="grid grid-cols-4 gap-4 pt-2">

                        <div className="relative">
                          <Label htmlFor="flight_departed_from" className="text-xs">
                            Departed From <span className="text-red-500">*</span>
                          </Label>
                          <div className="relative">
                            <SearchableSelect
                              value={tripPayload.flight_departed_from}
                              onValueChange={(value) => handleChange("flight_departed_from", value)}
                              placeholder="Departed From"
                              items={countryList?.data?.countries}
                              error={backendErrors.flight_departed_from}
                              isLoading={isLoadingCountries}
                            />
                          </div>
                          <ErrorMessage errors={backendErrors} field="flight_departed_from" />
                        </div>

                        <div>
                          <Label htmlFor="arrivedAt" className="text-xs">
                            Arrived At<span className="text-red-500">*</span>
                          </Label>
                          <div className="relative">
                            <SearchableSelect
                              value={tripPayload.flight_arrived_at}
                              onValueChange={(value) => handleChange("flight_arrived_at", value)}
                              placeholder="Arrived At"
                              items={countryList?.data?.countries}
                              error={backendErrors.flight_arrived_at}
                              isLoading={isLoadingCountries}
                            />
                          </div>
                          <ErrorMessage errors={backendErrors} field="flight_arrived_at" />
                        </div>

                        <div>
                          <Label htmlFor="departureDate" className="text-xs">
                            Depart Time <span className="text-red-500">*</span>
                          </Label>
                          <div className="flex">
                            <Popover 
                              open={openPopovers.flight_departure_time}
                              onOpenChange={(open) => togglePopover('flight_departure_time', open)}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal",
                                    !tripPayload.flight_departure_time && "text-muted-foreground",
                                    backendErrors.flight_departure_time && "validate_input"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {tripPayload.flight_departure_time ? (
                                    format(new Date(tripPayload.flight_departure_time), "PPP")
                                  ) : (
                                    <span>eg: 31 Jan 2020</span>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <Calendar
                                  mode="single"
                                  selected={tripPayload.flight_departure_time ? new Date(tripPayload.flight_departure_time) : null}
                                  onSelect={(date) => {
                                    handleChange("flight_departure_time", date);
                                    togglePopover('flight_departure_time', false);
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                          <ErrorMessage errors={backendErrors} field="flight_departure_time" />
                        </div>

                        <div>
                          <Label htmlFor="returnDate" className="text-xs">
                            Return Time <span className="text-red-500">*</span>
                          </Label>
                          <div className="flex">
                            <Popover
                              open={openPopovers.flight_return_time}
                              onOpenChange={(open) => togglePopover('flight_return_time', open)}
                            >
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full justify-start text-left font-normal",
                                    !tripPayload.flight_return_time && "text-muted-foreground",
                                    backendErrors.flight_return_time && "validate_input"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {tripPayload.flight_return_time ? (
                                    format(new Date(tripPayload.flight_return_time), "PPP")
                                  ) : (
                                    <span>eg: 31 Jan 2020</span>
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0">
                                <Calendar
                                  mode="single"
                                  selected={tripPayload.flight_return_time ? new Date(tripPayload.flight_return_time) : null}
                                  onSelect={(date) => {
                                    handleChange("flight_return_time", date);
                                    togglePopover('flight_return_time', false);
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                          <ErrorMessage errors={backendErrors} field="flight_return_time" />
                        </div>

                        {/* <div>
                          <Label htmlFor="departureTime" className="text-xs">
                            Arrived Time
                          </Label>
                          <div className="flex mt-1">
                            <Input
                              type="time"
                              id="departureTime"
                              value={departureTime}
                              onChange={(e) => setDepartureTime(e.target.value)}
                              className="w-full"
                            />
                          </div>
                        </div> */}

                        <div className={"col-span-4"}>
                          <Label htmlFor="flight_description" className="text-xs">
                            Description
                          </Label>
                          <Textarea
                            id="flight_description"
                            name="flight_description"
                            // placeholder="Description"
                            value={tripPayload?.flight_description}
                            onChange={(e) => handleChange("flight_description", e.target.value)}
                            rows="3"
                            className={`!max-h-[34px] !min-h-[10px] ${backendErrors.flight_description && "validate_input"}`}

                          />
                          <ErrorMessage errors={backendErrors} field="flight_description" />

                        </div>
                      </div>
                    </div>

                </div>

                <hr className="mb-2" />

                {/* Hotel Section */}
                <div className=".mt-8">
                  <h4 className="font-medium mb-3">HOTEL</h4>

                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2 .mb-4">
                      <div className="bg-secondary p-2 rounded-full">
                        <HotelIcon size={20} />
                      </div>
                    </div>

                    <div className="grid grid-cols-6 gap-4">

                      <div>
                        <Label htmlFor="hotel_city" className="text-xs">
                          Hotel City
                        </Label>

                        <FloatingLabelInput
                          id="hotel_city"
                          label="Hotel city *"
                          name="name"
                          value={tripPayload.hotel_city}
                          onChange={(e) => handleChange("hotel_city", e.target.value)}
                          className={`${backendErrors.hotel_city && "validate_input"}`}
                        />
                        <ErrorMessage errors={backendErrors} field="hotel_city" />

                      </div>

                      <div>
                        <Label htmlFor="checkInDate" className="text-xs">
                          Check In<span className="text-red-500">*</span>
                        </Label>
                        <div className="flex">
                          <Popover
                            open={openPopovers.hotel_check_in}
                            onOpenChange={(open) => togglePopover('hotel_check_in', open)}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !tripPayload.hotel_check_in && "text-muted-foreground",
                                  backendErrors.hotel_check_in && "validate_input"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {tripPayload.hotel_check_in ? (
                                  format(new Date(tripPayload.hotel_check_in), "PPP")
                                ) : (
                                  <span>eg: 31 Jan 2020</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={tripPayload.hotel_check_in ? new Date(tripPayload.hotel_check_in) : null}
                                onSelect={(date) => {
                                  handleChange("hotel_check_in", date);
                                  togglePopover('hotel_check_in', false);
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                        <ErrorMessage errors={backendErrors} field="hotel_check_in" />
                      </div>

                      <div>
                        <Label htmlFor="checkOutDate" className="text-xs">
                          Check Out<span className="text-red-500">*</span>
                        </Label>
                        <div className="flex">
                          <Popover
                            open={openPopovers.hotel_check_out}
                            onOpenChange={(open) => togglePopover('hotel_check_out', open)}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !tripPayload.hotel_check_out && "text-muted-foreground",
                                  backendErrors.hotel_check_out && "validate_input"
                                )}
                              >
                                <CalendarIcon className="mr-2 h-4 w-4" />
                                {tripPayload.hotel_check_out ? (
                                  format(new Date(tripPayload.hotel_check_out), "PPP")
                                ) : (
                                  <span>eg: 31 Jan 2020</span>
                                )}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0">
                              <Calendar
                                mode="single"
                                selected={tripPayload.hotel_check_out ? new Date(tripPayload.hotel_check_out) : null}
                                onSelect={(date) => {
                                  handleChange("hotel_check_out", date);
                                  togglePopover('hotel_check_out', false);
                                }}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                        <ErrorMessage errors={backendErrors} field="hotel_check_out" />
                      </div>

                      <div className={"col-span-3"}>
                        <Label htmlFor="hotel_description" className="text-xs">
                          Description
                        </Label>
                        <Textarea
                          id="hotel_description"
                          name="hotel_description"
                          // placeholder="Description"
                          value={tripPayload?.hotel_description}
                          onChange={(e) => handleChange("hotel_description", e.target.value)}
                          className={`!max-h-[34px] !min-h-[10px] ${backendErrors.hotel_description && "validate_input"}`}
                        />
                        <ErrorMessage errors={backendErrors} field="hotel_description" />

                      </div>

                    </div>
                  </div>
                </div>

              </div>
            </div>

            <div className="flex items-center mt-8 justify-end gap-2">
              <DialogClose asChild>
                <Button size="xs" variant="outline" type="button"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button 
                size="xs" 
                type="submit"
                disabled={isEditing ? isUpdating : isPending}
              >
                {isEditing 
                  ? (isUpdating ? "Updating..." : "Update Trip")
                  : (isPending ? "Creating..." : "Create Trip")}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
      {/* 
      {showDraftModal && (
        <DraftTrip
          isOpen={showDraftModal}
          onClose={() => setShowDraftModal(false)}
          tripData={tripData}
        />
      )} */}

    </>

  );
};

export default NewtripModal;
