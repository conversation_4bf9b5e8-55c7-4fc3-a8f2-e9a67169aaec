
"use client"
import { But<PERSON> } from '@/components/ui/button';
import { SquarePen } from 'lucide-react';
import React, { useState } from 'react'
import NewtripModal from './components/Newtrip/NewtripModal';

const Triplayout = ({ children }) => {

  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>

      <div className="overflow-x-hidden grid auto-rows-max">
        <div className=".mb-4 p-4 lg:p-4 border-b">
          <div className="flex flex-row items-center justify-between w-full gap-4">

            <div>
              <h3 className="text-sm font-semibold">Trips</h3>
            </div>


            <div className="flex items-center gap-3">

              <div>
                <Button
                  size="xs"
                  // className={`gap-1 rounded-full p-3`}
                  onClick={() => setIsModalOpen(true)}
                >
                  <SquarePen />
                  <span>New Trip</span>
                </Button>
              </div>

            </div>

          </div>

        </div>

        {isModalOpen && (
          <NewtripModal
            onClose={() => setIsModalOpen(false)}
            isOpen={() => setIsModalOpen(true)}
          />
        )}

        <main className="overflow-x-scroll">{children}</main>
      </div>

    </>
  )
}

export default Triplayout;
