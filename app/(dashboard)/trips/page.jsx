"use client";

import React, { Suspense, useCallback, useEffect, useMemo, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { Eye, EllipsisVertical, Plus, SquarePen } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { listOfAdvances, exportAdvanceCSVApi } from '@/apis/advances'
// import Advancesdetail from './Advancesdetail'
import { formatDate, formatExpenseDate } from '@/utils/Utils'
import { Checkbox } from '@/components/ui/checkbox'
// import { RecordAdvanceModal } from './RecordAdvanceModal'
// import DeleteadvanceModal from './DeleteadvanceModal'
import { useExportCSV } from '@/hooks/useExportCSV'
import { choicesApi } from '@/apis/utilapi'
import useSort from '@/hooks/useSort'
import useTableFilter from '@/hooks/useTableFilter'
import FilterWrapper from '@/components/reusables/FilterWrapper'
import { useSearchParams } from 'next/navigation'
import TabWrapper from '@/components/reusables/TabWrapper';
import { exportTripCSVApi, listOfTrips } from '@/apis/trips';
import DeleteTripModal from './components/DeleteTripModal';
import { StatusBadge } from '@/utils/status';
import TripDetail from './components/TripDetail';
import NewtripModal from './components/Newtrip/NewtripModal';

const TripsContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    title: "",
    has_report: "",
    paid_through: "",
    minAmount: "",
    maxAmount: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices data
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch trips data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfTrips", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max",
            dateRange: "date"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'pending') {
        apiParams.is_submitted = false; // Only show trips without reports
      }

      return listOfTrips(apiParams);
    },
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true
  });

  // Initialize useSort with memoized initial data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(useMemo(() => apiResponse?.results || [], [apiResponse?.results]));

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Export CSV functionality
  const { handleExport } = useExportCSV(exportTripCSVApi, { filename: 'trips.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Filter options configuration
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'title',
        label: 'Filter by title',
        type: 'text'
      },
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'paid_through',
        placeholder: 'Paid Through',
        options: choicesList?.data?.payment_types || [],
        label: "Paid Through",
      }
    ],
    showAmountFilter: true,
    showDateFilter: true
  }), [choicesList?.data?.payment_types]);

  // Get counts for tabs
  const getCounts = useMemo(() => {
    if (!apiResponse?.results) return { all: 0, pending: 0 };

    return {
      all: apiResponse.count || 0,
      pending: apiResponse.results.filter(trip => !trip.report).length
    };
  }, [apiResponse?.results, apiResponse?.count]);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData?.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "reference",
      header: "Reference Number",
      cell: ({ row }) => row.original.reference,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "name",
      header: "Report Name",
      cell: ({ row }) => row.original.name,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "travel_type_display",
      header: "Travel Type",
      cell: ({ row }) => row.original.travel_type_display,
      sortable: true,
      unhidable: true,
    },

    {
      accessorKey: "flight_ticket_type_display",
      header: "Flight Ticket Type",
      cell: ({ row }) => row.original.flight_ticket_type_display,
      sortable: true,
      unhidable: true,
    },

    {
      accessorKey: "flight_departed_from",
      header: "Departed From",
      cell: ({ row }) => row.original.flight_departed_from_display,
      sortable: true,
      unhidable: true,
    },

    {
      accessorKey: "flight_arrived_at",
      header: "Arrived At",
      cell: ({ row }) => row.original.flight_arrived_at_display,
      sortable: true,
      unhidable: true,
    },

    {
      accessorKey: "flight_duration",
      header: "Duration",
      cell: ({ row }) => row.original.flight_duration,
      sortable: true,
    },

    {
      accessorKey: "hotel_city",
      header: "Hotel City",
      cell: ({ row }) => row.original.hotel_city,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "status_display",
      header: "Status",
      cell: ({ row }) => (
        <StatusBadge status={row.original.status_display} />
      ),
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "report.name",
      header: "Report Name",
      cell: ({ row }) => row.original.report?.name || "--",
      sortable: true,
      unhidable: true
    },
    {
      accessorKey: "date",
      header: "Date",
      cell: ({ row }) => formatExpenseDate(row.original.date),
      sortable: true,
    },
    {
      accessorKey: "created_at",
      header: "Created",
      cell: ({ row }) => formatDate(row.original.created_at),
      sortable: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost" onClick={(e) => e.stopPropagation()}>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsEditModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsDeleteModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      sticky: true,
      unhidable: true
    }
  ], [selectedRows, sortedData, handleSelectAll]);

  const [createTrip, setCreateTrip] = useState(false)

  return (
    <>
      <div className="overflow-x-scroll w-full">
        <div className="no-scrollbar">
          <TableComponent
            rows={sortedData}
            columns={columns}
            tableTitle={
              <TabWrapper
                tabs={[
                  { value: 'all', label: 'All', count: getCounts.all },
                  { value: 'pending', label: 'Pending', count: getCounts.pending },
                ]}
                defaultTab="all"
              />
            }
            onRowClick={(row) => {
              setSelectedRow(row);
              setSheetOpen(true);
            }}
            isLoading={isLoading}
            onSort={handleSort}
            sortState={sortState}
            showImportExport={selectedRows.length > 0}
            exportToCSV={handleExportCSV}
            createTitle={<Button onClick={() => setCreateTrip(true)} size="xs"> <SquarePen /> Create Trip </Button>}
            NoavailableTitle={isLoading ? "Loading..." : apiResponse?.count <= 0 ? "Trip" : ""}
            tableDescription={
              "Plan and manage business trips, track travel expenses, and maintain detailed journey records for compliance and reimbursement purposes."
            }
            filterComponents={
              <FilterWrapper
                filterValues={filters}
                onFilterApply={handleFilterApply}
                onFilterClear={handleFilterClear}
                filterOptions={filterOptions}
              />
            }
          />
        </div>
      </div>

      <TripDetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedRow}
      />

      {isDeleteModalOpen && selectedRow && (
        <DeleteTripModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          tripId={selectedRow}
        />
      )}

      {createTrip && (
        <NewtripModal
          isOpen={createTrip}
          onClose={() => setCreateTrip(false)}
          mode="create"
        />
      )}

      {isEditModalOpen && selectedRow && (
        <NewtripModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedRow(null);
          }}
          mode="edit"
          tripData={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TripsContent />
    </Suspense>
  );
}

export default Page;
