"use client"

import React, { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { analyticsOverview, analyticsExpenseTrends } from '@/apis/analytics';
import { useQuery } from '@tanstack/react-query';
import { CircleCheckBig, CreditCard, DollarSign, SquareCheckBig, CalendarIcon, Download, FileSpreadsheet, FileText } from 'lucide-react';
import AllreportsTable from '../reports/components/Allreports/AllreportsTable';
import { AnalyticsChart } from "./components/AnalyticsChart";
import ExportFilter from './components/ExportFilter';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

const Page = () => {
  const [timeRange, setTimeRange] = useState("this-year");
  const [expenseTrendTimeRange, setExpenseTrendTimeRange] = useState("week");

  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["analytics-overview", timeRange],
    queryFn: () => analyticsOverview({ range: timeRange }),
  });

  const { data: trendsResponse, isLoading: isLoadingExpenseTrend } = useQuery({
    queryKey: ["analytics-trends", expenseTrendTimeRange],
    queryFn: () => analyticsExpenseTrends({ range: expenseTrendTimeRange }),
  });

  // Transform data for bar chart
  const barChartData = apiResponse?.data?.charts?.department_expenses_chart_data
    ?.map((item) => ({
      name: item.date,
      value: item.departments.reduce((sum, dept) => sum + dept.total_expenses, 0),
    })) || [];

  // Transform data for pie chart - assuming you have department totals
  const pieChartData = apiResponse?.data?.charts?.expense_distribution_chart_data
    .map((dept) => ({
      name: dept.name,
      value: dept.total_expenses,
      amount_display: dept.total_expenses_display,
    })) || [];

  // Transform data for area chart
  const areaChartData = trendsResponse?.data?.map((item) => ({
    name: item.date,
    value: item.total_amount,
  })) || [];

  // Dummy data for Area Chart (Expense Trends) - More data points
  // const dummyAreaChartData = [
  //   { name: '2025-06-02', value: 4000 },
  //   { name: '2025-06-03', value: 3000 },
  //   { name: '2025-06-04', value: 5000 },
  //   { name: '2025-06-05', value: 2780 },
  //   { name: '2025-06-06', value: 1890 },
  //   { name: '2025-06-07', value: 2390 },
  //   { name: '2025-06-08', value: 3490 },
  //   { name: '2025-06-09', value: 4200 },
  //   { name: '2025-06-10', value: 3800 },
  //   { name: '2025-06-11', value: 4100 },
  //   { name: '2025-06-12', value: 3700 },
  //   { name: '2025-06-13', value: 4500 }
  // ];

  // Dummy data for Pie Chart (Expense Distribution) - More categories
  // const dummyPieChartData = [
  //   { name: 'Travel', value: 30 },
  //   { name: 'Office Supplies', value: 20 },
  //   { name: 'Entertainment', value: 15 },
  //   { name: 'Equipment', value: 12 },
  //   { name: 'Software', value: 8 },
  //   { name: 'Training', value: 7 },
  // ];

  // Dummy data for Bar Chart (Monthly Expenses by Department) - More departments
  // const dummyBarChartData = [
  //   { name: 'Engineering', value: 12000 },
  //   { name: 'Marketing', value: 8000 },
  //   { name: 'Sales', value: 15000 },
  //   { name: 'HR', value: 5000 },
  //   { name: 'Finance', value: 7000 },
  //   { name: 'Operations', value: 9000 },
  //   { name: 'Customer Support', value: 6500 },
  //   { name: 'Research', value: 11000 }
  // ];

  return (
    <div>

      <div className="p-4 px-4 border-b">
        <div className="flex flex-col sm:flex-row flex-wrap items-start sm:items-center justify-between w-full gap-4">

          <div>
            <h3 className="text-lg font-bold ">Analytics Dashboard</h3>
            <p className="text-xs">Track and analyze your organization&apos;s expenses</p>
          </div>

          <div className='flex gap-2 items-center '>
            <div>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[140px] text-xs rounded-[10px] h-[36px]" aria-label="Select a value">
                  <CalendarIcon size={12} />
                  <SelectValue placeholder="Select a range" />
                </SelectTrigger>
                <SelectContent className="rounded-[10px] text-xs">
                  <SelectItem value="this-year" className="rounded-lg text-xs">
                    This Year
                  </SelectItem>
                  <SelectItem value="last-quarter" className="rounded-lg text-xs">
                    Last Quarter
                  </SelectItem>
                  <SelectItem value="last-month" className="rounded-lg text-xs">
                    Last Month
                  </SelectItem>
                  <SelectItem value="this-month" className="rounded-lg text-xs">
                    This Month
                  </SelectItem>
                  <SelectItem value="custom" className="rounded-lg text-xs">
                    Custom
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button size="xs" variant="outline">
              <FileSpreadsheet />
              CSV
            </Button>
            <Button size="xs" variant="outline">
              <FileText />
              PDF
            </Button>
            <Button size="xs" variant="outline">
              <Download />
              Excel
            </Button>
          </div>

        </div>
      </div>

      <div className="p-4 grid grid-cols-1 md:grid-cols-4 gap-3">
        <Card className="flex items-center justify-between pr-4">
          <CardHeader cclassName="pb-2">
            <CardDescription>Total of Expenses</CardDescription>
            <CardTitle className="text-xl font-bold">{apiResponse?.data?.expenses?.total_amount_display || 0}</CardTitle>
          </CardHeader>

          <div className='p-2 rounded-full bg-secondary'>
            <DollarSign size={18} />
          </div>

        </Card>

        <Card className="flex items-center justify-between pr-4">
          <CardHeader cclassName="pb-2">
            <CardDescription>Pending Approvals</CardDescription>
            <CardTitle className="text-xl font-bold">{apiResponse?.data?.reports?.pending_approval_count || 0}</CardTitle>
          </CardHeader>

          <div className='p-2 rounded-full bg-secondary'>
            <SquareCheckBig size={18} />
          </div>

        </Card>

        <Card className="flex items-center justify-between pr-4">
          <CardHeader cclassName="pb-2">
            <CardDescription>Approved Reports</CardDescription>
            <CardTitle className="text-xl font-bold">{apiResponse?.data?.reports?.approved_count || 0}</CardTitle>
          </CardHeader>

          <div className='p-2 rounded-full bg-secondary'>
            <CircleCheckBig size={18} />
          </div>

        </Card>

        <Card className="flex items-center justify-between pr-4">
          <CardHeader cclassName="pb-2">
            <CardDescription>Reimbursements</CardDescription>
            <CardTitle className="text-xl font-bold">{apiResponse?.data?.reports?.reimbursement_amount || 0}</CardTitle>
          </CardHeader>

          <div className='p-2 rounded-full bg-secondary'>
            <CreditCard size={18} />
          </div>
        </Card>
      </div>


      <div className="p-4 grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card className="space-0 p-0 md:col-span-4">
          <CardHeader className="py-2 px-2">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-sm">Monthly Expenses by Department</CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-0 p-0">
            <div className="h-[300px]">
              <AnalyticsChart type="bar" data={barChartData} isLoading={isLoading} />
            </div>
          </CardContent>
        </Card>

        <Card className="space-0 p-0 md:col-span-2">
          <CardHeader className="py-2 px-2">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-sm">Expense Distribution</CardTitle>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-0 p-0">
            <div className="h-[300px]">
              <AnalyticsChart type="pie" data={pieChartData} isLoading={isLoading} />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="p-4">
        <Card className="space-0 p-0 md:col-span-3">
          <CardHeader className="py-2 px-2">
            <div className=".flex .items-start .justify-between">
              <div className='flex items-center !justify-between'>
                <CardTitle className="text-sm">Expense Trends Over Time</CardTitle>
                <div className="text-[10px] sm:text-xs text-gray-600 font-semibold rounded-lg p-1 flex items-center gap-2 sm:gap-1 bg-[#F4F4F5] dark:bg-secondary">
                  <div
                    onClick={() => setExpenseTrendTimeRange("week")}
                    className={`px-4 p-2 rounded-md cursor-pointer transition-colors ${expenseTrendTimeRange === "week"
                        ? "bg-white dark:bg-background text-gray-950 dark:text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-950 dark:hover:text-white"
                      }`}
                  >
                    Week
                  </div>
                  <div
                    onClick={() => setExpenseTrendTimeRange("month")}
                    className={`px-4 p-2 rounded-md cursor-pointer transition-colors ${expenseTrendTimeRange === "month"
                        ? "bg-white dark:bg-background text-gray-950 dark:text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-950 dark:hover:text-white"
                      }`}
                  >
                    Month
                  </div>
                  <div
                    onClick={() => setExpenseTrendTimeRange("year")}
                    className={`px-4 p-2 rounded-md cursor-pointer transition-colors ${expenseTrendTimeRange === "year"
                        ? "bg-white dark:bg-background text-gray-950 dark:text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-400 hover:text-gray-950 dark:hover:text-white"
                      }`}
                  >
                    Year
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-0 p-0">
            <div className="h-[300px]">
              <AnalyticsChart
                type="area"
                data={areaChartData}
                isLoading={isLoadingExpenseTrend}

              />
            </div>
          </CardContent>
        </Card>
      </div>


      <div className="p-4 grid grid-cols-1 md:grid-cols-6 gap-4">
        <div className='md:col-span-4 rounded-md border'>
          <AllreportsTable tableTitle="Reports" />
        </div>

        <div className='md:col-span-2'>
          <ExportFilter />
        </div>

      </div>

    </div>
  )
}

export default Page;
