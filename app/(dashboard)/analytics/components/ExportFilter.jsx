import { choices<PERSON><PERSON> } from "@/apis/utilapi"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { useQuery } from "@tanstack/react-query"
import { Download, Filter } from "lucide-react"

const ExportFilter = () => {

  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  return (
    <Card className="p-4">
      <CardHeader className="px-0 pt-0">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">Export & Filter</CardTitle>
        </div>
      </CardHeader>

      <div className="space-y-6">
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Department</h3>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="All Departments" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {/* Add department options here */}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Status</h3>
          <Select>
            <SelectTrigger className="border-primary">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {/* Add status options here */}

              {choicesList?.data?.approval_status?.map((item) => (
                <SelectItem
                  className="text-xs"
                  key={item.id || item.value}
                  value={String(item.id || item.value)}
                >
                  {item.name || item.label}
                </SelectItem>
              ))}

            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Amount Range</h3>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>$0</span>
              <span>$5,000</span>
            </div>
            <Slider
              defaultValue={[5000]}
              max={5000}
              step={100}
              className="w-full"
            />
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="text-sm font-medium">Report Type</h3>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="All Types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {/* Add type options here */}

              {choicesList?.data?.report_types?.map((item) => (
                <SelectItem
                  className="text-xs"
                  key={item.id || item.value}
                  value={String(item.id || item.value)}
                >
                  {item.name || item.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex gap-2 pt-4">
          <Button className="flex-1" size="xs" variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Apply Filters
          </Button>
          <Button size="xs" className="flex-1">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>
    </Card>
  )
}

export default ExportFilter