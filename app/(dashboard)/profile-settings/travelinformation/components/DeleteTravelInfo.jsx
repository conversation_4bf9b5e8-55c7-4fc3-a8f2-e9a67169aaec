import React from 'react'
import {
  <PERSON><PERSON>,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Trash, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { deleteAdvancesApi } from '@/apis/advances';
import toast from 'react-hot-toast';
import { deleteExpense } from '@/apis/expenses';
import ButtonLoader from '@/utils/spinner/ButtonLoader';
import { deleteTrip } from '@/apis/trips';
import { deleteTravelInfo } from '@/apis/profile-management';


const DeleteTravelInfo = ({ onClose, isOpen, travelId }) => {
  
  const queryClient = useQueryClient()

  const { mutateAsync: deleteTravelMutation, isPending: isDeleting } = useMutation({
    mutationKey: ["delete-travel-info"],
    mutationFn: deleteTravelInfo
  })

  const handleDeleteTravelInfo = async (e) => {
    e.preventDefault()
    try {
      const response = await deleteTravelMutation({ id: travelId?.id })
      toast.success(response?.message || "Travel Info deleted sucessfully")
      onClose()
      queryClient.invalidateQueries(["listOfTravelDocument"]);
    } catch (error) {
      toast.error(error?.message)
      console.log(error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
      <DialogHeader className={"flex items-center justify-center max-w-[70%] text-center mx-auto"}>
          <DialogTitle className="text-xs font-normal">Are you sure you want to delete <span className='font-semibold'>{travelId?.document_type_display}</span> document ?</DialogTitle>
        </DialogHeader>


        <div className="flex items-center justify-center sm:mt-10 gap-2">
          <DialogClose asChild>
            <Button variant="outline"
            size="xs"
            >Cancel</Button>
          </DialogClose>

          <Button
            onClick={handleDeleteTravelInfo}
            disabled={isDeleting}
            size="xs"
          >
            {isDeleting ? (
              <span className='flex items-center gap-2'><ButtonLoader /> Deleting...</span>
            ) : (
              <>
                <Trash2 size={10}/> Delete
              </> 
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteTravelInfo;