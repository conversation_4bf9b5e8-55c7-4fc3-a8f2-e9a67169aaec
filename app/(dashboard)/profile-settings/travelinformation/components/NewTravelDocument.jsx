import { choices<PERSON><PERSON>, onboarding<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/apis/utilapi";
import { createTravelDocument, updateTravelDocument } from "@/apis/profile-management";
import { SearchableSelect } from "@/components/reusables/SearchableSelect";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { Label } from "@/components/ui/label";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { useQuery } from "@tanstack/react-query";
import { useState, useRef, useEffect } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Upload } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

const NewTravelDocument = ({ isOpen, onClose, mode = "create", documentData }) => {
    const [openIssue, setOpenIssue] = useState(false)    
    const [openExpiry, setOpenExpiry] = useState(false)    

  const queryClient = useQueryClient();
  const fileInputRef = useRef(null);
  const isEditing = mode === "update";

  // Fetch data
  const { data: countriesData } = useQuery({
    queryKey: ["countries-list"],
    queryFn: onboardingChoicesApi,
    enabled: isOpen
  });

  const { data: documentTypeQuery } = useQuery({
    queryKey: ["choices-list"],
    queryFn: choicesApi,
    enabled: isOpen
  });

  // Form state
  const [formData, setFormData] = useState({
    type: "",
    country: "",
    document_number: "",
    issued_on: "",
    expires_on: "",
    file: null
  });

  // Set initial form data when editing
  useEffect(() => {
    if (isEditing && documentData) {
      setFormData({
        type: documentData.type || "",
        country: documentData.country || "",
        document_number: documentData.document_number || "",
        issued_on: documentData.issued_on || "",
        expires_on: documentData.expires_on || "",
        file: null // Reset file on edit mode
      });
    }
  }, [isEditing, documentData]);

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const handleDateChange = (key, date) => {
    if (date) {
      const formattedDate = format(date, "yyyy-MM-dd");
      setFormData({ ...formData, [key]: formattedDate });
      setBackendErrors({ ...backendErrors, [key]: null });
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        toast.error("File size should not exceed 10MB");
        return;
      }
      setFormData({ ...formData, file });
      setBackendErrors({ ...backendErrors, file: null });
    }
  };

  const { mutate: submitTravelDocument, isPending: isSubmitting } = useMutation({
    mutationFn: async (data) => {
      const formDataToSend = new FormData();
      Object.keys(data).forEach(key => {
        if (key === 'file' && data[key]) {
          formDataToSend.append('file', data[key]);
        } else if (data[key]) {
          formDataToSend.append(key, data[key]);
        }
      });

      return isEditing
        ? updateTravelDocument(documentData.id, formDataToSend)
        : createTravelDocument(formDataToSend);
    },
    onSuccess: (data) => {
      toast.success(data?.message || `Travel document ${isEditing ? 'updated' : 'added'} successfully`);
      queryClient.invalidateQueries(["listOfTravelDocument"]);
      onClose();
    },
    onError: (error) => {
      toast.error(error?.message || `Failed to ${isEditing ? 'update' : 'add'} travel document`);
      if (error?.details) {
        setBackendErrors(error.details);
      }
    }
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    submitTravelDocument(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[40rem] overflow-hidden">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>New Travel Document</DialogTitle>
          </DialogHeader>

          <div className="my-4 space-y-5">
            <div>
              <Label htmlFor="type" className="text-xs font-medium mb-1 block">Select Document</Label>
              <SearchableSelect
                value={formData.type}
                onValueChange={(value) => handleChange("type", value)}
                placeholder="Select Document"
                searchPlaceholder="Search document..."
                items={documentTypeQuery?.data?.travel_document_types}
                error={backendErrors.type}
              />
              <ErrorMessage errors={backendErrors} field="type" />
            </div>

            <div>
              <Label htmlFor="country" className="text-xs font-medium mb-1 block">Country</Label>
              <SearchableSelect
                value={formData.country}
                onValueChange={(value) => handleChange("country", value)}
                placeholder="Select Country"
                searchPlaceholder="Search countries..."
                items={countriesData?.data?.countries}
                error={backendErrors.country}
              />
              <ErrorMessage errors={backendErrors} field="country" />
            </div>

            <div>
              <FloatingLabelInput
                id="document_number"
                name="document_number"
                label="Document Number"
                value={formData.document_number}
                onChange={(e) => handleChange("document_number", e.target.value)}
                className={`${backendErrors.document_number ? "validate_input" : ""}`}
              />
              <ErrorMessage errors={backendErrors} field="document_number" />
            </div>

            {/* Date fields side by side */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="issued_on" className="text-xs font-medium mb-1 block">Issue Date</Label>
                <Popover open={openIssue} onOpenChange={setOpenIssue}>
                  <PopoverTrigger asChild>
                    <Button
                      id="issued_on"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.issued_on && "text-muted-foreground",
                        backendErrors.issued_on && "validate_input"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.issued_on ? (
                        format(new Date(formData.issued_on), "PPP")
                      ) : (
                        <span>Select issue date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.issued_on ? new Date(formData.issued_on) : undefined}
                      onSelect={(date) => { handleDateChange("issued_on", date); setOpenIssue(false); }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="issued_on" />
              </div>

              <div>
                <Label htmlFor="expires_on" className="text-xs font-medium mb-1 block">Expiry Date</Label>
              <Popover open={openExpiry} onOpenChange={setOpenExpiry}>
                  <PopoverTrigger asChild>
                    <Button
                      id="expires_on"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.expires_on && "text-muted-foreground",
                        backendErrors.expires_on && "validate_input"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.expires_on ? (
                        format(new Date(formData.expires_on), "PPP")
                      ) : (
                        <span>Select expiry date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.expires_on ? new Date(formData.expires_on) : undefined}
                      onSelect={(date) => {handleDateChange("expires_on", date); setOpenExpiry(false);}}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="expires_on" />
              </div>
            </div>

            {/* File upload section */}
            <div>
              <Label htmlFor="file" className="text-xs font-medium mb-1 block">Document File</Label>
              <div
                className={`border-2 border-dashed rounded-md p-4 text-center cursor-pointer hover:bg-gray-50 transition-colors ${backendErrors.file ? "border-red-500" : "border-gray-300"}`}
                onClick={() => fileInputRef.current.click()}
              >
                <input
                  type="file"
                  id="file"
                  ref={fileInputRef}
                  className="hidden"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                />

                <div className="flex flex-col items-center justify-center gap-2">
                  <Upload className="h-8 w-8 text-gray-400" />
                  {formData.file ? (
                    <div className="text-sm font-medium text-gray-700">
                      {formData.file.name}
                    </div>
                  ) : (
                    <>
                      <p className="text-sm font-medium">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">
                        PDF, JPG, JPEG or PNG (max. 10MB)
                      </p>
                    </>
                  )}
                </div>
              </div>
              <ErrorMessage errors={backendErrors} field="file" />
            </div>
          </div>

          <div className="flex items-center mt-6 justify-end gap-2">
            <DialogClose asChild>
              <Button size="xs" variant="outline" type="button">Cancel</Button>
            </DialogClose>
            <Button
              size="xs"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default NewTravelDocument;
