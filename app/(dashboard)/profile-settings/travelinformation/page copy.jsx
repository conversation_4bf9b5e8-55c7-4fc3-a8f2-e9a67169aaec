"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ch<PERSON>ronLeft, CircleArrowUp, Plane, Users } from "lucide-react";
import React, { useState } from "react";
import NewTravelDocument from "./components/NewTravelDocument";

const Page = () => {

  const [openTravelInfo, setOpenTravelInfo] = useState(false)

  return (
    <>
      <div className="min-h-screen">

        <div className="px-5 pb-4">
          <h1 className="text-sm font-bold">Travel Information</h1>
        </div>
        <hr className="border-gray-200" />

        <div className="p-10 rounded-lg">

          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Travel Documents</h3>
              <p className="text-xs text-gray-600 mt-1 w-5/6">
              Travel documents added here can be accessed by the travel team to facilitate travel booking              </p>
            </div>
            <Button
              className="profileButton"
              variant="outline"
              size="xs"
              onClick={()=>setOpenTravelInfo(true)}
            >
              <Plane strokeWidth={2}/>
              Add Travel Documents
            </Button>
          </div>

          <hr className="border-gray-200 my-8" />

        </div>
      </div>

      {openTravelInfo && (
        <NewTravelDocument
          isOpen={openTravelInfo}
          onClose={()=>setOpenTravelInfo(false)}
        />
      )}
    </>
  );
};

export default Page;