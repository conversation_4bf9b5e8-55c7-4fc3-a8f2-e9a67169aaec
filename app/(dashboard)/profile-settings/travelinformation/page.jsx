"use client";

import React, { Suspense, useCallback, useEffect, useMemo, useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { TableComponent } from '@/components/reusables/table'
import { Eye, EllipsisVertical, Plus, SquarePen } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { useExportCSV } from '@/hooks/useExportCSV'
import { choicesApi } from '@/apis/utilapi'
import useSort from '@/hooks/useSort'
import useTableFilter from '@/hooks/useTableFilter'
import FilterWrapper from '@/components/reusables/FilterWrapper'
import { useSearchParams } from 'next/navigation'
import TabWrapper from '@/components/reusables/TabWrapper';
// import TripDetail from './components/TripDetail';
import DeleteTravelInfo from './components/DeleteTravelInfo';
import NewTravelDocument from './components/NewTravelDocument';
import { exportTravelCSVApi, getTravelDocumentApi } from '@/apis/profile-management';

const TravelDocumentContent = () => {
  const searchParams = useSearchParams();
  const currentTab = searchParams.get('tab') || 'all';
  const queryClient = useQueryClient();

  // Initialize filters
  const initialFilters = useMemo(() => ({
    reference: "",
    title: "",
    has_report: "",
    paid_through: "",
    minAmount: "",
    maxAmount: "",
    dateRange: null
  }), []);

  const {
    filters,
    handleFilter: handleFilterApply,
    clearFilters: handleFilterClear
  } = useTableFilter(initialFilters, {
    syncWithUrl: true
  });

  // Fetch choices data
  const { data: choicesList } = useQuery({
    queryKey: ["choices"],
    queryFn: choicesApi,
    staleTime: Infinity
  });

  // Fetch trips data
  const { data: apiResponse, isLoading } = useQuery({
    queryKey: ["listOfTravelDocument", filters, currentTab],
    queryFn: () => {
      const apiParams = {};

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (!value || value === "all") return;

          const paramMap = {
            minAmount: "amount_min",
            maxAmount: "amount_max",
            dateRange: "date"
          };

          apiParams[paramMap[key] || key] = value;
        });

        if (filters.dateRange?.from && filters.dateRange?.to) {
          apiParams.date_from = filters.dateRange.from;
          apiParams.date_to = filters.dateRange.to;
        }
      }

      // Add tab-specific filtering
      if (currentTab === 'pending') {
        apiParams.is_submitted = false; // Only show trips without reports
      }

      return getTravelDocumentApi(apiParams);
    },
    staleTime: 1000 * 60 * 5,
    keepPreviousData: true
  });

  // Initialize useSort with memoized initial data
  const {
    sortedData,
    sortState,
    handleSort
  } = useSort(useMemo(() => apiResponse?.results || [], [apiResponse?.results]));

  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRow, setSelectedRow] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [sheetOpen, setSheetOpen] = useState(false);

  // Export CSV functionality
  const { handleExport } = useExportCSV(exportTravelCSVApi, { filename: 'travelinfo.csv' });
  const handleExportCSV = () => handleExport(selectedRows.length ? { ids: selectedRows } : {});

  const handleSelectAll = useCallback((checked) => {
    setSelectedRows(checked ? sortedData.map(r => r.id) : []);
  }, [sortedData]);

  // Reset selected rows when filters change
  useEffect(() => {
    setSelectedRows([]);
  }, [filters]);

  // Filter options configuration
  const filterOptions = useMemo(() => ({
    inputFilters: [
      {
        key: 'title',
        label: 'Filter by title',
        type: 'text'
      },
      {
        key: 'reference',
        label: 'Filter by reference',
        type: 'text'
      },
    ],
    selectFilters: [
      {
        key: 'paid_through',
        placeholder: 'Paid Through',
        options: choicesList?.data?.payment_types || [],
        label: "Paid Through",
      }
    ],
    showAmountFilter: true,
    showDateFilter: true
  }), [choicesList?.data?.payment_types]);

  const columns = useMemo(() => [
    {
      id: "select",
      header: () => (
        <div className='flex items-center justify-center'>
          <Checkbox
            checked={selectedRows.length === sortedData?.length && sortedData.length > 0}
            onCheckedChange={handleSelectAll}
          />
        </div>
      ),
      cell: ({ row }) => (
        <div className='flex items-center justify-center' onClick={(e) => e.stopPropagation()}>
          <Checkbox
            checked={selectedRows.includes(row.original.id)}
            onCheckedChange={(checked) =>
              setSelectedRows(prev => checked
                ? [...prev, row.original.id]
                : prev.filter(id => id !== row.original.id)
              )
            }
          />
        </div>
      ),
      sortable: false,
      unhidable: true
    },
    {
      accessorKey: "document_type",
      header: "Document Type",
      cell: ({ row }) => row.original.document_type_display,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "country",
      header: "Country",
      cell: ({ row }) => row.original.country,
      sortable: true,
      unhidable: true,
    },
    {
      accessorKey: "document_number",
      header: "Document Number",
      cell: ({ row }) => row.original.document_number,
      sortable: true,
      unhidable: true,
    },

    {
      accessorKey: "issued_on",
      header: "Issued On",
      cell: ({ row }) => row.original.issued_on,
      sortable: true,
      unhidable: false,
    },

    {
      accessorKey: "expires_on",
      header: "Expires On",
      cell: ({ row }) => row.original.expires_on,
      sortable: true,
      unhidable: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost" onClick={(e) => e.stopPropagation()}>
              <EllipsisVertical />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsEditModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => {
              e.stopPropagation();
              setIsDeleteModalOpen(true);
              setSelectedRow(row.original);
            }}>
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      sticky: true,
      unhidable: true
    }
  ], [selectedRows, sortedData, handleSelectAll]);

  const [createTravelInfo, setCreateTravelInfo] = useState(false)

  return (
    <>
      <div className="overflow-x-scroll w-full">
        <div className="no-scrollbar">
          <TableComponent
            rows={sortedData}
            columns={columns}
            tableTitle={`Travel Information (${apiResponse?.count})`}
            onRowClick={(row) => {
              setSelectedRow(row);
              setSheetOpen(true);
            }}
            isLoading={isLoading}
            onSort={handleSort}
            sortState={sortState}
            showImportExport={selectedRows.length > 0}
            exportToCSV={handleExportCSV}
            createTitle={<Button onClick={() => setCreateTravelInfo(true)} size="xs"> <SquarePen /> New Travel Request </Button>}
            NoavailableTitle={isLoading ? "Loading..." : apiResponse?.count <= 0 ? "Travel Information" : ""}
            tableDescription={
              "Track and manage your travel documents, flight bookings, hotel accommodations, and journey details. Keep all your travel information organized in one place."
            }
            filterComponents={
              <FilterWrapper
                filterValues={filters}
                onFilterApply={handleFilterApply}
                onFilterClear={handleFilterClear}
                filterOptions={filterOptions}
              />
            }
          />
        </div>
      </div>

      {/* <TripDetail
        sheetOpen={sheetOpen}
        setSheetOpen={setSheetOpen}
        selectedRow={selectedRow}
      /> */}

      {isDeleteModalOpen && selectedRow && (
        <DeleteTravelInfo
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          travelId={selectedRow}
        />
      )}

      {createTravelInfo && (
        <NewTravelDocument
          isOpen={createTravelInfo}
          onClose={() => setCreateTravelInfo(false)}
          mode="create"
        />
      )}

      {isEditModalOpen && (
        <NewTravelDocument
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          mode="update"
          documentData={selectedRow}
        />
      )}
    </>
  );
};

const Page = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TravelDocumentContent />
    </Suspense>
  );
}

export default Page;
