"use client"

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { updateProfileApi } from "@/apis/profile-management";
import toast from "react-hot-toast";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import ButtonLoader from "@/utils/spinner/ButtonLoader";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { SearchableSelect } from "@/components/reusables/SearchableSelect";
import { onboardingChoicesApi, onboardingChoicesJobTitleApi } from "@/apis/utilapi";

export const EditDetailsModal = ({ isOpen, onClose, profileData }) => {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({});
  const [originalData, setOriginalData] = useState({});
  const [backendErrors, setBackendErrors] = useState({});
  const [changedFields, setChangedFields] = useState({});

  // Fetch countries data
  const { data: countriesData, isPending:isLoadingCountries } = useQuery({
    queryKey: ["countries-list"],
    queryFn: onboardingChoicesApi,
    enabled: isOpen
  });

  // Fetch job titles data
  const { data: jobTitlesData, isPending:isLoadingJob } = useQuery({
    queryKey: ["job-titles-list"],
    queryFn: onboardingChoicesJobTitleApi,
    enabled: isOpen
  });

  // Initialize form data when modal opens
  useEffect(() => {
    if (profileData) {
      const initialData = {
        name: profileData?.name || "",
        phone: profileData?.profile?.phone || "",
        job_title: profileData?.profile?.job_title?.id?.toString() || "",
        bio: profileData?.profile?.bio || "",
        state: profileData?.profile?.state || "",
        country: profileData?.profile?.country || "",
        address_1: profileData?.profile?.address_1 || "",
        address_2: profileData?.profile?.address_2 || "",
        position: profileData?.profile?.position || "",
      };

      setFormData(initialData);
      setOriginalData(initialData);
      setChangedFields({});
    }
  }, [profileData, isOpen]);

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });

    // Track changed fields by comparing with original data
    if (value !== originalData[key]) {
      setChangedFields({ ...changedFields, [key]: value });
    } else {
      // If value is back to original, remove from changed fields
      const updatedChangedFields = { ...changedFields };
      delete updatedChangedFields[key];
      setChangedFields(updatedChangedFields);
    }

    // Clear any error for this field
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const { mutateAsync: updateProfile, isPending } = useMutation({
    mutationFn: updateProfileApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Profile updated successfully");
      queryClient.invalidateQueries(["profile-settings"]);
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to update profile");
    },
  });

  const handleSubmit = async (e) => {
    e.preventDefault();

    // If no fields were changed, just close the modal
    if (Object.keys(changedFields).length === 0) {
      toast.info("No changes to save");
      onClose();
      return;
    }

    try {
      // Only send changed fields to the API
      await updateProfile(changedFields);
    } catch (error) {
      console.error("Error updating profile:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[35rem] overflow-y-auto max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>Edit Profile</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div>
            <FloatingLabelInput
              id="name"
              name="name"
              label="Full Name"
              value={formData.name}
              onChange={(e) => handleChange("name", e.target.value)}
              className={`${backendErrors.name ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="name" />
          </div>

          <div>
            <FloatingLabelInput
              id="phone"
              name="phone"
              label="Phone Number"
              value={formData.phone}
              onChange={(e) => handleChange("phone", e.target.value)}
              className={`${backendErrors.phone ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="phone" />
          </div>

          <div>
            <Label htmlFor="job_title" className="text-xs font-medium mb-1 block">Job Title</Label>
            <SearchableSelect
              value={formData.job_title}
              onValueChange={(value) => handleChange("job_title", value)}
              placeholder="Select Job Title"
              searchPlaceholder="Search job titles..."
              items={jobTitlesData?.results}
              error={backendErrors.job_title}
              isLoading={isLoadingJob}
            />
            <ErrorMessage errors={backendErrors} field="job_title" />
          </div>

          <div>
            <FloatingLabelInput
              id="position"
              name="position"
              label="Position"
              value={formData.position}
              onChange={(e) => handleChange("position", e.target.value)}
              className={`${backendErrors.position ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="position" />
          </div>

          <div>
            <FloatingLabelInput
              id="state"
              name="state"
              label="State"
              value={formData.state}
              onChange={(e) => handleChange("state", e.target.value)}
              className={`${backendErrors.state ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="state" />
          </div>

          <div>
            <Label htmlFor="country" className="text-xs font-medium mb-1 block">Country</Label>
            <SearchableSelect
              value={formData.country}
              onValueChange={(value) => handleChange("country", value)}
              placeholder="Select Country"
              searchPlaceholder="Search countries..."
              items={countriesData?.data?.countries}
              error={backendErrors.country}
              isLoading={isLoadingCountries}
            />
            <ErrorMessage errors={backendErrors} field="country" />
          </div>

          <div>
            <FloatingLabelInput
              id="address_1"
              name="address_1"
              label="Address Line 1"
              value={formData.address_1}
              onChange={(e) => handleChange("address_1", e.target.value)}
              className={`${backendErrors.address_1 ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="address_1" />
          </div>

          <div>
            <FloatingLabelInput
              id="address_2"
              name="address_2"
              label="Address Line 2"
              value={formData.address_2}
              onChange={(e) => handleChange("address_2", e.target.value)}
              className={`${backendErrors.address_2 ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="address_2" />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => handleChange("bio", e.target.value)}
              placeholder="Tell us about yourself"
              className={`min-h-[60px] placeholder:text-xs ${backendErrors.bio ? "validate_input" : ""}`}
            />
            <ErrorMessage errors={backendErrors} field="bio" />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              size="xs"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isPending || Object.keys(changedFields).length === 0}
              size="xs"
            >
              {isPending ? (
                <span className="flex items-center gap-2">
                  <ButtonLoader color="#FFFFFF" /> <span>Updating...</span>
                </span>
              ) : (
                "Save Changes"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
