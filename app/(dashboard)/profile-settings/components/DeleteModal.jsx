import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { SearchableSelect } from "@/components/reusables/SearchableSelect";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { usersApi } from "@/apis/admin/users";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { deleteProfile } from "@/apis/profile-management";
import toast from "react-hot-toast";
import ButtonLoader from "@/utils/spinner/ButtonLoader";

const DeleteModal = ({ isOpen, onClose }) => {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState({
    employee: ""
  });
  const [backendErrors, setBackendErrors] = useState({});

  // Fetch users data
  const { data: usersData, isPending:isLoadingUser } = useQuery({
    queryKey: ["users-list"],
    queryFn: usersApi,
    enabled: isOpen
  });

  // Transform users data to include both name and email in the label
  const usersWithLabels = usersData?.results?.map(user => ({
    id: user.id,
    label: (<div className="flex items-center gap-4">
      <div className="rounded-full bg-gray-200 p-1 text-[8px] font-semibold h-8 w-8 flex items-center justify-center">{user?.initials}</div>
      <div className="flex flex-col items-start">
        <span className="font-medium">{user?.name}</span>
        <span>{user?.email}</span>
      </div>
    </div>)
  }));

  const { mutateAsync, isPending } = useMutation({
    mutationKey: ["delete-profile"],
    mutationFn: deleteProfile,
    onSuccess: (data) => {
      toast.success(data?.message || "Delete Profile successfully");
      queryClient.invalidateQueries(["profile-settings"]);
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);
      toast.error(error?.message || "Failed to delete profile");
    }
  });

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.employee) {
      toast.error("Please select a user to transfer ownership to");
      return;
    }

    try {
      await mutateAsync({ employee: formData.employee });
    } catch (error) {
      console.error("Error while deleting profile:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <DialogHeader>
            <DialogTitle>Delete Account</DialogTitle>
          </DialogHeader>

          <div className="mt-6 mb-8">
            <p className="text-sm text-gray-600 mb-4">
              Before deleting, you will have to transfer ownership to one of the employees below, and it cannot be undone.
            </p>

            <Label htmlFor="employee" className="text-xs font-medium mb-1 block">Select User</Label>
            <SearchableSelect
              value={formData.employee}
              onValueChange={(value) => handleChange("employee", value)}
              placeholder="Select User"
              searchPlaceholder="Search users..."
              items={usersWithLabels}
              error={backendErrors.employee}
              className={"py-5"}
              isLoading={isLoadingUser}
            />
            <ErrorMessage errors={backendErrors} field="employee" />
          </div>

          <div className="flex items-center justify-end sm:mt-10 gap-2">
            <DialogClose asChild>
              <Button
                variant="outline"
                size="xs"
                type="button"
              >
                Cancel
              </Button>
            </DialogClose>

            <Button
              size="xs"
              type="submit"
              disabled={isPending || !formData.employee}
            >
              {isPending ? (
                <span className="flex items-center gap-2">
                  <ButtonLoader color="#FFFFFF" /> <span>Processing...</span>
                </span>
              ) : (
                "Delete"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default DeleteModal;
