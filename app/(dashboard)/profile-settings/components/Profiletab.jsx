"use client"

import Link from "next/link";
import { usePathname } from "next/navigation";

const ProfileTab = () => {

  const pathname = usePathname();

  const profileNavigation = [
    {
      name: "Basic Information",
      path: "/profile-settings"
    },
    {
      name: "Preferences",
      path: "/profile-settings/preferences"
    },
    {
      name: "Travel Information",
      path: "/profile-settings/travelinformation"
    },
  ]

  return (
    <>
      <div className="p-4 .px-10 min-h-screen border-r border-gray-50">
        <div className="textTitle">Profile Settings</div>

        <div className="space-y-2 flex flex-col gap-4 whitespace-nowrap mt-6">
          {profileNavigation?.map((item, index) => (
            <Link href={item?.path} key={index} className={`text-xs sm:text-xs font-[500] hover:font-semibold flex items-center h-8 ${pathname === item?.path && 'font-semibold border-l-4 border-primary'}`}> <span className="pl-2">{item?.name}</span> </Link>
          ))}
        </div>

      </div>
    </>
  );
};


export default ProfileTab;