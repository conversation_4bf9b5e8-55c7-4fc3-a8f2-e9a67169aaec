import { FloatingLabelInput } from "@/components/ui/flaoting-label-input";
import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import { changePasswordApi } from "@/apis/profile-management";
import ButtonLoader from "@/utils/spinner/ButtonLoader";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";

export const Changepassword = ({ isOpen, onClose }) => {

  const [toggle, setToggle] = useState({
    old_password: false,
    new_password: false,
    confirm_new_password: false,
  })

  const handleTogglePasswords = (key) => {
    setToggle(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const [formData, setFormData] = useState({
    old_password: "",
    new_password: "",
  });

  const [confirmnewpassword, setconfirmnewpassword] = useState("")
  const [backendErrors, setBackendErrors] = useState({});

  const { mutateAsync: changePasswordMutation, isPending: isChanging } = useMutation({
    mutationFn: changePasswordApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Password Changed successfully");
      onClose();
    },
    onError: (error) => {
      const errorDetails = error?.details || {};
      setBackendErrors(errorDetails);

      // If there's a general error message, show it as a toast
      if (error?.message) {
        toast.error(error.message);
      } else {
        toast.error("Failed to change password");
      }
    },
  });

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    // Clear the specific error when user starts typing
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const handleConfirmPasswordChange = (value) => {
    setconfirmnewpassword(value);
    setBackendErrors({ ...backendErrors, confirm_new_password: null });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Client-side validation
    if (formData.new_password.length < 8 || formData.old_password.length < 8) {
      toast.error("Password or new password must be at least 8 characters long.");
      return;
    }

    // Check if new password and confirm new password match
    if (formData.new_password !== confirmnewpassword) {
      setBackendErrors({
        ...backendErrors,
        confirm_new_password: "New password does not match with confirm new password."
      });
      return;
    }

    try {
      await changePasswordMutation(formData);
    } catch (error) {
      console.log(error);
      // Error handling is done in the mutation's onError callback
    }
  }

  const check = !formData.new_password || !formData.old_password || !confirmnewpassword || isChanging

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[30rem] overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 pt-4">

            <div>

              <div className="flex flex-col gap-3 relative">
                <FloatingLabelInput
                  id="password"
                  value={formData.old_password}
                  className={`placeholder:text_gray ${backendErrors.old_password ? "validate_input" : ""}`}
                  name="password"
                  onChange={(e) => handleChange("old_password", e.target.value)}
                  label="Password"
                  type={toggle.old_password ? "text" : "password"}
                />
                <span
                  className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                  onClick={() => handleTogglePasswords("old_password")}
                >
                  {toggle.old_password ? (
                    <EyeOff size={16} className="text-gray-500" />
                  ) : (
                    <Eye size={16} className="text-gray-500" />
                  )}
                </span>
              </div>
              <ErrorMessage errors={backendErrors} field="old_password" />
            </div>

            <div>

              <div className="flex flex-col gap-3 relative">
                <FloatingLabelInput
                  id="newpassword"
                  value={formData.new_password}
                  className={`placeholder:text_gray ${backendErrors.new_password ? "validate_input" : ""}`}
                  name="new_password"
                  onChange={(e) => handleChange("new_password", e.target.value)}
                  label="New Password"
                  type={toggle.new_password ? "text" : "password"}
                />
                <span
                  className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                  onClick={() => handleTogglePasswords("new_password")}
                >
                  {toggle.new_password ? (
                    <EyeOff size={16} className="text-gray-500" />
                  ) : (
                    <Eye size={16} className="text-gray-500" />
                  )}
                </span>
              </div>
              <ErrorMessage errors={backendErrors} field="new_password" />
            </div>

            <div>

              <div className="flex flex-col gap-3 relative">
                <FloatingLabelInput
                  id="confirm_newpassword"
                  value={confirmnewpassword}
                  className={`placeholder:text_gray ${backendErrors.confirm_new_password ? "validate_input" : ""}`}
                  label="Confirm New Password"
                  type={toggle.confirm_new_password ? "text" : "password"}
                  onChange={(e) => handleConfirmPasswordChange(e.target.value)}
                />
                <span
                  className="absolute inset-y-0 right-0 flex items-center pr-3 cursor-pointer"
                  onClick={() => handleTogglePasswords("confirm_new_password")}
                >
                  {toggle.confirm_new_password ? (
                    <EyeOff size={16} className="text-gray-500" />
                  ) : (
                    <Eye size={16} className="text-gray-500" />
                  )}
                </span>
              </div>
              <ErrorMessage errors={backendErrors} field="confirm_new_password" />
            </div>


          </div>

          {/* Submit and Cancel Buttons */}
          <div className="flex items-center mt-4 gap-2">
            <Button
              size="xs"
              type="submit"
              className="w-full"
              disabled={check}
            >
              {isChanging ? (
                <React.Fragment>
                  <ButtonLoader color={"text-white"} />
                  <span>Changing...</span>
                </React.Fragment>
              ) : (
                <span>Change Password</span>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}