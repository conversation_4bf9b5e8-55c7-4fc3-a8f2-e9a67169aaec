"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, CircleArrowUp, Users } from "lucide-react";
import React, { useState } from "react";
import NewDelegateAssign from "./components/NewDelegateAssign";

const Page = () => {

  const [addDelegate, setAddDelegate] = useState(false)

  return (
    <>
      <div className="min-h-screen">

        <div className="px-5 pb-4">
          <h1 className="text-sm font-bold">Preferences</h1>
        </div>
        <hr className="border-gray-200" />

        <div className="p-10 rounded-lg">

          {/* <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Email Receipt Fowarding</h3>
              <p className="text-xs text-gray-600 mt-1 w-5/6">
                Send emails with <NAME_EMAIL>. Eloope expense will scan and convert them into expenses automatically              </p>
            </div>
            <Button
              className="profileButton"
              variant="outline"
              size="xs"
            >
              <CircleArrowUp strokeWidth={2} />
              Update
            </Button>
          </div>

          <hr className="border-gray-200 my-8" /> */}

          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Delegate Expense Management</h3>
              <p className="text-xs text-gray-600 mt-1 w-5/6">Authorize another user to create, submit and approve expense records on your behalf</p>
            </div>
            <Button
              className="profileButton"
              variant="outline"
              size="xs"
              onClick={() => setAddDelegate(true)}
            >
              <Users strokeWidth={2} />
              Transfer
            </Button>
          </div>

        </div>
      </div>

      {addDelegate && (
        <NewDelegateAssign
          isOpen={addDelegate}
          onClose={() => setAddDelegate(false)}
          mode="create"
        />
      )}
    </>
  );
};

export default Page;