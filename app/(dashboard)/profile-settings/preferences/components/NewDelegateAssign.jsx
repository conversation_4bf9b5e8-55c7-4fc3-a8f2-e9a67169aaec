import { assignDelegate<PERSON>pi, getDelegatePermissions } from "@/apis/profile-management";
import { SearchableSelect } from "@/components/reusables/SearchableSelect";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ErrorMessage } from "@/utils/validations/ErrorMessage";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Forward, Info, Upload, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { usersApi } from "@/apis/admin/users";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

const NewDelegateAssign = ({ isOpen, onClose }) => {
    const [openIssue, setOpenIssue] = useState(false)    
    const [openExpiry, setOpenExpiry] = useState(false)    
  
  // Fetch data
  const { data: usersQuery, isPending: isLoadingUser } = useQuery({
    queryKey: ["users-list"],
    queryFn: usersApi,
    enabled: isOpen
  });

  const { data: apiResponse, isPending: fetchingRole } = useQuery({
    queryKey: ["role"],
    queryFn: getDelegatePermissions
  })

  const usersWithLabels = usersQuery?.results?.map(user => ({
    id: user.id,
    label: (<div className="flex items-center gap-4">
      <div className="rounded-full bg-gray-200 p-1 text-[8px] font-semibold h-8 w-8 flex items-center justify-center">{user?.initials}</div>
      <div className="flex flex-col items-start">
        <span className="font-medium">{user?.name}</span>
        <span>{user?.email}</span>
      </div>
    </div>)
  }));


  // Form state
  const [formData, setFormData] = useState({
    delegate: "",
    start_date: "",
    end_date: "",
    permissions: []
  });

  console.log("delegate", formData.delegate)
  console.log("userQ", usersQuery)

  const [backendErrors, setBackendErrors] = useState({});

  const handleChange = (key, value) => {
    setFormData({ ...formData, [key]: value });
    setBackendErrors({ ...backendErrors, [key]: null });
  };

  const handleDateChange = (key, date) => {
    if (date) {
      const formattedDate = format(date, "yyyy-MM-dd");
      setFormData({ ...formData, [key]: formattedDate });
      setBackendErrors({ ...backendErrors, [key]: null });
    }
  };

  const handleRoleSelect = (roleName) => {
    // Find the role object from API response
    const roleObj = apiResponse?.results?.find(item => item.name === roleName);
    if (!roleObj) return;

    // Check if this role is already selected
    const isSelected = formData.permissions.some(r => r.id === roleObj.id);

    const updatedRoles = isSelected
      ? formData.permissions.filter(r => r.id !== roleObj.id)
      : [...formData.permissions, roleObj];

    handleChange("permissions", updatedRoles);
  }

  const { mutate: submitAssignDelegate, isPending: isSubmitting } = useMutation({
    mutationFn: assignDelegateApi,
    onSuccess: (data) => {
      toast.success(data?.message);
      onClose();
    },
    onError: (error) => {
      toast.error(error?.message);
      if (error?.details) {
        setBackendErrors(error.details);
      }
    }
  });

  // Helper function to get delegate initials
  const getDelegateInitials = () => {
    if (!formData.delegate) return '?';
    
    const selectedUser = usersQuery?.results?.find(user => {
      // Convert both to strings for comparison to avoid type issues
      return String(user.id) === String(formData.delegate);
    });
    
    return selectedUser?.initials || selectedUser?.name?.charAt(0) || '?';
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const dataToSend = {
        ...formData,
        permissions: formData.permissions.map(role => role.id),
      };
    submitAssignDelegate(dataToSend);
  };

  const getCurrentDetail = JSON.parse(localStorage.getItem("Eloope_UserData"))

  // console.log("getCurrentDetail", getCurrentDetail)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:max-w-[40rem] overflow-hidden">
        <form onSubmit={handleSubmit}>

          <DialogHeader>
            <DialogTitle>Add Delegate</DialogTitle>
          </DialogHeader>

          <div className="flex items-center justify-center gap-4">
            <div
              className="flex items-center justify-center p-4 font-black text-xl rounded-full h-16 w-16 bg-foreground dark:bg-primary text-white dark:text-background"
            >
              {getCurrentDetail?.userData?.initials}
            </div>

            <div>
              <Forward size={30} />
            </div>

            <div
              className="flex items-center justify-center p-4 font-black text-xl rounded-full h-16 w-16 bg-foreground dark:bg-primary text-white dark:text-background"
            >
              {getDelegateInitials()}
            </div>
          </div>

          <div className="flex items-center p-4 gap-2 text-xs bg-gray-100 mt-4 rounded-md">
            <Info size={16} className="text-red-500"/>
            <span>The Delegate you add here will have acceess to perform actions on your behalf</span>
          </div>

          <div className="my-4 space-y-5">
            <div>
              <Label htmlFor="type" className="text-xs font-medium mb-1 block">Delegate access to:</Label>
              <SearchableSelect
                value={formData.delegate}
                onValueChange={(value) => handleChange("delegate", value)}
                placeholder="Delegate Access To"
                searchPlaceholder="Search delegate..."
                items={usersWithLabels}
                error={backendErrors.delegate}
                className="py-5"
                isLoading={isLoadingUser}
              />
              <ErrorMessage errors={backendErrors} field="delegate" />
            </div>


            {/* Date fields side by side */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="start_date" className="text-xs font-medium mb-1 block">Issue Date</Label>
              <Popover open={openIssue} onOpenChange={setOpenIssue}>
                  <PopoverTrigger asChild>
                    <Button
                      id="start_date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.start_date && "text-muted-foreground",
                        backendErrors.start_date && "validate_input"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.start_date ? (
                        format(new Date(formData.start_date), "PPP")
                      ) : (
                        <span>Select issue date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.start_date ? new Date(formData.start_date) : undefined}
                      onSelect={(date) => {handleDateChange("start_date", date); setOpenIssue(false);}}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="start_date" />
              </div>

              <div>
                <Label htmlFor="end_date" className="text-xs font-medium mb-1 block">Expiry Date</Label>
              <Popover open={openExpiry} onOpenChange={setOpenExpiry}>
                  <PopoverTrigger asChild>
                    <Button
                      id="end_date"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.end_date && "text-muted-foreground",
                        backendErrors.end_date && "validate_input"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.end_date ? (
                        format(new Date(formData.end_date), "PPP")
                      ) : (
                        <span>Select expiry date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.end_date ? new Date(formData.end_date) : undefined}
                      onSelect={(date) => { handleDateChange("end_date", date); setOpenExpiry(false); }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <ErrorMessage errors={backendErrors} field="end_date" />
              </div>
            </div>


            <div className="relative">
              <label className="text-xs font-medium mb-1 block">Permissions *</label>
              <div className="relative">
                <Select
                  onValueChange={(value) => handleRoleSelect(value)}
                >
                  <SelectTrigger className={`text-xs ${backendErrors.permissions && "validate_input"} min-h-[40px] flex-wrap pr-8`}>
                    {formData.permissions.length > 0 ? (
                      <div className="flex flex-wrap gap-1 ">
                        {formData.permissions.map((role, index) => (
                          <Badge key={index} className="bg-transparent text-primary hover:text-secondary border border-primary px-2 flex items-center gap-1 text-[8px]">
                            {role.name}
                            <span
                              className="cursor-pointer ml-1 border rounded-full p-[2px] inline-flex items-center justify-center"
                              onMouseDown={(e) => {
                                e.preventDefault();
                                handleChange("permissions", formData.permissions.filter(r => r.id !== role.id));
                              }}
                            >
                              <X size={8} />
                            </span>
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <SelectValue placeholder="Select Roles" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    {apiResponse?.results?.map((item) => (
                      <SelectItem
                        key={item?.id}
                        value={item?.name}
                        className={formData.permissions.some(r => r.id === item.id) ? "bg-gray-100 text-xs" : "text-xs"}
                      >
                        {item?.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.permissions.length > 0 && (
                  <button
                    type="button"
                    className={`absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full hover:bg-gray-100`}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      handleChange("permissions", []);
                    }}
                  >
                    <X size={14} />
                  </button>
                )}
              </div>
              <ErrorMessage errors={backendErrors} field="permissions" />
            </div>

          </div>

          <div className="flex items-center mt-6 justify-end gap-2">
            <DialogClose asChild>
              <Button size="xs" variant="outline" type="button">Cancel</Button>
            </DialogClose>
            <Button
              size="xs"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Processing..." : "Grant Access"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default NewDelegateAssign;
