"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import React, { useState, useRef } from "react";
import { Changepassword } from "./components/Changepassword";
import { EditDetailsModal } from "./components/EditDetailsModal";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getProfileApi, updateProfileApi } from "@/apis/profile-management";
import TransferOwnership from "./components/TransferOwnership";
import { Camera } from "lucide-react";
import toast from "react-hot-toast";
import Image from "next/image";
import DeleteModal from "./components/DeleteModal";

const Page = () => {
  const [open, setOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [editDetailsOpen, setEditDetailsOpen] = useState(false);
  const [openOwership, setOpenOwership] = useState(false);
  const fileInputRef = useRef(null);
  const queryClient = useQueryClient();

  const { data: myProfile } = useQuery({
    queryKey: ["profile-settings"],
    queryFn: getProfileApi
  });

  const { mutate: updateProfile, isPending } = useMutation({
    mutationFn: updateProfileApi,
    onSuccess: (data) => {
      toast.success(data?.message || "Profile picture updated successfully");
      queryClient.invalidateQueries(["profile-settings"]);
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to update profile picture");
    },
  });

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size should not exceed 5MB");
        return;
      }

      const formData = new FormData();
      formData.append('profile_picture', file);

      updateProfile(formData);
    }
  };

  return (
    <>
      <div className="min-h-screen">

        <div className="px-5 pb-4">
          <h1 className="text-sm font-bold">Basic Information</h1>
        </div>
        <hr className="border-gray-200" />

        <div className="p-10 mt-4 rounded-lg">
          {/* Your Detail Section */}
          <div className="flex justify-between items-center w-full mb-6">
            <h2 className="text-lg font-bold">Your Details</h2>
            <Button
              variant="outline"
              size="xs"
              onClick={() => setEditDetailsOpen(true)}
            >
              Edit Details
            </Button>
          </div>

          {/* Information Grid */}
          <div className="grid grid-cols-5 gap-y-6 gap-x-10 items-center text-sm">
            <div className="col-span-1 font-semibold">Avatar</div>
            <div className="col-span-4 w-fit relative group">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept="image/*"
                className="hidden"
              />

              {myProfile?.data?.profile?.profile_picture ? (
                <div className="relative h-16 w-16 rounded-full overflow-hidden cursor-pointer" onClick={() => fileInputRef.current.click()}>
                  <Image
                    src={myProfile.data.profile.profile_picture}
                    alt="Profile"
                    className="h-full w-full object-cover"
                    width={64}
                    height={64}
                    unoptimized={true}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <Camera className="h-4 w-4 text-white" />
                  </div>
                </div>
              ) : (
                <div
                  className="flex items-center justify-center p-4 font-black text-xl rounded-full h-16 w-16 bg-foreground dark:bg-primary text-white dark:text-background cursor-pointer relative group"
                  onClick={() => fileInputRef.current.click()}
                >
                  {myProfile?.data?.initials}
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity rounded-full">
                    <Camera className="h-4 w-4 text-white" />
                  </div>
                </div>
              )}

              {isPending && (
                <div className="absolute top-0 left-0 h-16 w-16 rounded-full bg-black bg-opacity-60 flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </div>

            <div className="col-span-1 font-semibold">Full Name</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.name}</div>

            <div className="col-span-1 font-semibold">Email Address</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.email}</div>

            <div className="col-span-1 font-semibold">Phone Number</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.phone}</div>

            <div className="col-span-1 font-semibold">Job Title</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.job_title?.name || "--"}</div>

            <div className="col-span-1 font-semibold">State</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.state || "--"}</div>

            <div className="col-span-1 font-semibold">Country</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.country_display || "--"}</div>

            <div className="col-span-1 font-semibold">Address 1</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.address_1 || "--"}</div>

            <div className="col-span-1 font-semibold">Address 2</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.address_2 || "--"}</div>

            <div className="col-span-1 font-semibold">Position</div>
            <div className="col-span-4 w-fit">{myProfile?.data?.profile?.position || "--"}</div>

            <div className="col-span-1 font-semibold">Bio</div>
            <div className="col-span-4 w-fit">
              {myProfile?.data?.profile?.bio || "--"}
            </div>

          </div>

          <hr className="border-gray-200 my-8" />

          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Password Change</h3>
              <p className="text-sm text-gray-600 mt-1">
                A password must contain a minimum of 12 characters, one lowercase letter, and one number.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => setOpen(true)}
              size="xs"
            >
              Change Password
            </Button>
          </div>

          <hr className="border-gray-200 my-8" />

          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Take Ownership</h3>
              <p className="text-sm text-gray-600 mt-1">Transfer this account to another person.</p>
            </div>
            <Button
              variant="outline"
              size="xs"
              onClick={() => setOpenOwership(true)}
            >
              Transfer
            </Button>
          </div>

          <hr className="border-gray-200 my-8" />

          <div className="flex justify-between items-center w-full">
            <div className="flex flex-col">
              <h3 className="text-base font-bold">Account Deletion</h3>
              <p className="text-sm text-gray-600 mt-1">
                Permanently remove this account and all associated data.
              </p>
            </div>
            <Button
              variant="outline"
              size="xs"
              onClick={() => setDeleteModalOpen(true)}
            >
              Delete Account
            </Button>
          </div>
        </div>
      </div>

      {open && (
        <Changepassword
          isOpen={open}
          onClose={() => setOpen(false)}
        />
      )}

      {deleteModalOpen && (
        <DeleteModal
          isOpen={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
        />
      )}

      {editDetailsOpen && (
        <EditDetailsModal
          isOpen={editDetailsOpen}
          onClose={() => setEditDetailsOpen(false)}
          profileData={myProfile?.data}
        />
      )}

      {openOwership && (
        <TransferOwnership
          isOpen={openOwership}
          onClose={() => setOpenOwership(false)}
        />
      )}
    </>
  );
};

export default Page;
