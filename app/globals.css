@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@theme {
  --container-3xl: 96rem;
}

/* geist-sans-latin-400-normal */
@font-face {
  font-family: 'Geist Sans';
  font-style: normal;
  font-display: swap;
  font-weight: 400;
  src: url(https://cdn.jsdelivr.net/fontsource/fonts/geist-sans@latest/latin-400-normal.woff2) format('woff2'), url(https://cdn.jsdelivr.net/fontsource/fonts/geist-sans@latest/latin-400-normal.woff) format('woff');
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground overflow-x-hidden;
  }

  .btn_opacity {
    @apply opacity-70 cursor-not-allowed
  }

  .Title {
    @apply text-lg font-semibold;
  }

  .textTitle {
    @apply text-[14px] font-semibold text-gray-950;
  }

  .textContent {
    @apply text-[14px] font-[400] text-gray-950;
  }

  .profileButton {
    @apply gap-2 hover:bg-slate-950 hover:text-white transition-all duration-500;
  }

  #detail span {
    @apply whitespace-nowrap;
  }

}

.validate {
  @apply text-red-500 text-xs pt-1;
}

.validate_input {
  @apply border-red-500 outline-red-500;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.authBg {
  background: url("/images/onboarding/onboarding-bg.jpeg");
  width: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.quill {
  border-radius: 10px;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}


/* Custom CSS for Quill Editor */
.quill-container {
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(133, 132, 132, 0.211)
}

.ql-container {
  border-radius: 0 0 20px 20px !important;
  border: none !important;
}

/* .ql-toolbar {
  border-radius: 20px 20px 0 0 !important;
  border: 0 0 0 0 !important;
} */ /* IntlTelInput custom styles */


.intl-tel-input {
  width: 100%;
  z-index: 100;
}

.intl-tel-input .form-control {
  width: 100%;
  height: 32px;
  border-radius: 0.4rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.intl-tel-input .selected-flag {
  padding: 0 8px 0 8px;
}

.intl-tel-input .country-list {
  font-size: 10px;
  border-radius: 5px;
  border: none !important;
  z-index: 100 !important;
  background: gray !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.bprogress {
  color: var(--primary);
}