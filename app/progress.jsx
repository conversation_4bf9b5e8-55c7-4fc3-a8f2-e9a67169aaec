'use client'

import React from 'react'
import { AppProgressBar as ProgressBar } from 'next-nprogress-bar'
import { useTheme } from 'next-themes' // Add this import

const Progress = ({ children }) => {
  const { theme } = useTheme() // Get current theme

  return (
    <>
      {children}
      <ProgressBar
        height='4px'
        // color={theme === 'dark' ? 'red' : 'white'}
        shallowRouting
        // className="dark:text-white"
      />
    </>
  )
}

export default Progress
