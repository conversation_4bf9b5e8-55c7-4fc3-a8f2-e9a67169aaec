import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name = shortName

export const listOfExpenses = async (params = {}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expenses`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createExpenseApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/expenses`, payload, {
      headers: {
        'Content-Type': 'multipart/form-data', // Set content type for FormData
      }
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createMultipleExpenseApi = async (formData) => {
  try {
    const response = await axiosInstance.post(
      `/expense-app/${short_name}/expenses/bulk-create`,
      formData, // Send FormData directly
      {
        headers: {
          'Content-Type': 'multipart/form-data', // Set content type for FormData
        },
      }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateExpenseApi = async (id, payload) => {
  try {
    // FormData objects can't be spread or destructured properly
    // So we need to pass the id separately and the FormData object directly
    const response = await axiosInstance.patch(`/expense-app/${short_name}/expenses/${id}`, payload, {
      headers: {
        'Content-Type': 'multipart/form-data', // Set content type for FormData
      },
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteExpense = async ({ id }) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/expenses/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const bulkDeleteExpense = async (ids) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/expenses/bulk-delete`, ids);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportExpenseCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/expenses/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};