import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const retrieveOrgData = async () => {
  try {
    const response = await axiosInstance.get(`/${short_name}/settings`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateOrgData = async (payload, isFormData=false) => {
  try {
    const response = await axiosInstance.patch(`/${short_name}/settings`, payload, 
      { headers: isFormData ? 
        { 'Content-Type': 'multipart/form-data' }
      : { 'Content-Type': 'application/json' }
    }
    );
    return response.data;
  } catch (error) {
    handleError(error);
  }
};