import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const listOfTrips = async (params = {}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/trips`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createTripApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/trips`,  payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateTripApi = async (id, payload) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/trips/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteTrip = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/trips/${id}`);
    return response.data; 
  } catch (error) {
    handleError(error);
  }
};

export const exportTripCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/trips/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};