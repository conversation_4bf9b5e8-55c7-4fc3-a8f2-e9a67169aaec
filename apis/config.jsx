import axios from 'axios';

// Base URL for your API
const url = process.env.NEXT_PUBLIC_EXPENSE_BASE_URL;

// Create an Axios instance
export const axiosInstance = axios.create({
  baseURL: url,
  headers: { 'Content-Type': 'application/json' },
});

// Utility to safely access localStorage
const getLocalStorageItem = (key) => {
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error parsing localStorage item ${key}:`, error);
      // Clear corrupted data
      localStorage.removeItem(key);
      return null;
    }
  }
  return null;
};

// Utility to safely set localStorage
export const setLocalStorageItem = (key, value) => {
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Error setting localStorage item ${key}:`, error);
      return false;
    }
  }
  return false;
};

// Utility to safely remove localStorage
export const removeLocalStorageItem = (key) => {
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing localStorage item ${key}:`, error);
      return false;
    }
  }
  return false;
};

// Export the localStorage getter as well
export { getLocalStorageItem };

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    // Try to get token from multiple possible storage formats
    const tokenData = getLocalStorageItem('EloopeToken');
    let token = null;

    if (tokenData) {
      // Handle different token storage formats
      token = tokenData.userAuthToken || tokenData.token || tokenData;
    }

    if (token && typeof token === 'string') {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (typeof window !== 'undefined' && window.localStorage && error.response?.status === 401) {
      // Save current path before redirecting (only if not already on auth pages)
      const currentPath = window.location.pathname + window.location.search;
      const isAuthPage = currentPath === '/' ||
                        currentPath.includes('/login') ||
                        currentPath.includes('/signup') ||
                        currentPath.includes('/forgot-password') ||
                        currentPath.includes('/create-password');

      if (!isAuthPage) {
        try {
          localStorage.setItem('lastPath', currentPath);
        } catch (e) {
          console.error('Failed to save last path:', e);
        }
      }

      // Clear all auth-related data
      try {
        localStorage.removeItem('EloopeToken');
        localStorage.removeItem('Eloope_UserData');
      } catch (e) {
        console.error('Failed to clear auth data:', e);
      }

      // Redirect to login page with a small delay to ensure cleanup
      setTimeout(() => {
        window.location.href = '/';
      }, 100);
    }
    return Promise.reject(error);
  }
);

// Function to get shortName dynamically
export const getShortName = () => {
  if (typeof window === 'undefined' || !window.localStorage) {
    return null;
  }

  try {
    const userData = getLocalStorageItem('Eloope_UserData')?.userData;
    // If user is admin, return null, otherwise return organization short_name
    return userData?.is_admin ? null : userData?.organizations?.[0]?.short_name || null;
  } catch (error) {
    console.error('Error getting shortName:', error);
    return null;
  }
};

// Legacy export for backward compatibility - but this should be avoided in SSR
export const shortName = typeof window !== 'undefined' ? getShortName() : null;

// Global error handler
export const handleError = (error) => {
  if (error?.response?.data) {
    throw error.response.data;
  }
  throw error;
};