import axios from 'axios';

// Base URL for your API
let url = process.env.NEXT_PUBLIC_EXPENSE_BASE_URL;

// Create an Axios instance
export const axiosInstance = axios.create({
  baseURL: url,
  headers: { 'Content-Type': 'application/json' },
});

// Utility to safely access localStorage
const getLocalStorageItem = (key) => {
  if (typeof window !== 'undefined') {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error parsing localStorage item ${key}:`, error);
      return null;
    }
  }
  return null; // Return null if localStorage is not available
};

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    const tokenData = getLocalStorageItem('EloopeToken');
    const token = tokenData?.userAuthToken;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (typeof window !== 'undefined' && error.response?.status === 401) {
      // Save current path before redirecting
      const currentPath = window.location.pathname + window.location.search;
      if (currentPath !== '/' && !currentPath.includes('/login')) {
        localStorage.setItem('lastPath', currentPath);
      }
      
      // Clear auth token
      localStorage.removeItem('EloopeToken');
      
      // Redirect to login page
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Safe export of `shortName` with admin check
export const shortName = typeof window !== 'undefined'
  ? (() => {
      const userData = getLocalStorageItem('Eloope_UserData')?.userData;
      // If user is admin, return null, otherwise return organization short_name
      return userData?.is_admin ? null : userData?.organizations[0]?.short_name;
    })()
  : null;

// Global error handler
export const handleError = (error) => {
  if (error?.response?.data) {
    throw error.response.data;
  }
  throw error;
};