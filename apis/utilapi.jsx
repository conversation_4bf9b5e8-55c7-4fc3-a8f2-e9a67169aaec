import { axiosInstance, handleError } from "./config";


export const choicesApi = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/utils/choices`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const onboardingChoicesApi = async () => {
  try {
    const response = await axiosInstance.get(`/utils/choices`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const onboardingChoicesJobTitleApi = async () => {
  try {
    const response = await axiosInstance.get(`/utils/choices/job-titles`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const jobTitles = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/utils/choices`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const currencyList = async () => {
  try {
    const response = await axiosInstance.get(`/utils/choices/currencies`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const timezoneList = async () => {
  try {
    const response = await axiosInstance.get(`/utils/choices/timezone`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

// permission endpoint
export const permissionApi = async () => {
  try {
    const response = await axiosInstance.get(`/utils/permissions`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


// notification endpoint
export const getListOfNotification = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/utils/notifications`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const notificationMarkAsReadApi = async (id) => {
  try {
    const response = await axiosInstance.post(`/expense-app/utils/notifications/${id}/read`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const notificationMarkAllAsReadApi = async () => {
  try {
    const response = await axiosInstance.post(`/expense-app/utils/notifications/read-all`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};