import { axiosInstance, handleError, shortName } from "./config";

const short_name=shortName


export const analyticsOverview = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/analytics`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const analyticsExpenseTrends = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/analytics/expense-trends`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};