import { axiosInstance, handleError, getShortName } from "./config";

const getShortNameForAPI = () => getShortName();


export const analyticsOverview = async (params={}) => {
  try {
    const short_name = getShortNameForAPI();
    const response = await axiosInstance.get(`/expense-app/${short_name}/analytics`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const analyticsExpenseTrends = async (params={}) => {
  try {
    const short_name = getShortNameForAPI();
    const response = await axiosInstance.get(`/expense-app/${short_name}/analytics/expense-trends`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};