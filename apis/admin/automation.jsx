import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const getSubApps = async () => {
  try {
    const response = await axiosInstance.get(`/automation/utils/sub-apps`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const listOfAutomations = async () => {
  try {
    const response = await axiosInstance.get(`/automation/${short_name}/automations`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveAutomaation = async ({id}) => {
  try {
    const response = await axiosInstance.get(`/automation/${short_name}/automations/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createAutomation = async (payload) => {
  try {
    const response = await axiosInstance.post(`/automation/${short_name}/automations`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteAutomationApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/automation/${short_name}/automations/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportAutomationCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/automation/${short_name}/automations/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


// triggers and actions
export const getActionTypes = async () => {
  try {
    const response = await axiosInstance.get(`/automation/utils/action-types`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getEventTypes = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/event-types`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getDateTimeTypes = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/date-time-types`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getActionTypeFields = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/action-type-fields`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

// choices and options

export const getChoices = async () => {
  try {
    const response = await axiosInstance.get(`/automation/utils/choices`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getEventTypesChoices = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/event-types/choices`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getEventTypesOptions = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/event-types/options`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getActionTypeFieldChoices = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/action-type-fields/choices`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getActionTypeFieldOptions = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/automation/utils/action-type-fields/options`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};



// validate triggers and actions
export const validateTriggers = async (payload) => {
  try {
    const response = await axiosInstance.post(`/automation/${short_name}/automations/validate-triggers`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const validateActions = async (payload) => {
  try {
    const response = await axiosInstance.post(`/automation/${short_name}/automations/validate-actions`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};