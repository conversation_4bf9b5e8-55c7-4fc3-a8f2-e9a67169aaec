import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const listOfCurrencyRate = async () => {
  try {
    const response = await axiosInstance.get(`/${short_name}/exchange-rates`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exchangeRateApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/exchange-rates`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateExchangeRateApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/${short_name}/exchange-rates/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


export const deleteExchangeRateApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/${short_name}/exchange-rates/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportExchangeRateCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/exchange-rates/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};
