import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const listOfPolicies = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expense-policies`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retreivePolicy = async (id) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expense-policies/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createNewPolicyApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/expense-policies`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deletePolicyApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/expense-policies/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updatePolicyApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/expense-policies/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

