import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const usersApi = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/${short_name}/employees`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveUsersApi = async ({id}) => {
  try {
    const response = await axiosInstance.get(`/${short_name}/employees/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createUsersApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/employees`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const inviteUsersApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/employees/invite`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateUsersApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/${short_name}/employees/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteUsersApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/${short_name}/employees/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportUsersCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/employees/export`, payload, { 
      responseType: 'blob' 
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};