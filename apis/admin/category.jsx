import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const listLimit = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expense-policy-category-limits`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateCategoryApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/expense-policy-category-limits/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteCategoryApi = async ({id}) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/expense-policy-category-limits/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

