import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const RolesApi = async () => {
  try {
    const response = await axiosInstance.get(`/${short_name}/roles`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveRolesApi = async ({id}) => {
  try {
    const response = await axiosInstance.get(`/${short_name}/roles/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createRolesApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/roles`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateRolesApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/${short_name}/roles/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteRolesApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/${short_name}/roles/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const duplicateRolesApi = async ({id}) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/roles/${id}/duplicate`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportRolesCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/roles/export`, payload, { 
      responseType: 'blob' 
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};