import { axiosInstance, handleError, shortName } from "../config";


// API request functions
const short_name=shortName

export const departmentsApi = async () => {
  try {
    const response = await axiosInstance.get(`/${short_name}/departments`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveDepartmentsApi = async ({id}) => {
  try {
    const response = await axiosInstance.get(`/${short_name}/departments/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createDepartmentsApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/${short_name}/departments`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateDepartmentsApi = async ({id, ...payload}) => {
  try {
    const response = await axiosInstance.patch(`/${short_name}/departments/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteDepartmentsApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/${short_name}/departments/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportDepartmentsCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`${short_name}/departments/export`, payload, { 
      responseType: 'blob' 
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};