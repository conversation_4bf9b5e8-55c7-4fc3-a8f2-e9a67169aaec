import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const listOfPolicies = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expense-policies`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportPoliciesCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/expense-policies/export`, payload, { 
      responseType: 'blob' 
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};