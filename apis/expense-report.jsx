import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const listOfReports = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/reports`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createReportApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/reports`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteReportApi = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/reports/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const RetrieveReports = async (id) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/reports/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateReportApi = async ({ id, ...payload }) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/reports/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export const submitReportApi = async ({ id }) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/reports/${id}/submit`);
    return response.data;
  } catch (error) {
    handleError(error);
    // throw error;
  }
};

export const approveReportApi = async ({ id }) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/report-approvals/${id}/approve`);
    return response.data;
  } catch (error) {
    handleError(error);
    // throw error;
  }
};

export const rejectReportApi = async ({ id, reason }) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/report-approvals/${id}/reject`, { reason });
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export const readReceiptApi = async (images) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/expenses/read-receipt`, images, {
      headers: {
        'Content-Type': 'multipart/form-data',
      }
    });
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export const exportReportCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/reports/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};
