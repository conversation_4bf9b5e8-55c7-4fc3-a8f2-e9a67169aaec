import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const recordAdvance = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/advances`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const listOfAdvances = async (params = {}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/advances`, { params });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateAdvancesApi = async ({ id, ...payload }) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/advances/${id}`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export const deleteAdvancesApi = async ({ id }) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/advances/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export const exportAdvanceCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/advances/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};