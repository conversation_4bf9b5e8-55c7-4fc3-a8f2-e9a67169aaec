import { axiosInstance, handleError } from "../config";


// API request functions

export const CompanyApi = async () => {
  try {
    const response = await axiosInstance.get(`/organizations`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportCompanyCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/organizations/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};