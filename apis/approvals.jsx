import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const listOfApprovals = async (params={}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/report-approvals/`, {params});
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveApprovals = async (id) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/report-approvals/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const retrieveExpenseForApproval = async (id) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/report-approvals/expenses/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const listOfApprovalsExpenses = async (params = {}) => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/report-approvals-expenses`, {
      params
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const exportApprovalCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/approvals/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};
