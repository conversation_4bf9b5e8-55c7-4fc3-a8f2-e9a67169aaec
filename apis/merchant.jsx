import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name=shortName

export const createMerchant = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/merchants`,payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteMerchant = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/merchants/${id}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateMerchant = async ({id,...payload}) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/merchants/${id}`,payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const listOfMerchants = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/merchants`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};