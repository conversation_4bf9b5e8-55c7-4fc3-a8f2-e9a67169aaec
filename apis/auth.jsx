import { axiosInstance, handleError } from "./config";

// API request functions
export const Registration = async (payload) => {
  try {
    // Determine which step we're on based on payload content
    const isStep2 = payload.hasOwnProperty('password') && payload.hasOwnProperty('otp');
    const endpoint = `/auth/sign-up?step=${isStep2 ? 2 : 1}`;
    
    const response = await axiosInstance.post(endpoint, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const ResendVerificationCodeApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/auth/verify-email', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getOnboardingApi = async () => {
  try {
    const response = await axiosInstance.get('/auth/onboarding');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const OnboardingApi = async (payload) => {
  try {
    const response = await axiosInstance.patch('/auth/onboarding', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const ForgotPasswordApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/auth/reset-password', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const CreatenewPasswordApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/auth/create-password', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }  
};

export const LoginApi = async (payload) => {
  try {
    const response = await axiosInstance.post('/auth/sign-in?app_name=expense', payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const LogoutAllApi = async () => {
  try {
    const response = await axiosInstance.post('/auth/sign-out-all');
    return response.data;
  } catch (error) {
    handleError(error);
  }
};


// invitation

export const ValidateInvitationApi = async (token) => {
  try {
    const response = await axiosInstance.get(`/auth/invitation/${token}`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const InvitationResponseApi = async ({ token, payload }) => {
  try {
    const response = await axiosInstance.patch(`/auth/invitation/${token}`, payload);
    return response.data;
  } catch (error) {
    throw handleError(error);
  }
};