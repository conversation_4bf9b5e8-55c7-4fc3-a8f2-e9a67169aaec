import { axiosInstance, handleError, shortName } from "./config";

// API request functions
const short_name = shortName

export const changePasswordApi = async (payload) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/auth/change-password`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const loginActivity = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/auth/profile/login-activities`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getProfileApi = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/auth/profile`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateProfileApi = async (payload) => {
  try {
    // Check if payload is FormData (for profile picture upload)
    const isFormData = payload instanceof FormData;
    
    const response = await axiosInstance.patch(`/expense-app/auth/profile`, payload, {
      headers: isFormData ? 
        { 'Content-Type': 'multipart/form-data' } : 
        { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteProfile = async (payload) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/auth/profile/delete`, payload);
    return response.data; 
  } catch (error) {
    handleError(error);
  }
};

export const transferOwnership = async (payload) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/auth/profile/transfer`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

// travel api

export const getTravelDocumentApi = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/travel-documents`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const createTravelDocument = async (payload) => {
  const isFormData = payload instanceof FormData;
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/travel-documents`, payload,  {
      headers: isFormData ? 
        { 'Content-Type': 'multipart/form-data' } : 
        { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateTravelDocument = async (id, payload) => {
  const isFormData = payload instanceof FormData;
  try {
    const response = await axiosInstance.patch(`/expense-app/${short_name}/travel-documents/${id}`, payload,  {
      headers: isFormData ? 
        { 'Content-Type': 'multipart/form-data' } : 
        { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const deleteTravelInfo = async ({id}) => {
  try {
    const response = await axiosInstance.delete(`/expense-app/${short_name}/travel-documents/${id}`);
    return response.data; 
  } catch (error) {
    handleError(error);
  }
};

export const exportTravelCSVApi = async (payload) => {
  try {
    const response = await axiosInstance.post(`/expense-app/${short_name}/travel-documents/export`, payload, {
      responseType: 'blob', // Set response type to blob for file downloads
    });
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

// delegate api

export const assignDelegateApi = async (payload) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/auth/profile/delegate`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const getDelegatePermissions = async () => {
  try {
    const response = await axiosInstance.get(`/expense-app/${short_name}/expense-delegation-permissions`);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};

export const updateNotificationPreferenceApi = async (payload) => {
  try {
    const response = await axiosInstance.patch(`/expense-app/auth/profile/notification-preference`, payload);
    return response.data;
  } catch (error) {
    handleError(error);
  }
};