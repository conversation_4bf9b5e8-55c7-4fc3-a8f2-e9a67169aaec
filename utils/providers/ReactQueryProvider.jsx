"use client"

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import React, { useState } from 'react'
import { Toaster } from 'react-hot-toast'

const ReactQueryProvider = ({ children }) => {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
      },
    },
  }))

  return (
    <QueryClientProvider client={queryClient}>
      <Toaster position="top-right" containerClassName="text-sm" toastOptions={{duration: 7000}}  />
      {children}
    </QueryClientProvider>
  )
}

export default ReactQueryProvider;