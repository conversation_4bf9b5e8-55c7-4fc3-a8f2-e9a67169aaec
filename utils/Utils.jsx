import { format, parseISO } from "date-fns";

export const formatDate = (dateString) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM dd, yyyy HH:mm a');
  } catch (error) {
    return <div className="w-full flex items-center justify-center p-0 overflow-hidden text-xl">
      --
    </div>;
  }
};

export const formatExpenseDate = (dateString) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM dd, yyyy');
  } catch (error) {
    return <div className="w-full flex items-center justify-center p-0 overflow-hidden text-xl">
      --
    </div>;
  }
};
