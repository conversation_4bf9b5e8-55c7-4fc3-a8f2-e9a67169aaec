import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { UploadIcon, Trash2 } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const FileUpload = ({
    layout = 'vertical',
    uploadMode = 'single',
    defaultText = 'Select or drag and drop your files here',
    otherText = '(PDF, DOC, DOCX, Images up to 20MB)',
    maxSize = 20 * 1024 * 1024, // 20MB
    acceptedFileTypes = {
        // 'application/pdf': ['.pdf'],
        // 'application/msword': ['.doc'],
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
        'image/*': ['.png', '.jpg', '.jpeg'] // Added image types
    },
    onFilesUploaded = () => {}, // Make the callback optional with a default empty function
    zodSchema,
    errors: externalErrors,
    initialFiles = []
}) => {
    const [files, setFiles] = useState([]);
    const [internalErrors, setInternalErrors] = useState(null);

    // Handle initial files
    useEffect(() => {
        if (initialFiles?.length > 0) {
            setFiles(initialFiles);
        }
    }, [initialFiles]);

    const validateFile = (file) => {
        if (!file) {
            return "No file selected";
        }

        if (zodSchema) {
            try {
                zodSchema.parse({ file: file });
                return null;
            } catch (error) {
                console.log("Validation error:", error);
                return error.errors[0]?.message || "Invalid file";
            }
        }

        return null;
    };

    const onDrop = useCallback((acceptedFiles) => {
        if (acceptedFiles.length === 0) {
            setInternalErrors("No valid files were dropped");
            return;
        }

        const newFiles = acceptedFiles.map(file => {
            // Create preview URLs for images
            const isImage = file.type.startsWith('image/');
            return Object.assign(file, {
                preview: isImage ? URL.createObjectURL(file) : null
            });
        });

        let validationError = null;
        if (uploadMode === 'single') {
            validationError = validateFile(newFiles[0]);

            if (!validationError) {
                // Revoke old preview URL if it exists
                if (files.length > 0 && files[0].preview) {
                    URL.revokeObjectURL(files[0].preview);
                }
                setFiles(newFiles.slice(0, 1));
                onFilesUploaded?.(newFiles[0]); // Make callback optional with ?. operator
                setInternalErrors(null);
            } else {
                setInternalErrors(validationError);
            }
        } else {
            const errors = newFiles.map(validateFile).filter(Boolean);

            if (errors.length === 0) {
                setFiles(prev => [...prev, ...newFiles]);
                onFilesUploaded?.(newFiles); // Make callback optional with ?. operator
                setInternalErrors(null);
            } else {
                setInternalErrors(errors[0]);
            }
        }
    }, [uploadMode, onFilesUploaded, zodSchema, files]);

    // Cleanup preview URLs when component unmounts
    useEffect(() => {
        return () => {
            files.forEach(file => {
                if (file.preview) {
                    URL.revokeObjectURL(file.preview);
                }
            });
        };
    }, []);

    const removeFile = (e, file) => {
        e.preventDefault(); // Prevent form submission
        e.stopPropagation(); // Stop event propagation
        
        const newFiles = files.filter(f => f !== file);
        // Revoke preview URL if it exists
        if (file.preview) {
            URL.revokeObjectURL(file.preview);
        }
        setFiles(newFiles);
        onFilesUploaded?.(uploadMode === 'single' ? null : newFiles); // Make callback optional
        setInternalErrors(null);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: acceptedFileTypes,
        maxSize,
        multiple: uploadMode === 'multi'
    });

    const dropzoneClasses = cn(
        "border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors",
        isDragActive ? "border-blue-500 bg-blue-50" : (internalErrors || externalErrors) ? "border-red-500" : "border-gray-300 hover:border-gray-400",
        layout === 'horizontal' ? "flex items-center justify-center space-x-4" : "flex flex-col justify-center items-center space-y-2"
    );

    const renderFileList = () => (
        <div className="space-y-2 mt-4">
            {files.map((file, index) => {
                console.log("Rendering file:", file);
                
                // Determine if we have a valid preview URL
                let previewUrl = null;
                
                // Case 1: File has a preview property that's a string URL
                if (file.preview && typeof file.preview === 'string') {
                    previewUrl = file.preview;
                }
                // Case 2: File has a preview property with a path
                else if (file.preview && typeof file.preview === 'object' && file.preview.path) {
                    previewUrl = file.preview.path;
                }
                // Case 3: File is a standard File object and is an image
                else if (file instanceof File && file.type.startsWith('image/')) {
                    previewUrl = URL.createObjectURL(file);
                }
                
                return (
                    <div key={index} className="flex items-center justify-between p-3 rounded-md border shadow">
                        <div className="flex items-center space-x-2">
                            {previewUrl ? (
                                <img
                                    src={previewUrl}
                                    alt={file.name || "Receipt"}
                                    className="w-12 h-12 object-cover rounded"
                                    onError={(e) => {
                                        console.error("Error loading image:", previewUrl);
                                        e.target.src = "/placeholder-image.png"; // Fallback image
                                        e.target.onerror = null; // Prevent infinite loop
                                    }}
                                />
                            ) : (
                                <div className="w-12 h-12 bg-gray-300 rounded flex items-center justify-center p-5">
                                    <span className="text-xs font-medium">
                                        {file.name ? file.name.split('.').pop().toUpperCase() : "FILE"}
                                    </span>
                                </div>
                            )}

                            <div className='flex flex-col space-y-1'>
                                <p className="text-sm font-medium truncate max-w-xs">
                                    {file.name || (file.isExisting ? "Existing Receipt" : "Receipt")}
                                </p>
                                <p className="text-xs text-gray-500">
                                    {file.size ? `${(file.size / 1024).toFixed(2)} KB` : ""}
                                </p>
                            </div>
                        </div>

                        <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={(e) => removeFile(e, file)}
                            type="button" // Prevent form submission
                        >
                            <Trash2 className="w-4 h-4" />
                        </Button>
                    </div>
                );
            })}
        </div>
    );

    const renderDropzone = () => (
        <>
            <div {...getRootProps({ className: dropzoneClasses })}>
                <input {...getInputProps()} />
                <UploadIcon className="w-8 h-8 text-gray-400" />
                <p className="text-sm text-gray-600">{defaultText}</p>
                <p className="text-xs text-gray-500">{otherText}</p>
            </div>

            {(internalErrors || externalErrors) && (
                <p className="text-xs font-medium text-red-500 mt-2">
                    {internalErrors || (Array.isArray(externalErrors) ? externalErrors.join(', ') : externalErrors)}
                </p>
            )}
        </>
    );

    return (
        <div>
            {(uploadMode === 'multi' || files.length === 0) && renderDropzone()}
            {renderFileList()}
        </div>
    );
};

export default FileUpload;
