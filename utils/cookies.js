import Cookies from 'js-cookie';

const COOKIE_OPTIONS = {
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  path: '/'
};

export const setCookie = (key, value) => {
  Cookies.set(key, JSON.stringify(value), COOKIE_OPTIONS);
};

export const getCookie = (key) => {
  const value = Cookies.get(key);
  return value ? JSON.parse(value) : null;
};

export const removeCookie = (key) => {
  Cookies.remove(key, COOKIE_OPTIONS);
};