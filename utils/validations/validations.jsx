// src/utils/validateForm.js

export const ValidateExpenseForm = (formData, expenseType = 'AllExpense') => {
  let errors = {};
  
  // Common validations for all expense types
  if (!formData.title) errors.title = "Report Name is required.";
  if (!formData.date) errors.date = "Expense Date is required.";
  if (!formData.type) errors.type = "Expense Type is required.";
  if (!formData.amount_currency) errors.amount_currency = "Currency is required.";
  if (!formData.amount) errors.amount = "Amount is required.";
  if (!formData.description) errors.description = "Description is required.";
  
  // Conditional validations based on expense type
  if (expenseType === 'AddMileage') {
    if (!formData.distance) {
      errors.distance = "Distance is required.";
    }
  } else {
    // For AllExpense
    if (!formData.merchant) {
      errors.merchant = "Merchant is required.";
    }
  }
  
  return errors;
};


export const ValidateAdvancesForm = (formData) => {
  let errors = {};
  if (!formData.title) errors.title = "Report Name is required.";
  if (!formData.amount) errors.amount = "Amount is required.";
  if (!formData.amount_currency)
    errors.amount_currency = "Currency is required.";
  if (!formData.date) errors.date = "Expense Date is required.";
  if (!formData.paid_through) errors.paid_through = "Paid through field is required.";
  if (!formData.notes) errors.notes = "Note is required.";
  return errors;
};


export const ValidateReportForm = (formData) => {
  let errors = {};
  if (!formData.name) errors.name = "Report Name is required.";
  if (!formData.type) errors.type = "Report Type is required.";
  if (!formData.date) errors.date = "Report Date is required.";
  if (!formData.policy) errors.policy = "Policy field is required.";
  if (!formData.purpose) errors.purpose = "Purpose field is required.";
  return errors;
};

export const ValidatePasswordForm = (formData) => {
  let errors = {};
  if (!formData.old_password) errors.name = "Old Password is required.";
  if (!formData.new_password) errors.type = "New Password is required.";
  return errors;
};