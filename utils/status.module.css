.statusBadge {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  width: fit-content;
}

/* Default status styles */
.pending {
  background-color: #FFF3CC;
  color: #715600;
  border: 1px solid #FFEBAD;
}

.approved {
  background-color: #DDF9E5;
  color: #065A39;
  border: 1px solid #C7F4D4;
}

.rejected {
  background-color: #FEEFE1;
  color: #763500;
  border: 1px solid #FFE0C8;
}

.draft {
  background-color: #E6EEFF;
  color: #183C82;
  border: 1px solid #D7E5FF;
}

.default {
  background-color: #2F1BAF;
}

/* Custom status variants */
.primary {
  background-color: transparent;
  color: #2F1BAF;
  font-weight: 600;
}

.large {
  font-size: 14px;
  padding: 6px 16px;
}

.bold {
  font-weight: 700;
}