import React from 'react'
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';


const Navigateback = () => {

  const navigate = useRouter()

  const handleNavigateback = (e) => {
    e.preventDefault()
    navigate.back();
  }

  return (
    <>
      <div className='flex items-center justify-start'>
        <div className='flex items-center gap-1 mb-5 text-auth_bg cursor-pointer' onClick={handleNavigateback}>
          <ArrowLeft size={16}/>
          <span className='text-[13px]'>Back</span>
        </div>
      </div>
    </>
  )
}

export default Navigateback;