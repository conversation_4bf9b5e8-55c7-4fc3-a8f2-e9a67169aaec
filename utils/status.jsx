import styles from './status.module.css';

export const GetStatusColor = (status) => {
  switch(status) {
    case 'Pending':
      return styles.pending;
    case 'Approved':
      return styles.approved;
    case 'Rejected':
      return styles.rejected;
    case 'Draft':
      return styles.draft; // Add a new style for "draft"
    default:
      return styles.default;
  }
};

export const StatusBadge = ({ status, isSubmitted }) => {
  if (!status) return null;

  // Override status to "draft" if isSubmitted is false
  const displayStatus = isSubmitted === false ? 'Draft' : status;

  return (
    <div className={`${styles.statusBadge} ${GetStatusColor(displayStatus)}`}>
      {displayStatus}
    </div>
  );
};