import toast from "react-hot-toast";

export const ExportCSV = ({ headers = [], rows = [], filename = "data.csv"}) => {
  try {
    if (!headers.length || !rows.length) {
      toast.error("Headers and rows cannot be empty")
      throw new Error("Headers and rows cannot be empty.");
    }

    // Format CSV content
    const csvContent = [headers, ...rows]
      .map(row => row.map(value => `"${value}"`).join(",")) // Wrap values in quotes
      .join("\n");

    // Create a downloadable blob
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Failed to export CSV:", error.message);
    toast.error(error.message || "An error occurred while exporting the CSV.");
  }
};