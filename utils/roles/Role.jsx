import { useEffect, useState } from "react";

export const Role = () => {
    const [userData, setUserData] = useState(null);
  
    useEffect(() => {
      if (typeof window !== "undefined") {
        const storedData = localStorage.getItem("Eloope_UserData");
        if (storedData) {
          setUserData(JSON.parse(storedData));
        }
      }
    }, []);
  
    const roles = userData?.userData?.profile?.roles || [];
    const isSystemAdmin = userData?.userData?.is_admin || false;

    const isAdministrator = roles?.includes("Administrator");
    const isApproverManager = roles?.includes("Approver/Manager");
    
    return {
        isAdministrator,
        isApproverManager,
        isSystemAdmin
    }
}