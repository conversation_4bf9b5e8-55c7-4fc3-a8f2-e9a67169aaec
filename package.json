{"type": "module", "name": "eloope", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@tanstack/react-query": "^5.59.19", "@tanstack/react-table": "^8.21.2", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^2.29.3", "js-cookie": "^3.0.5", "lucide-react": "^0.454.0", "next": "^14.2.18", "next-nprogress-bar": "^2.3.14", "next-themes": "^0.4.6", "papaparse": "^5.4.1", "quill": "^2.0.3", "react": "^18.2.0", "react-currency-flags": "^0.1.2", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-hot-toast": "^2.4.1", "react-intl-tel-input": "^8.2.0", "react-quill": "^2.0.0", "react-spinners": "^0.14.1", "recharts": "^2.13.3", "shadcn-file-upload": "^1.0.2", "sonner": "^2.0.2", "styled-components": "^6.1.15", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0"}, "devDependencies": {"babel-plugin-styled-components": "^2.1.4", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "tailwindcss": "^3.4.1"}}