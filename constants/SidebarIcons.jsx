import {
  SettingsIcon,
  Download,
  LayoutDashboard,
  ChartNoAxesColumn,
  Database,
  FileSliders,
  Plane,
  Wallet,
  Building2,
} from "lucide-react";

const SidebarIcons = ({ as, size, strokeWidth="10px", color }) => {
  const icons = {
    Dashboard: <LayoutDashboard strokeWidth={strokeWidth} size={size} color={color} />,
    Expenses: <Wallet strokeWidth={strokeWidth} size={size} color={color} />,
    Reports: <ChartNoAxesColumn strokeWidth={strokeWidth} size={size} color={color} />,
    Trips: <Plane strokeWidth={strokeWidth} size={size} color={color} />,
    Advances: <FileSliders strokeWidth={strokeWidth} size={size} color={color} />,
    Approvals: <Download strokeWidth={strokeWidth} size={size} color={color} />,
    Analytics: <Database strokeWidth={strokeWidth} size={size} color={color} />,
    Companies: <Building2 strokeWidth={strokeWidth} size={size} color={color} />,
    Settings: <SettingsIcon strokeWidth={strokeWidth} size={size} color={color} />,
  };

  return icons[as];
};

export default SidebarIcons;
