import { HelpCircle, Settings, UserPlus } from "lucide-react";

export const Sidebar = [
  {
    name: "Dashboard",
    path: "/dashboard",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Expenses",
    path: "/expenses",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Reports",
    path: "/reports",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Trip<PERSON>",
    path: "/trips",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Advances",
    path: "/advances",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Approvals",
    path: "/approvals",
    isExtendedTool: false,
    isBottomItem: false
  },
  {
    name: "Analytics",
    path: "/analytics",
    isExtendedTool: true,
    isBottomItem: false
  },
  {
    name: "Companies",
    path: "/companies",
    isExtendedTool: true,
    isBottomItem: false
  },
  {
    name: "Settings",
    path: "/admin-settings",
    isExtendedTool: false,
    isBottomItem: true,
    icon: <Settings size={18} strokeWidth={1.8} />
  },
  {
    name: "Get Help",
    path: "/help",
    isExtendedTool: false,
    isBottomItem: true,
    icon: <HelpCircle size={18} strokeWidth={1.8} />
  },
  // {
  //   name: "Invite Members",
  //   path: "/invite-user",
  //   isExtendedTool: false,
  //   isBottomItem: true,
  //   icon: <UserPlus size={18} strokeWidth={1.8} />
  // }
];