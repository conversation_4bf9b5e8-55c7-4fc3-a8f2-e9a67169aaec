import { useMutation } from '@tanstack/react-query';
import toast from 'react-hot-toast';

/**
 * Custom hook for handling CSV exports
 * @param {Function} exportFunction - The API function to call for exporting
 * @param {Object} options - Additional options
 * @param {string} options.filename - The filename for the downloaded file (default: 'export.csv')
 * @param {Function} options.onSuccess - Additional callback on successful export
 * @param {Function} options.onError - Additional callback on export error
 * @returns {Object} - Export mutation and handler function
 */
export const useExportCSV = (exportFunction, options = {}) => {
  const {
    filename = 'export.csv',
    onSuccess: onSuccessCallback,
    onError: onErrorCallback,
  } = options;

  // Helper function to download CSV
  const downloadCSV = (blob, name) => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = name;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const { mutateAsync, isLoading } = useMutation({
    mutationFn: exportFunction,
    onSuccess: (response) => {
      try {
        if (response instanceof Blob) {
          // If the API directly returns a Blob
          downloadCSV(response, filename);
          toast.success("Export successful");
        } else if (response?.data) {
          // If API returns data in a data property
          const csvContent = typeof response.data === 'string' 
            ? response.data 
            : JSON.stringify(response.data);
            
          const blob = new Blob([csvContent], { type: 'text/csv' });
          downloadCSV(blob, filename);
          toast.success("Export successful");
        } else if (response?.download_url) {
          // If API returns a download URL
          window.open(response.download_url, '_blank');
          toast.success("Export successful");
        } else {
          // If response format is unexpected
          toast.error("Invalid response format from server");
          console.error("Invalid export response:", response);
        }

        // Call additional success callback if provided
        if (onSuccessCallback) onSuccessCallback(response);
      } catch (err) {
        toast.error("Error processing export data");
        console.error("Export processing error:", err);
        
        // Call additional error callback if provided
        if (onErrorCallback) onErrorCallback(err);
      }
    },
    onError: (error) => {
      toast.error(error?.message || "Failed to export");
      console.error("Export error:", error);
      
      // Call additional error callback if provided
      if (onErrorCallback) onErrorCallback(error);
    }
  });

  const handleExport = (payload = {}) => {
    const toastId = toast.loading("Preparing export...");
    return mutateAsync(payload).finally(() => toast.dismiss(toastId));
  };

  return {
    exportMutation: mutateAsync,
    handleExport,
    isExporting: isLoading
  };
};