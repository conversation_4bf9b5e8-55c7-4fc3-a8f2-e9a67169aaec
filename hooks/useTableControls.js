import { useState, useEffect, useCallback, useRef } from 'react';

export const useTableControls = (options = {}) => {
  const {
    data = [],
    initialFilters = {},
    initialPagination = { pageIndex: 0, pageSize: 10 },
    filterFunction,
  } = options;

  // Use a ref to track if this is the first data load
  const isFirstLoad = useRef(true);
  
  const [rows, setRows] = useState([]);
  const [sortedRows, setSortedRows] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [sortOrder, setSortOrder] = useState("asc");
  const [sortColumn, setSortColumn] = useState("");
  const [pagination, setPagination] = useState(initialPagination);
  const [filterValues, setFilterValues] = useState(initialFilters);

  // Update rows when data changes
  useEffect(() => {
    if (data && data.length) {
      setRows(data);
      
      // Only apply initial filters on first data load
      if (isFirstLoad.current) {
        isFirstLoad.current = false;
        setSortedRows(data);
      } else {
        // For subsequent data changes, maintain current filters
        if (Object.keys(filterValues).some(key => filterValues[key])) {
          if (typeof filterFunction === 'function') {
            const filteredData = filterFunction(data, filterValues);
            setSortedRows(filteredData);
          } else {
            setSortedRows(data);
          }
        } else {
          setSortedRows(data);
        }
      }
    }
  }, [data]);

  // Handle select all rows
  const handleSelectAll = useCallback(() => {
    if (selectAll) {
      setSelectedRows([]);
    } else {
      setSelectedRows(sortedRows.map(row => row.id));
    }
    setSelectAll(!selectAll);
  }, [selectAll, sortedRows]);

  // Handle row selection
  const handleRowSelection = useCallback((id, checked) => {
    if (checked) {
      setSelectedRows(prev => [...prev, id]);
    } else {
      setSelectedRows(prev => prev.filter(rowId => rowId !== id));
    }
  }, []);

  // Handle sorting
  const handleSort = useCallback((column) => {
    const newSortOrder = sortColumn === column.header && sortOrder === "asc" ? "desc" : "asc";
    
    const sorted = [...sortedRows].sort((a, b) => {
      const aValue = column.accessor ? column.accessor(a) : column.cell({ row: { original: a } });
      const bValue = column.accessor ? column.accessor(b) : column.cell({ row: { original: b } });
      
      if (aValue < bValue) return newSortOrder === "asc" ? -1 : 1;
      if (aValue > bValue) return newSortOrder === "asc" ? 1 : -1;
      return 0;
    });
    
    setSortedRows(sorted);
    setSortOrder(newSortOrder);
    setSortColumn(column.header || column.title);
  }, [sortColumn, sortOrder, sortedRows]);

  // Update URL with filter values
  const updateQueryParams = useCallback((filters) => {
    if (typeof window === 'undefined') return;
    
    const searchParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== "" && value !== "all" && key !== "dateRange") {
        searchParams.set(key, value);
      }
    });
    
    // Update URL without page reload
    const newUrl = window.location.pathname + 
      (searchParams.toString() ? `?${searchParams.toString()}` : '');
    
    // Use replaceState to avoid adding to browser history
    window.history.replaceState({ path: newUrl }, '', newUrl);
  }, []);

  // Handle filtering
  const handleFilter = useCallback((filters) => {
    if (!filters || !rows.length) return;
    
    // Update URL parameters
    updateQueryParams(filters);
    
    // Update filter values state
    setFilterValues(filters);
    
    let filteredRows;
    
    if (typeof filterFunction === 'function') {
      // Use custom filter function if provided
      filteredRows = filterFunction(rows, filters);
    } else {
      // Default filtering behavior
      filteredRows = rows;
    }
    
    setSortedRows(filteredRows);
    // Reset to first page when filtering
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [rows, filterFunction, updateQueryParams]);

  // Handle clearing filters
  const handleClearFilters = useCallback(() => {
    // Clear URL parameters
    if (typeof window !== 'undefined') {
      window.history.replaceState({}, '', window.location.pathname);
    }
    
    // Reset filter values
    setFilterValues(initialFilters);
    
    // Reset data to unfiltered state
    setSortedRows(rows);
    
    // Reset pagination
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [rows, initialFilters]);

  // Count active filters
  const getActiveFilterCount = useCallback(() => {
    return Object.entries(filterValues).filter(([key, value]) => {
      return value && value !== "" && value !== "all" && key !== "dateRange";
    }).length;
  }, [filterValues]);

  return {
    rows: sortedRows,
    selectedRows,
    selectAll,
    sortOrder,
    sortColumn,
    pagination,
    filterValues,
    setFilterValues,
    setPagination,
    handleSelectAll,
    handleRowSelection,
    handleSort,
    handleFilter,
    handleClearFilters,
    getActiveFilterCount,
    setSelectedRows,
    setSortedRows
  };
};