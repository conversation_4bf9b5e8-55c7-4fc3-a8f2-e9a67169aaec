import { useState, useEffect, useCallback } from "react";

/**
 * Enhanced hook for table sorting with support for nested properties
 */
const useSort = (initialData = []) => {
  const [sortedData, setSortedData] = useState(initialData);
  const [sortState, setSortState] = useState({
    column: null,
    order: "asc"
  });

  useEffect(() => {
    setSortedData(initialData);
  }, [initialData]);

  const handleSort = useCallback((column) => {
    const newOrder = sortState.column === column.accessorKey && sortState.order === "asc" ? "desc" : "asc";

    const sorted = [...sortedData].sort((a, b) => {
      const getValue = (obj, path) => {
        if (!path) return obj;
        return path.split('.').reduce((o, key) => (o && o[key] !== undefined) ? o[key] : null, obj);
      };

      const aValue = getValue(a, column.accessorKey);
      const bValue = getValue(b, column.accessorKey);

      // Handle null/undefined values
      if (!aValue && !bValue) return 0;
      if (!aValue) return newOrder === "asc" ? -1 : 1;
      if (!bValue) return newOrder === "asc" ? 1 : -1;

      // // Handle different data types
      // if (column.type === "date") {
      //   return newOrder === "asc" 
      //     ? new Date(aValue) - new Date(bValue)
      //     : new Date(bValue) - new Date(aValue);
      // }

      // if (column.type === "number") {
      //   return newOrder === "asc" 
      //     ? Number(aValue) - Number(bValue)
      //     : Number(bValue) - Number(aValue);
      // }

      // Default string comparison
      return newOrder === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });

    setSortedData(sorted);
    setSortState({ column: column.accessorKey, order: newOrder });
  }, [sortedData, sortState]);

  return {
    sortedData,
    sortState,
    handleSort
  };
};

export default useSort;
