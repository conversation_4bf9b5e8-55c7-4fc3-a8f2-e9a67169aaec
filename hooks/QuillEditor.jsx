"use client"

// import ReactQuill from 'react-quill'
import dynamic from'next/dynamic'

const ReactQuill = dynamic(() =>import('react-quill'), { ssr: false })
import 'react-quill/dist/quill.snow.css';



export const QuillEditor = ({value, onChange, className}) => {

  return (
    <div className='quill-container'>
      <ReactQuill theme="snow" placeholder='Description' value={value} className={className} onChange={onChange}  style={{ height: "19vh"}}/>
    </div>
  ) 
}