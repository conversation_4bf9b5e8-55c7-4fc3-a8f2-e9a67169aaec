"use client";

import { useCallback, useEffect, useState } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

/**
 * Custom hook for managing table filters with URL synchronization
 * @param {Object} initialFilters - Initial filter values
 * @param {Object} options - Hook options
 * @param {boolean} options.syncWithUrl - Whether to sync filters with URL
 * @returns {Object} Filter state and handlers
 */
const useTableFilter = (initialFilters = {}, options = {}) => {
  const { syncWithUrl = false } = options;
  const router = useRouter();
  const pathname = usePathname();
  
  // Move searchParams inside a try-catch to handle server/client mismatch
  let searchParams;
  try {
    searchParams = useSearchParams();
  } catch (error) {
    // Fallback if searchParams is not available
    searchParams = new URLSearchParams();
  }
  
  // Initialize filters from URL if syncWithUrl is true
  const [filters, setFilters] = useState(() => {
    if (syncWithUrl && searchParams) {
      const urlFilters = { ...initialFilters };
      
      // Populate filters from URL search params
      Object.keys(initialFilters).forEach(key => {
        const paramValue = searchParams.get(key);
        if (paramValue) {
          urlFilters[key] = paramValue;
        }
      });
      
      // Handle date range separately
      if (searchParams.get('date_from') && searchParams.get('date_to')) {
        urlFilters.dateRange = {
          from: searchParams.get('date_from'),
          to: searchParams.get('date_to')
        };
      }
      
      return urlFilters;
    }
    
    return initialFilters;
  });
  
  // Update URL when filters change
  useEffect(() => {
    if (syncWithUrl && searchParams) {
      const params = new URLSearchParams(searchParams);
      
      // Update URL params based on filter values
      Object.entries(filters).forEach(([key, value]) => {
        if (key === 'dateRange') {
          // Handle date range separately
          if (value?.from && value?.to) {
            params.set('date_from', value.from);
            params.set('date_to', value.to);
          } else {
            params.delete('date_from');
            params.delete('date_to');
          }
        } else if (value && value !== '') {
          params.set(key, value);
        } else {
          params.delete(key);
        }
      });
      
      // Update URL without refreshing the page
      const newUrl = `${pathname}?${params.toString()}`;
      router.push(newUrl, { scroll: false });
    }
  }, [filters, syncWithUrl, pathname, router, searchParams]);
  
  // Handler for applying filters
  const handleFilter = useCallback((newFilters) => {
    setFilters(newFilters);
  }, []);
  
  // Handler for clearing filters
  const clearFilters = useCallback(() => {
    setFilters(initialFilters);
  }, [initialFilters]);
  
  return {
    filters,
    setFilters,
    handleFilter,
    clearFilters
  };
};

export default useTableFilter;
